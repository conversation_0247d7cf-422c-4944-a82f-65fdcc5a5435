ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'Top Plate.step',
/* time_stamp */ '2025-06-01T14:21:01-04:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v20.1',
/* originating_system */ 'Autodesk Translation Framework v14.10.0.0',
/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#20,#21),
#3533);
#11=CONTEXT_DEPENDENT_OVER_RIDING_STYLED_ITEM('',(#3558),#3544,#21,(#15));
#12=MAPPED_ITEM('',#13,#2305);
#13=REPRESENTATION_MAP(#2305,#3543);
#14=ITEM_DEFINED_TRANSFORMATION($,$,#2097,#2304);
#15=(
REPRESENTATION_RELATIONSHIP($,$,#3544,#3543)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#14)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#16=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#15,#3542);
#17=NEXT_ASSEMBLY_USAGE_OCCURRENCE('Top Plate (1):1','Top Plate (1):1',
'Top Plate (1):1',#3546,#3547,'Top Plate (1):1');
#18=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#3544,#19);
#19=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#22),#3532);
#20=STYLED_ITEM('',(#3558),#22);
#21=STYLED_ITEM('',(#3559),#12);
#22=MANIFOLD_SOLID_BREP('Body1',#2083);
#23=FACE_BOUND('',#206,.T.);
#24=FACE_BOUND('',#207,.T.);
#25=FACE_BOUND('',#208,.T.);
#26=FACE_BOUND('',#209,.T.);
#27=FACE_BOUND('',#210,.T.);
#28=FACE_BOUND('',#211,.T.);
#29=FACE_BOUND('',#212,.T.);
#30=FACE_BOUND('',#213,.T.);
#31=FACE_BOUND('',#214,.T.);
#32=FACE_BOUND('',#215,.T.);
#33=FACE_BOUND('',#216,.T.);
#34=FACE_BOUND('',#217,.T.);
#35=FACE_BOUND('',#218,.T.);
#36=FACE_BOUND('',#219,.T.);
#37=FACE_BOUND('',#228,.T.);
#38=FACE_BOUND('',#229,.T.);
#39=FACE_BOUND('',#230,.T.);
#40=FACE_BOUND('',#231,.T.);
#41=FACE_BOUND('',#232,.T.);
#42=FACE_BOUND('',#233,.T.);
#43=FACE_BOUND('',#234,.T.);
#44=FACE_BOUND('',#235,.T.);
#45=FACE_BOUND('',#236,.T.);
#46=FACE_BOUND('',#237,.T.);
#47=FACE_BOUND('',#238,.T.);
#48=FACE_BOUND('',#239,.T.);
#49=FACE_BOUND('',#240,.T.);
#50=FACE_BOUND('',#241,.T.);
#51=PLANE('',#2101);
#52=PLANE('',#2102);
#53=PLANE('',#2156);
#54=PLANE('',#2159);
#55=PLANE('',#2162);
#56=PLANE('',#2163);
#57=PLANE('',#2164);
#58=PLANE('',#2214);
#59=PLANE('',#2216);
#60=PLANE('',#2218);
#61=PLANE('',#2220);
#62=PLANE('',#2222);
#63=PLANE('',#2224);
#64=PLANE('',#2227);
#65=PLANE('',#2229);
#66=PLANE('',#2231);
#67=PLANE('',#2233);
#68=PLANE('',#2235);
#69=PLANE('',#2237);
#70=PLANE('',#2239);
#71=PLANE('',#2241);
#72=PLANE('',#2243);
#73=PLANE('',#2245);
#74=PLANE('',#2247);
#75=PLANE('',#2249);
#76=PLANE('',#2251);
#77=PLANE('',#2253);
#78=PLANE('',#2255);
#79=PLANE('',#2257);
#80=PLANE('',#2260);
#81=PLANE('',#2262);
#82=PLANE('',#2264);
#83=PLANE('',#2266);
#84=PLANE('',#2269);
#85=PLANE('',#2271);
#86=PLANE('',#2273);
#87=PLANE('',#2275);
#88=PLANE('',#2277);
#89=PLANE('',#2279);
#90=PLANE('',#2281);
#91=PLANE('',#2283);
#92=PLANE('',#2287);
#93=PLANE('',#2289);
#94=PLANE('',#2291);
#95=PLANE('',#2293);
#96=PLANE('',#2295);
#97=PLANE('',#2297);
#98=PLANE('',#2299);
#99=PLANE('',#2301);
#100=PLANE('',#2303);
#101=FACE_OUTER_BOUND('',#203,.T.);
#102=FACE_OUTER_BOUND('',#204,.T.);
#103=FACE_OUTER_BOUND('',#205,.T.);
#104=FACE_OUTER_BOUND('',#220,.T.);
#105=FACE_OUTER_BOUND('',#221,.T.);
#106=FACE_OUTER_BOUND('',#222,.T.);
#107=FACE_OUTER_BOUND('',#223,.T.);
#108=FACE_OUTER_BOUND('',#224,.T.);
#109=FACE_OUTER_BOUND('',#225,.T.);
#110=FACE_OUTER_BOUND('',#226,.T.);
#111=FACE_OUTER_BOUND('',#227,.T.);
#112=FACE_OUTER_BOUND('',#242,.T.);
#113=FACE_OUTER_BOUND('',#243,.T.);
#114=FACE_OUTER_BOUND('',#244,.T.);
#115=FACE_OUTER_BOUND('',#245,.T.);
#116=FACE_OUTER_BOUND('',#246,.T.);
#117=FACE_OUTER_BOUND('',#247,.T.);
#118=FACE_OUTER_BOUND('',#248,.T.);
#119=FACE_OUTER_BOUND('',#249,.T.);
#120=FACE_OUTER_BOUND('',#250,.T.);
#121=FACE_OUTER_BOUND('',#251,.T.);
#122=FACE_OUTER_BOUND('',#252,.T.);
#123=FACE_OUTER_BOUND('',#253,.T.);
#124=FACE_OUTER_BOUND('',#254,.T.);
#125=FACE_OUTER_BOUND('',#255,.T.);
#126=FACE_OUTER_BOUND('',#256,.T.);
#127=FACE_OUTER_BOUND('',#257,.T.);
#128=FACE_OUTER_BOUND('',#258,.T.);
#129=FACE_OUTER_BOUND('',#259,.T.);
#130=FACE_OUTER_BOUND('',#260,.T.);
#131=FACE_OUTER_BOUND('',#261,.T.);
#132=FACE_OUTER_BOUND('',#262,.T.);
#133=FACE_OUTER_BOUND('',#263,.T.);
#134=FACE_OUTER_BOUND('',#264,.T.);
#135=FACE_OUTER_BOUND('',#265,.T.);
#136=FACE_OUTER_BOUND('',#266,.T.);
#137=FACE_OUTER_BOUND('',#267,.T.);
#138=FACE_OUTER_BOUND('',#268,.T.);
#139=FACE_OUTER_BOUND('',#269,.T.);
#140=FACE_OUTER_BOUND('',#270,.T.);
#141=FACE_OUTER_BOUND('',#271,.T.);
#142=FACE_OUTER_BOUND('',#272,.T.);
#143=FACE_OUTER_BOUND('',#273,.T.);
#144=FACE_OUTER_BOUND('',#274,.T.);
#145=FACE_OUTER_BOUND('',#275,.T.);
#146=FACE_OUTER_BOUND('',#276,.T.);
#147=FACE_OUTER_BOUND('',#277,.T.);
#148=FACE_OUTER_BOUND('',#278,.T.);
#149=FACE_OUTER_BOUND('',#279,.T.);
#150=FACE_OUTER_BOUND('',#280,.T.);
#151=FACE_OUTER_BOUND('',#281,.T.);
#152=FACE_OUTER_BOUND('',#282,.T.);
#153=FACE_OUTER_BOUND('',#283,.T.);
#154=FACE_OUTER_BOUND('',#284,.T.);
#155=FACE_OUTER_BOUND('',#285,.T.);
#156=FACE_OUTER_BOUND('',#286,.T.);
#157=FACE_OUTER_BOUND('',#287,.T.);
#158=FACE_OUTER_BOUND('',#288,.T.);
#159=FACE_OUTER_BOUND('',#289,.T.);
#160=FACE_OUTER_BOUND('',#290,.T.);
#161=FACE_OUTER_BOUND('',#291,.T.);
#162=FACE_OUTER_BOUND('',#292,.T.);
#163=FACE_OUTER_BOUND('',#293,.T.);
#164=FACE_OUTER_BOUND('',#294,.T.);
#165=FACE_OUTER_BOUND('',#295,.T.);
#166=FACE_OUTER_BOUND('',#296,.T.);
#167=FACE_OUTER_BOUND('',#297,.T.);
#168=FACE_OUTER_BOUND('',#298,.T.);
#169=FACE_OUTER_BOUND('',#299,.T.);
#170=FACE_OUTER_BOUND('',#300,.T.);
#171=FACE_OUTER_BOUND('',#301,.T.);
#172=FACE_OUTER_BOUND('',#302,.T.);
#173=FACE_OUTER_BOUND('',#303,.T.);
#174=FACE_OUTER_BOUND('',#304,.T.);
#175=FACE_OUTER_BOUND('',#305,.T.);
#176=FACE_OUTER_BOUND('',#306,.T.);
#177=FACE_OUTER_BOUND('',#307,.T.);
#178=FACE_OUTER_BOUND('',#308,.T.);
#179=FACE_OUTER_BOUND('',#309,.T.);
#180=FACE_OUTER_BOUND('',#310,.T.);
#181=FACE_OUTER_BOUND('',#311,.T.);
#182=FACE_OUTER_BOUND('',#312,.T.);
#183=FACE_OUTER_BOUND('',#313,.T.);
#184=FACE_OUTER_BOUND('',#314,.T.);
#185=FACE_OUTER_BOUND('',#315,.T.);
#186=FACE_OUTER_BOUND('',#316,.T.);
#187=FACE_OUTER_BOUND('',#317,.T.);
#188=FACE_OUTER_BOUND('',#318,.T.);
#189=FACE_OUTER_BOUND('',#319,.T.);
#190=FACE_OUTER_BOUND('',#320,.T.);
#191=FACE_OUTER_BOUND('',#321,.T.);
#192=FACE_OUTER_BOUND('',#322,.T.);
#193=FACE_OUTER_BOUND('',#323,.T.);
#194=FACE_OUTER_BOUND('',#324,.T.);
#195=FACE_OUTER_BOUND('',#325,.T.);
#196=FACE_OUTER_BOUND('',#326,.T.);
#197=FACE_OUTER_BOUND('',#327,.T.);
#198=FACE_OUTER_BOUND('',#328,.T.);
#199=FACE_OUTER_BOUND('',#329,.T.);
#200=FACE_OUTER_BOUND('',#330,.T.);
#201=FACE_OUTER_BOUND('',#331,.T.);
#202=FACE_OUTER_BOUND('',#332,.T.);
#203=EDGE_LOOP('',(#1329,#1330,#1331,#1332));
#204=EDGE_LOOP('',(#1333,#1334,#1335,#1336));
#205=EDGE_LOOP('',(#1337,#1338,#1339,#1340,#1341,#1342,#1343,#1344,#1345,
#1346,#1347,#1348,#1349,#1350,#1351,#1352));
#206=EDGE_LOOP('',(#1353,#1354,#1355,#1356,#1357,#1358,#1359,#1360));
#207=EDGE_LOOP('',(#1361,#1362,#1363,#1364,#1365,#1366,#1367,#1368));
#208=EDGE_LOOP('',(#1369,#1370,#1371,#1372,#1373,#1374,#1375,#1376));
#209=EDGE_LOOP('',(#1377));
#210=EDGE_LOOP('',(#1378));
#211=EDGE_LOOP('',(#1379,#1380,#1381,#1382,#1383,#1384,#1385,#1386));
#212=EDGE_LOOP('',(#1387,#1388,#1389,#1390,#1391,#1392,#1393,#1394));
#213=EDGE_LOOP('',(#1395));
#214=EDGE_LOOP('',(#1396,#1397,#1398,#1399,#1400,#1401,#1402,#1403));
#215=EDGE_LOOP('',(#1404));
#216=EDGE_LOOP('',(#1405,#1406,#1407,#1408,#1409,#1410,#1411,#1412));
#217=EDGE_LOOP('',(#1413,#1414,#1415,#1416,#1417,#1418,#1419,#1420));
#218=EDGE_LOOP('',(#1421,#1422,#1423,#1424,#1425,#1426,#1427,#1428));
#219=EDGE_LOOP('',(#1429,#1430,#1431,#1432,#1433,#1434,#1435,#1436));
#220=EDGE_LOOP('',(#1437,#1438,#1439,#1440));
#221=EDGE_LOOP('',(#1441,#1442,#1443,#1444));
#222=EDGE_LOOP('',(#1445,#1446,#1447,#1448));
#223=EDGE_LOOP('',(#1449,#1450,#1451,#1452));
#224=EDGE_LOOP('',(#1453,#1454,#1455,#1456));
#225=EDGE_LOOP('',(#1457,#1458,#1459,#1460));
#226=EDGE_LOOP('',(#1461,#1462,#1463,#1464));
#227=EDGE_LOOP('',(#1465,#1466,#1467,#1468,#1469,#1470,#1471,#1472,#1473,
#1474,#1475,#1476,#1477,#1478,#1479,#1480));
#228=EDGE_LOOP('',(#1481,#1482,#1483,#1484,#1485,#1486,#1487,#1488));
#229=EDGE_LOOP('',(#1489,#1490,#1491,#1492,#1493,#1494,#1495,#1496));
#230=EDGE_LOOP('',(#1497,#1498,#1499,#1500,#1501,#1502,#1503,#1504));
#231=EDGE_LOOP('',(#1505,#1506,#1507,#1508,#1509,#1510,#1511,#1512));
#232=EDGE_LOOP('',(#1513));
#233=EDGE_LOOP('',(#1514,#1515,#1516,#1517,#1518,#1519,#1520,#1521));
#234=EDGE_LOOP('',(#1522));
#235=EDGE_LOOP('',(#1523,#1524,#1525,#1526,#1527,#1528,#1529,#1530));
#236=EDGE_LOOP('',(#1531,#1532,#1533,#1534,#1535,#1536,#1537,#1538));
#237=EDGE_LOOP('',(#1539));
#238=EDGE_LOOP('',(#1540));
#239=EDGE_LOOP('',(#1541,#1542,#1543,#1544,#1545,#1546,#1547,#1548));
#240=EDGE_LOOP('',(#1549,#1550,#1551,#1552,#1553,#1554,#1555,#1556));
#241=EDGE_LOOP('',(#1557,#1558,#1559,#1560,#1561,#1562,#1563,#1564));
#242=EDGE_LOOP('',(#1565,#1566,#1567,#1568));
#243=EDGE_LOOP('',(#1569,#1570,#1571,#1572));
#244=EDGE_LOOP('',(#1573,#1574,#1575,#1576));
#245=EDGE_LOOP('',(#1577,#1578,#1579,#1580));
#246=EDGE_LOOP('',(#1581,#1582,#1583,#1584));
#247=EDGE_LOOP('',(#1585,#1586,#1587,#1588));
#248=EDGE_LOOP('',(#1589,#1590,#1591,#1592));
#249=EDGE_LOOP('',(#1593,#1594,#1595,#1596));
#250=EDGE_LOOP('',(#1597,#1598,#1599,#1600));
#251=EDGE_LOOP('',(#1601,#1602,#1603,#1604));
#252=EDGE_LOOP('',(#1605,#1606,#1607,#1608));
#253=EDGE_LOOP('',(#1609,#1610,#1611,#1612));
#254=EDGE_LOOP('',(#1613,#1614,#1615,#1616));
#255=EDGE_LOOP('',(#1617,#1618,#1619,#1620));
#256=EDGE_LOOP('',(#1621,#1622,#1623,#1624));
#257=EDGE_LOOP('',(#1625,#1626,#1627,#1628));
#258=EDGE_LOOP('',(#1629,#1630,#1631,#1632));
#259=EDGE_LOOP('',(#1633,#1634,#1635,#1636));
#260=EDGE_LOOP('',(#1637,#1638,#1639,#1640));
#261=EDGE_LOOP('',(#1641,#1642,#1643,#1644));
#262=EDGE_LOOP('',(#1645,#1646,#1647,#1648));
#263=EDGE_LOOP('',(#1649,#1650,#1651,#1652));
#264=EDGE_LOOP('',(#1653,#1654,#1655,#1656));
#265=EDGE_LOOP('',(#1657,#1658,#1659,#1660));
#266=EDGE_LOOP('',(#1661,#1662,#1663,#1664));
#267=EDGE_LOOP('',(#1665,#1666,#1667,#1668));
#268=EDGE_LOOP('',(#1669,#1670,#1671,#1672));
#269=EDGE_LOOP('',(#1673,#1674,#1675,#1676));
#270=EDGE_LOOP('',(#1677,#1678,#1679,#1680));
#271=EDGE_LOOP('',(#1681,#1682,#1683,#1684));
#272=EDGE_LOOP('',(#1685,#1686,#1687,#1688));
#273=EDGE_LOOP('',(#1689,#1690,#1691,#1692));
#274=EDGE_LOOP('',(#1693,#1694,#1695,#1696));
#275=EDGE_LOOP('',(#1697,#1698,#1699,#1700));
#276=EDGE_LOOP('',(#1701,#1702,#1703,#1704));
#277=EDGE_LOOP('',(#1705,#1706,#1707,#1708));
#278=EDGE_LOOP('',(#1709,#1710,#1711,#1712));
#279=EDGE_LOOP('',(#1713,#1714,#1715,#1716));
#280=EDGE_LOOP('',(#1717,#1718,#1719,#1720));
#281=EDGE_LOOP('',(#1721,#1722,#1723,#1724));
#282=EDGE_LOOP('',(#1725,#1726,#1727,#1728));
#283=EDGE_LOOP('',(#1729,#1730,#1731,#1732));
#284=EDGE_LOOP('',(#1733,#1734,#1735,#1736));
#285=EDGE_LOOP('',(#1737,#1738,#1739,#1740));
#286=EDGE_LOOP('',(#1741,#1742,#1743,#1744));
#287=EDGE_LOOP('',(#1745,#1746,#1747,#1748));
#288=EDGE_LOOP('',(#1749,#1750,#1751,#1752));
#289=EDGE_LOOP('',(#1753,#1754,#1755,#1756));
#290=EDGE_LOOP('',(#1757,#1758,#1759,#1760));
#291=EDGE_LOOP('',(#1761,#1762,#1763,#1764));
#292=EDGE_LOOP('',(#1765,#1766,#1767,#1768));
#293=EDGE_LOOP('',(#1769,#1770,#1771,#1772));
#294=EDGE_LOOP('',(#1773,#1774,#1775,#1776));
#295=EDGE_LOOP('',(#1777,#1778,#1779,#1780));
#296=EDGE_LOOP('',(#1781,#1782,#1783,#1784));
#297=EDGE_LOOP('',(#1785,#1786,#1787,#1788));
#298=EDGE_LOOP('',(#1789,#1790,#1791,#1792));
#299=EDGE_LOOP('',(#1793,#1794,#1795,#1796));
#300=EDGE_LOOP('',(#1797,#1798,#1799,#1800));
#301=EDGE_LOOP('',(#1801,#1802,#1803,#1804));
#302=EDGE_LOOP('',(#1805,#1806,#1807,#1808));
#303=EDGE_LOOP('',(#1809,#1810,#1811,#1812));
#304=EDGE_LOOP('',(#1813,#1814,#1815,#1816));
#305=EDGE_LOOP('',(#1817,#1818,#1819,#1820));
#306=EDGE_LOOP('',(#1821,#1822,#1823,#1824));
#307=EDGE_LOOP('',(#1825,#1826,#1827,#1828));
#308=EDGE_LOOP('',(#1829,#1830,#1831,#1832));
#309=EDGE_LOOP('',(#1833,#1834,#1835,#1836));
#310=EDGE_LOOP('',(#1837,#1838,#1839,#1840));
#311=EDGE_LOOP('',(#1841,#1842,#1843,#1844));
#312=EDGE_LOOP('',(#1845,#1846,#1847,#1848));
#313=EDGE_LOOP('',(#1849,#1850,#1851,#1852));
#314=EDGE_LOOP('',(#1853,#1854,#1855,#1856));
#315=EDGE_LOOP('',(#1857,#1858,#1859,#1860));
#316=EDGE_LOOP('',(#1861,#1862,#1863,#1864));
#317=EDGE_LOOP('',(#1865,#1866,#1867,#1868));
#318=EDGE_LOOP('',(#1869,#1870,#1871,#1872));
#319=EDGE_LOOP('',(#1873,#1874,#1875,#1876));
#320=EDGE_LOOP('',(#1877,#1878,#1879,#1880));
#321=EDGE_LOOP('',(#1881,#1882,#1883,#1884));
#322=EDGE_LOOP('',(#1885,#1886,#1887,#1888));
#323=EDGE_LOOP('',(#1889,#1890,#1891,#1892));
#324=EDGE_LOOP('',(#1893,#1894,#1895,#1896));
#325=EDGE_LOOP('',(#1897,#1898,#1899,#1900));
#326=EDGE_LOOP('',(#1901,#1902,#1903,#1904));
#327=EDGE_LOOP('',(#1905,#1906,#1907,#1908));
#328=EDGE_LOOP('',(#1909,#1910,#1911,#1912));
#329=EDGE_LOOP('',(#1913,#1914,#1915,#1916));
#330=EDGE_LOOP('',(#1917,#1918,#1919,#1920));
#331=EDGE_LOOP('',(#1921,#1922,#1923,#1924));
#332=EDGE_LOOP('',(#1925,#1926,#1927,#1928));
#333=LINE('',#2929,#529);
#334=LINE('',#2932,#530);
#335=LINE('',#2935,#531);
#336=LINE('',#2937,#532);
#337=LINE('',#2938,#533);
#338=LINE('',#2941,#534);
#339=LINE('',#2945,#535);
#340=LINE('',#2949,#536);
#341=LINE('',#2953,#537);
#342=LINE('',#2957,#538);
#343=LINE('',#2961,#539);
#344=LINE('',#2965,#540);
#345=LINE('',#2971,#541);
#346=LINE('',#2975,#542);
#347=LINE('',#2979,#543);
#348=LINE('',#2982,#544);
#349=LINE('',#2987,#545);
#350=LINE('',#2991,#546);
#351=LINE('',#2995,#547);
#352=LINE('',#2998,#548);
#353=LINE('',#3003,#549);
#354=LINE('',#3007,#550);
#355=LINE('',#3011,#551);
#356=LINE('',#3014,#552);
#357=LINE('',#3023,#553);
#358=LINE('',#3027,#554);
#359=LINE('',#3031,#555);
#360=LINE('',#3034,#556);
#361=LINE('',#3039,#557);
#362=LINE('',#3043,#558);
#363=LINE('',#3047,#559);
#364=LINE('',#3050,#560);
#365=LINE('',#3057,#561);
#366=LINE('',#3061,#562);
#367=LINE('',#3065,#563);
#368=LINE('',#3068,#564);
#369=LINE('',#3075,#565);
#370=LINE('',#3079,#566);
#371=LINE('',#3083,#567);
#372=LINE('',#3086,#568);
#373=LINE('',#3091,#569);
#374=LINE('',#3095,#570);
#375=LINE('',#3099,#571);
#376=LINE('',#3102,#572);
#377=LINE('',#3107,#573);
#378=LINE('',#3111,#574);
#379=LINE('',#3115,#575);
#380=LINE('',#3118,#576);
#381=LINE('',#3123,#577);
#382=LINE('',#3127,#578);
#383=LINE('',#3131,#579);
#384=LINE('',#3134,#580);
#385=LINE('',#3139,#581);
#386=LINE('',#3140,#582);
#387=LINE('',#3143,#583);
#388=LINE('',#3144,#584);
#389=LINE('',#3149,#585);
#390=LINE('',#3150,#586);
#391=LINE('',#3153,#587);
#392=LINE('',#3154,#588);
#393=LINE('',#3158,#589);
#394=LINE('',#3160,#590);
#395=LINE('',#3162,#591);
#396=LINE('',#3167,#592);
#397=LINE('',#3171,#593);
#398=LINE('',#3175,#594);
#399=LINE('',#3181,#595);
#400=LINE('',#3185,#596);
#401=LINE('',#3189,#597);
#402=LINE('',#3192,#598);
#403=LINE('',#3197,#599);
#404=LINE('',#3201,#600);
#405=LINE('',#3205,#601);
#406=LINE('',#3208,#602);
#407=LINE('',#3213,#603);
#408=LINE('',#3217,#604);
#409=LINE('',#3221,#605);
#410=LINE('',#3224,#606);
#411=LINE('',#3229,#607);
#412=LINE('',#3233,#608);
#413=LINE('',#3237,#609);
#414=LINE('',#3240,#610);
#415=LINE('',#3247,#611);
#416=LINE('',#3251,#612);
#417=LINE('',#3255,#613);
#418=LINE('',#3258,#614);
#419=LINE('',#3265,#615);
#420=LINE('',#3269,#616);
#421=LINE('',#3273,#617);
#422=LINE('',#3276,#618);
#423=LINE('',#3281,#619);
#424=LINE('',#3285,#620);
#425=LINE('',#3289,#621);
#426=LINE('',#3292,#622);
#427=LINE('',#3299,#623);
#428=LINE('',#3303,#624);
#429=LINE('',#3307,#625);
#430=LINE('',#3311,#626);
#431=LINE('',#3317,#627);
#432=LINE('',#3321,#628);
#433=LINE('',#3325,#629);
#434=LINE('',#3328,#630);
#435=LINE('',#3333,#631);
#436=LINE('',#3337,#632);
#437=LINE('',#3341,#633);
#438=LINE('',#3344,#634);
#439=LINE('',#3346,#635);
#440=LINE('',#3347,#636);
#441=LINE('',#3349,#637);
#442=LINE('',#3351,#638);
#443=LINE('',#3352,#639);
#444=LINE('',#3355,#640);
#445=LINE('',#3356,#641);
#446=LINE('',#3359,#642);
#447=LINE('',#3362,#643);
#448=LINE('',#3364,#644);
#449=LINE('',#3366,#645);
#450=LINE('',#3368,#646);
#451=LINE('',#3370,#647);
#452=LINE('',#3372,#648);
#453=LINE('',#3374,#649);
#454=LINE('',#3375,#650);
#455=LINE('',#3377,#651);
#456=LINE('',#3379,#652);
#457=LINE('',#3381,#653);
#458=LINE('',#3383,#654);
#459=LINE('',#3385,#655);
#460=LINE('',#3387,#656);
#461=LINE('',#3390,#657);
#462=LINE('',#3391,#658);
#463=LINE('',#3393,#659);
#464=LINE('',#3395,#660);
#465=LINE('',#3397,#661);
#466=LINE('',#3399,#662);
#467=LINE('',#3401,#663);
#468=LINE('',#3403,#664);
#469=LINE('',#3406,#665);
#470=LINE('',#3407,#666);
#471=LINE('',#3409,#667);
#472=LINE('',#3411,#668);
#473=LINE('',#3413,#669);
#474=LINE('',#3415,#670);
#475=LINE('',#3417,#671);
#476=LINE('',#3419,#672);
#477=LINE('',#3422,#673);
#478=LINE('',#3423,#674);
#479=LINE('',#3425,#675);
#480=LINE('',#3427,#676);
#481=LINE('',#3429,#677);
#482=LINE('',#3431,#678);
#483=LINE('',#3433,#679);
#484=LINE('',#3435,#680);
#485=LINE('',#3438,#681);
#486=LINE('',#3440,#682);
#487=LINE('',#3441,#683);
#488=LINE('',#3443,#684);
#489=LINE('',#3445,#685);
#490=LINE('',#3447,#686);
#491=LINE('',#3449,#687);
#492=LINE('',#3451,#688);
#493=LINE('',#3453,#689);
#494=LINE('',#3456,#690);
#495=LINE('',#3458,#691);
#496=LINE('',#3459,#692);
#497=LINE('',#3461,#693);
#498=LINE('',#3463,#694);
#499=LINE('',#3465,#695);
#500=LINE('',#3467,#696);
#501=LINE('',#3469,#697);
#502=LINE('',#3471,#698);
#503=LINE('',#3474,#699);
#504=LINE('',#3475,#700);
#505=LINE('',#3477,#701);
#506=LINE('',#3479,#702);
#507=LINE('',#3481,#703);
#508=LINE('',#3483,#704);
#509=LINE('',#3485,#705);
#510=LINE('',#3487,#706);
#511=LINE('',#3490,#707);
#512=LINE('',#3492,#708);
#513=LINE('',#3494,#709);
#514=LINE('',#3495,#710);
#515=LINE('',#3497,#711);
#516=LINE('',#3499,#712);
#517=LINE('',#3501,#713);
#518=LINE('',#3503,#714);
#519=LINE('',#3505,#715);
#520=LINE('',#3507,#716);
#521=LINE('',#3510,#717);
#522=LINE('',#3511,#718);
#523=LINE('',#3513,#719);
#524=LINE('',#3515,#720);
#525=LINE('',#3517,#721);
#526=LINE('',#3519,#722);
#527=LINE('',#3521,#723);
#528=LINE('',#3523,#724);
#529=VECTOR('',#2314,10.);
#530=VECTOR('',#2317,10.);
#531=VECTOR('',#2320,10.);
#532=VECTOR('',#2321,10.);
#533=VECTOR('',#2322,10.);
#534=VECTOR('',#2325,10.);
#535=VECTOR('',#2328,10.);
#536=VECTOR('',#2331,10.);
#537=VECTOR('',#2334,10.);
#538=VECTOR('',#2337,10.);
#539=VECTOR('',#2340,10.);
#540=VECTOR('',#2343,10.);
#541=VECTOR('',#2348,10.);
#542=VECTOR('',#2351,10.);
#543=VECTOR('',#2354,10.);
#544=VECTOR('',#2357,10.);
#545=VECTOR('',#2360,10.);
#546=VECTOR('',#2363,10.);
#547=VECTOR('',#2366,10.);
#548=VECTOR('',#2369,10.);
#549=VECTOR('',#2372,10.);
#550=VECTOR('',#2375,10.);
#551=VECTOR('',#2378,10.);
#552=VECTOR('',#2381,10.);
#553=VECTOR('',#2388,10.);
#554=VECTOR('',#2391,10.);
#555=VECTOR('',#2394,10.);
#556=VECTOR('',#2397,10.);
#557=VECTOR('',#2400,10.);
#558=VECTOR('',#2403,10.);
#559=VECTOR('',#2406,10.);
#560=VECTOR('',#2409,10.);
#561=VECTOR('',#2414,10.);
#562=VECTOR('',#2417,10.);
#563=VECTOR('',#2420,10.);
#564=VECTOR('',#2423,10.);
#565=VECTOR('',#2428,10.);
#566=VECTOR('',#2431,10.);
#567=VECTOR('',#2434,10.);
#568=VECTOR('',#2437,10.);
#569=VECTOR('',#2440,10.);
#570=VECTOR('',#2443,10.);
#571=VECTOR('',#2446,10.);
#572=VECTOR('',#2449,10.);
#573=VECTOR('',#2452,10.);
#574=VECTOR('',#2455,10.);
#575=VECTOR('',#2458,10.);
#576=VECTOR('',#2461,10.);
#577=VECTOR('',#2464,10.);
#578=VECTOR('',#2467,10.);
#579=VECTOR('',#2470,10.);
#580=VECTOR('',#2473,10.);
#581=VECTOR('',#2478,10.);
#582=VECTOR('',#2479,10.);
#583=VECTOR('',#2482,10.);
#584=VECTOR('',#2483,10.);
#585=VECTOR('',#2488,10.);
#586=VECTOR('',#2489,10.);
#587=VECTOR('',#2492,10.);
#588=VECTOR('',#2493,10.);
#589=VECTOR('',#2498,10.);
#590=VECTOR('',#2501,10.);
#591=VECTOR('',#2504,10.);
#592=VECTOR('',#2509,10.);
#593=VECTOR('',#2512,10.);
#594=VECTOR('',#2515,10.);
#595=VECTOR('',#2520,10.);
#596=VECTOR('',#2523,10.);
#597=VECTOR('',#2526,10.);
#598=VECTOR('',#2529,10.);
#599=VECTOR('',#2532,10.);
#600=VECTOR('',#2535,10.);
#601=VECTOR('',#2538,10.);
#602=VECTOR('',#2541,10.);
#603=VECTOR('',#2544,10.);
#604=VECTOR('',#2547,10.);
#605=VECTOR('',#2550,10.);
#606=VECTOR('',#2553,10.);
#607=VECTOR('',#2556,10.);
#608=VECTOR('',#2559,10.);
#609=VECTOR('',#2562,10.);
#610=VECTOR('',#2565,10.);
#611=VECTOR('',#2570,10.);
#612=VECTOR('',#2573,10.);
#613=VECTOR('',#2576,10.);
#614=VECTOR('',#2579,10.);
#615=VECTOR('',#2584,10.);
#616=VECTOR('',#2587,10.);
#617=VECTOR('',#2590,10.);
#618=VECTOR('',#2593,10.);
#619=VECTOR('',#2596,10.);
#620=VECTOR('',#2599,10.);
#621=VECTOR('',#2602,10.);
#622=VECTOR('',#2605,10.);
#623=VECTOR('',#2610,10.);
#624=VECTOR('',#2613,10.);
#625=VECTOR('',#2616,10.);
#626=VECTOR('',#2619,10.);
#627=VECTOR('',#2624,10.);
#628=VECTOR('',#2627,10.);
#629=VECTOR('',#2630,10.);
#630=VECTOR('',#2633,10.);
#631=VECTOR('',#2636,10.);
#632=VECTOR('',#2639,10.);
#633=VECTOR('',#2642,10.);
#634=VECTOR('',#2645,10.);
#635=VECTOR('',#2648,10.);
#636=VECTOR('',#2649,10.);
#637=VECTOR('',#2652,10.);
#638=VECTOR('',#2655,10.);
#639=VECTOR('',#2656,10.);
#640=VECTOR('',#2661,10.);
#641=VECTOR('',#2662,10.);
#642=VECTOR('',#2667,10.);
#643=VECTOR('',#2672,10.);
#644=VECTOR('',#2675,10.);
#645=VECTOR('',#2678,10.);
#646=VECTOR('',#2681,10.);
#647=VECTOR('',#2684,10.);
#648=VECTOR('',#2687,10.);
#649=VECTOR('',#2690,10.);
#650=VECTOR('',#2691,10.);
#651=VECTOR('',#2694,10.);
#652=VECTOR('',#2697,10.);
#653=VECTOR('',#2700,10.);
#654=VECTOR('',#2703,10.);
#655=VECTOR('',#2706,10.);
#656=VECTOR('',#2709,10.);
#657=VECTOR('',#2714,10.);
#658=VECTOR('',#2715,10.);
#659=VECTOR('',#2718,10.);
#660=VECTOR('',#2721,10.);
#661=VECTOR('',#2724,10.);
#662=VECTOR('',#2727,10.);
#663=VECTOR('',#2730,10.);
#664=VECTOR('',#2733,10.);
#665=VECTOR('',#2738,10.);
#666=VECTOR('',#2739,10.);
#667=VECTOR('',#2742,10.);
#668=VECTOR('',#2745,10.);
#669=VECTOR('',#2748,10.);
#670=VECTOR('',#2751,10.);
#671=VECTOR('',#2754,10.);
#672=VECTOR('',#2757,10.);
#673=VECTOR('',#2762,10.);
#674=VECTOR('',#2763,10.);
#675=VECTOR('',#2766,10.);
#676=VECTOR('',#2769,10.);
#677=VECTOR('',#2772,10.);
#678=VECTOR('',#2775,10.);
#679=VECTOR('',#2778,10.);
#680=VECTOR('',#2781,10.);
#681=VECTOR('',#2786,1.7);
#682=VECTOR('',#2789,10.);
#683=VECTOR('',#2790,10.);
#684=VECTOR('',#2793,10.);
#685=VECTOR('',#2796,10.);
#686=VECTOR('',#2799,10.);
#687=VECTOR('',#2802,10.);
#688=VECTOR('',#2805,10.);
#689=VECTOR('',#2808,10.);
#690=VECTOR('',#2813,1.7);
#691=VECTOR('',#2816,10.);
#692=VECTOR('',#2817,10.);
#693=VECTOR('',#2820,10.);
#694=VECTOR('',#2823,10.);
#695=VECTOR('',#2826,10.);
#696=VECTOR('',#2829,10.);
#697=VECTOR('',#2832,10.);
#698=VECTOR('',#2835,10.);
#699=VECTOR('',#2840,10.);
#700=VECTOR('',#2841,10.);
#701=VECTOR('',#2844,10.);
#702=VECTOR('',#2847,10.);
#703=VECTOR('',#2850,10.);
#704=VECTOR('',#2853,10.);
#705=VECTOR('',#2856,10.);
#706=VECTOR('',#2859,10.);
#707=VECTOR('',#2864,1.7);
#708=VECTOR('',#2867,1.70000000000001);
#709=VECTOR('',#2870,10.);
#710=VECTOR('',#2871,10.);
#711=VECTOR('',#2874,10.);
#712=VECTOR('',#2877,10.);
#713=VECTOR('',#2880,10.);
#714=VECTOR('',#2883,10.);
#715=VECTOR('',#2886,10.);
#716=VECTOR('',#2889,10.);
#717=VECTOR('',#2894,10.);
#718=VECTOR('',#2895,10.);
#719=VECTOR('',#2898,10.);
#720=VECTOR('',#2901,10.);
#721=VECTOR('',#2904,10.);
#722=VECTOR('',#2907,10.);
#723=VECTOR('',#2910,10.);
#724=VECTOR('',#2913,10.);
#725=CIRCLE('',#2099,2.);
#726=CIRCLE('',#2100,2.);
#727=CIRCLE('',#2103,2.);
#728=CIRCLE('',#2104,2.);
#729=CIRCLE('',#2105,2.);
#730=CIRCLE('',#2106,5.);
#731=CIRCLE('',#2107,5.);
#732=CIRCLE('',#2108,5.);
#733=CIRCLE('',#2109,5.);
#734=CIRCLE('',#2110,0.499999999999998);
#735=CIRCLE('',#2111,0.5);
#736=CIRCLE('',#2112,0.499999999999998);
#737=CIRCLE('',#2113,0.499999999999998);
#738=CIRCLE('',#2114,0.5);
#739=CIRCLE('',#2115,0.5);
#740=CIRCLE('',#2116,0.499999999999998);
#741=CIRCLE('',#2117,0.499999999999998);
#742=CIRCLE('',#2118,1.);
#743=CIRCLE('',#2119,1.);
#744=CIRCLE('',#2120,1.);
#745=CIRCLE('',#2121,1.);
#746=CIRCLE('',#2122,1.70000000000001);
#747=CIRCLE('',#2123,1.7);
#748=CIRCLE('',#2124,0.5);
#749=CIRCLE('',#2125,0.499999999999998);
#750=CIRCLE('',#2126,0.499999999999998);
#751=CIRCLE('',#2127,0.500000000000003);
#752=CIRCLE('',#2128,0.499999999999998);
#753=CIRCLE('',#2129,0.499999999999998);
#754=CIRCLE('',#2130,0.500000000000003);
#755=CIRCLE('',#2131,0.500000000000003);
#756=CIRCLE('',#2132,1.7);
#757=CIRCLE('',#2133,0.499999999999998);
#758=CIRCLE('',#2134,0.499999999999998);
#759=CIRCLE('',#2135,0.499999999999998);
#760=CIRCLE('',#2136,0.499999999999998);
#761=CIRCLE('',#2137,1.7);
#762=CIRCLE('',#2138,0.499999999999998);
#763=CIRCLE('',#2139,0.499999999999998);
#764=CIRCLE('',#2140,0.499999999999998);
#765=CIRCLE('',#2141,0.500000000000003);
#766=CIRCLE('',#2142,0.499999999999998);
#767=CIRCLE('',#2143,0.5);
#768=CIRCLE('',#2144,0.500000000000003);
#769=CIRCLE('',#2145,0.499999999999998);
#770=CIRCLE('',#2146,0.5);
#771=CIRCLE('',#2147,0.499999999999998);
#772=CIRCLE('',#2148,0.499999999999998);
#773=CIRCLE('',#2149,0.499999999999998);
#774=CIRCLE('',#2150,0.499999999999998);
#775=CIRCLE('',#2151,0.499999999999998);
#776=CIRCLE('',#2152,0.500000000000003);
#777=CIRCLE('',#2153,0.499999999999998);
#778=CIRCLE('',#2155,2.);
#779=CIRCLE('',#2158,2.);
#780=CIRCLE('',#2161,2.);
#781=CIRCLE('',#2165,5.);
#782=CIRCLE('',#2166,5.);
#783=CIRCLE('',#2167,5.);
#784=CIRCLE('',#2168,5.);
#785=CIRCLE('',#2169,0.499999999999998);
#786=CIRCLE('',#2170,0.499999999999998);
#787=CIRCLE('',#2171,0.500000000000003);
#788=CIRCLE('',#2172,0.499999999999998);
#789=CIRCLE('',#2173,0.5);
#790=CIRCLE('',#2174,0.499999999999998);
#791=CIRCLE('',#2175,0.499999999999998);
#792=CIRCLE('',#2176,0.499999999999998);
#793=CIRCLE('',#2177,0.499999999999998);
#794=CIRCLE('',#2178,0.499999999999998);
#795=CIRCLE('',#2179,0.500000000000003);
#796=CIRCLE('',#2180,0.5);
#797=CIRCLE('',#2181,0.499999999999998);
#798=CIRCLE('',#2182,0.500000000000003);
#799=CIRCLE('',#2183,0.499999999999998);
#800=CIRCLE('',#2184,0.499999999999998);
#801=CIRCLE('',#2185,1.7);
#802=CIRCLE('',#2186,0.499999999999998);
#803=CIRCLE('',#2187,0.499999999999998);
#804=CIRCLE('',#2188,0.499999999999998);
#805=CIRCLE('',#2189,0.499999999999998);
#806=CIRCLE('',#2190,1.7);
#807=CIRCLE('',#2191,0.499999999999998);
#808=CIRCLE('',#2192,0.500000000000003);
#809=CIRCLE('',#2193,0.500000000000003);
#810=CIRCLE('',#2194,0.499999999999998);
#811=CIRCLE('',#2195,0.5);
#812=CIRCLE('',#2196,0.500000000000003);
#813=CIRCLE('',#2197,0.499999999999998);
#814=CIRCLE('',#2198,0.499999999999998);
#815=CIRCLE('',#2199,1.7);
#816=CIRCLE('',#2200,1.70000000000001);
#817=CIRCLE('',#2201,1.);
#818=CIRCLE('',#2202,1.);
#819=CIRCLE('',#2203,1.);
#820=CIRCLE('',#2204,1.);
#821=CIRCLE('',#2205,0.5);
#822=CIRCLE('',#2206,0.499999999999998);
#823=CIRCLE('',#2207,0.499999999999998);
#824=CIRCLE('',#2208,0.5);
#825=CIRCLE('',#2209,0.499999999999998);
#826=CIRCLE('',#2210,0.499999999999998);
#827=CIRCLE('',#2211,0.499999999999998);
#828=CIRCLE('',#2212,0.5);
#829=VERTEX_POINT('',#2925);
#830=VERTEX_POINT('',#2926);
#831=VERTEX_POINT('',#2928);
#832=VERTEX_POINT('',#2930);
#833=VERTEX_POINT('',#2934);
#834=VERTEX_POINT('',#2936);
#835=VERTEX_POINT('',#2940);
#836=VERTEX_POINT('',#2942);
#837=VERTEX_POINT('',#2944);
#838=VERTEX_POINT('',#2946);
#839=VERTEX_POINT('',#2948);
#840=VERTEX_POINT('',#2950);
#841=VERTEX_POINT('',#2952);
#842=VERTEX_POINT('',#2954);
#843=VERTEX_POINT('',#2956);
#844=VERTEX_POINT('',#2958);
#845=VERTEX_POINT('',#2960);
#846=VERTEX_POINT('',#2962);
#847=VERTEX_POINT('',#2964);
#848=VERTEX_POINT('',#2967);
#849=VERTEX_POINT('',#2968);
#850=VERTEX_POINT('',#2970);
#851=VERTEX_POINT('',#2972);
#852=VERTEX_POINT('',#2974);
#853=VERTEX_POINT('',#2976);
#854=VERTEX_POINT('',#2978);
#855=VERTEX_POINT('',#2980);
#856=VERTEX_POINT('',#2983);
#857=VERTEX_POINT('',#2984);
#858=VERTEX_POINT('',#2986);
#859=VERTEX_POINT('',#2988);
#860=VERTEX_POINT('',#2990);
#861=VERTEX_POINT('',#2992);
#862=VERTEX_POINT('',#2994);
#863=VERTEX_POINT('',#2996);
#864=VERTEX_POINT('',#2999);
#865=VERTEX_POINT('',#3000);
#866=VERTEX_POINT('',#3002);
#867=VERTEX_POINT('',#3004);
#868=VERTEX_POINT('',#3006);
#869=VERTEX_POINT('',#3008);
#870=VERTEX_POINT('',#3010);
#871=VERTEX_POINT('',#3012);
#872=VERTEX_POINT('',#3015);
#873=VERTEX_POINT('',#3017);
#874=VERTEX_POINT('',#3019);
#875=VERTEX_POINT('',#3020);
#876=VERTEX_POINT('',#3022);
#877=VERTEX_POINT('',#3024);
#878=VERTEX_POINT('',#3026);
#879=VERTEX_POINT('',#3028);
#880=VERTEX_POINT('',#3030);
#881=VERTEX_POINT('',#3032);
#882=VERTEX_POINT('',#3035);
#883=VERTEX_POINT('',#3036);
#884=VERTEX_POINT('',#3038);
#885=VERTEX_POINT('',#3040);
#886=VERTEX_POINT('',#3042);
#887=VERTEX_POINT('',#3044);
#888=VERTEX_POINT('',#3046);
#889=VERTEX_POINT('',#3048);
#890=VERTEX_POINT('',#3051);
#891=VERTEX_POINT('',#3053);
#892=VERTEX_POINT('',#3054);
#893=VERTEX_POINT('',#3056);
#894=VERTEX_POINT('',#3058);
#895=VERTEX_POINT('',#3060);
#896=VERTEX_POINT('',#3062);
#897=VERTEX_POINT('',#3064);
#898=VERTEX_POINT('',#3066);
#899=VERTEX_POINT('',#3069);
#900=VERTEX_POINT('',#3071);
#901=VERTEX_POINT('',#3072);
#902=VERTEX_POINT('',#3074);
#903=VERTEX_POINT('',#3076);
#904=VERTEX_POINT('',#3078);
#905=VERTEX_POINT('',#3080);
#906=VERTEX_POINT('',#3082);
#907=VERTEX_POINT('',#3084);
#908=VERTEX_POINT('',#3087);
#909=VERTEX_POINT('',#3088);
#910=VERTEX_POINT('',#3090);
#911=VERTEX_POINT('',#3092);
#912=VERTEX_POINT('',#3094);
#913=VERTEX_POINT('',#3096);
#914=VERTEX_POINT('',#3098);
#915=VERTEX_POINT('',#3100);
#916=VERTEX_POINT('',#3103);
#917=VERTEX_POINT('',#3104);
#918=VERTEX_POINT('',#3106);
#919=VERTEX_POINT('',#3108);
#920=VERTEX_POINT('',#3110);
#921=VERTEX_POINT('',#3112);
#922=VERTEX_POINT('',#3114);
#923=VERTEX_POINT('',#3116);
#924=VERTEX_POINT('',#3119);
#925=VERTEX_POINT('',#3120);
#926=VERTEX_POINT('',#3122);
#927=VERTEX_POINT('',#3124);
#928=VERTEX_POINT('',#3126);
#929=VERTEX_POINT('',#3128);
#930=VERTEX_POINT('',#3130);
#931=VERTEX_POINT('',#3132);
#932=VERTEX_POINT('',#3136);
#933=VERTEX_POINT('',#3137);
#934=VERTEX_POINT('',#3142);
#935=VERTEX_POINT('',#3146);
#936=VERTEX_POINT('',#3147);
#937=VERTEX_POINT('',#3152);
#938=VERTEX_POINT('',#3156);
#939=VERTEX_POINT('',#3164);
#940=VERTEX_POINT('',#3166);
#941=VERTEX_POINT('',#3168);
#942=VERTEX_POINT('',#3170);
#943=VERTEX_POINT('',#3172);
#944=VERTEX_POINT('',#3174);
#945=VERTEX_POINT('',#3177);
#946=VERTEX_POINT('',#3178);
#947=VERTEX_POINT('',#3180);
#948=VERTEX_POINT('',#3182);
#949=VERTEX_POINT('',#3184);
#950=VERTEX_POINT('',#3186);
#951=VERTEX_POINT('',#3188);
#952=VERTEX_POINT('',#3190);
#953=VERTEX_POINT('',#3193);
#954=VERTEX_POINT('',#3194);
#955=VERTEX_POINT('',#3196);
#956=VERTEX_POINT('',#3198);
#957=VERTEX_POINT('',#3200);
#958=VERTEX_POINT('',#3202);
#959=VERTEX_POINT('',#3204);
#960=VERTEX_POINT('',#3206);
#961=VERTEX_POINT('',#3209);
#962=VERTEX_POINT('',#3210);
#963=VERTEX_POINT('',#3212);
#964=VERTEX_POINT('',#3214);
#965=VERTEX_POINT('',#3216);
#966=VERTEX_POINT('',#3218);
#967=VERTEX_POINT('',#3220);
#968=VERTEX_POINT('',#3222);
#969=VERTEX_POINT('',#3225);
#970=VERTEX_POINT('',#3226);
#971=VERTEX_POINT('',#3228);
#972=VERTEX_POINT('',#3230);
#973=VERTEX_POINT('',#3232);
#974=VERTEX_POINT('',#3234);
#975=VERTEX_POINT('',#3236);
#976=VERTEX_POINT('',#3238);
#977=VERTEX_POINT('',#3241);
#978=VERTEX_POINT('',#3243);
#979=VERTEX_POINT('',#3244);
#980=VERTEX_POINT('',#3246);
#981=VERTEX_POINT('',#3248);
#982=VERTEX_POINT('',#3250);
#983=VERTEX_POINT('',#3252);
#984=VERTEX_POINT('',#3254);
#985=VERTEX_POINT('',#3256);
#986=VERTEX_POINT('',#3259);
#987=VERTEX_POINT('',#3261);
#988=VERTEX_POINT('',#3262);
#989=VERTEX_POINT('',#3264);
#990=VERTEX_POINT('',#3266);
#991=VERTEX_POINT('',#3268);
#992=VERTEX_POINT('',#3270);
#993=VERTEX_POINT('',#3272);
#994=VERTEX_POINT('',#3274);
#995=VERTEX_POINT('',#3277);
#996=VERTEX_POINT('',#3278);
#997=VERTEX_POINT('',#3280);
#998=VERTEX_POINT('',#3282);
#999=VERTEX_POINT('',#3284);
#1000=VERTEX_POINT('',#3286);
#1001=VERTEX_POINT('',#3288);
#1002=VERTEX_POINT('',#3290);
#1003=VERTEX_POINT('',#3293);
#1004=VERTEX_POINT('',#3295);
#1005=VERTEX_POINT('',#3297);
#1006=VERTEX_POINT('',#3298);
#1007=VERTEX_POINT('',#3300);
#1008=VERTEX_POINT('',#3302);
#1009=VERTEX_POINT('',#3304);
#1010=VERTEX_POINT('',#3306);
#1011=VERTEX_POINT('',#3308);
#1012=VERTEX_POINT('',#3310);
#1013=VERTEX_POINT('',#3313);
#1014=VERTEX_POINT('',#3314);
#1015=VERTEX_POINT('',#3316);
#1016=VERTEX_POINT('',#3318);
#1017=VERTEX_POINT('',#3320);
#1018=VERTEX_POINT('',#3322);
#1019=VERTEX_POINT('',#3324);
#1020=VERTEX_POINT('',#3326);
#1021=VERTEX_POINT('',#3329);
#1022=VERTEX_POINT('',#3330);
#1023=VERTEX_POINT('',#3332);
#1024=VERTEX_POINT('',#3334);
#1025=VERTEX_POINT('',#3336);
#1026=VERTEX_POINT('',#3338);
#1027=VERTEX_POINT('',#3340);
#1028=VERTEX_POINT('',#3342);
#1029=EDGE_CURVE('',#829,#830,#725,.T.);
#1030=EDGE_CURVE('',#830,#831,#333,.T.);
#1031=EDGE_CURVE('',#831,#832,#726,.T.);
#1032=EDGE_CURVE('',#832,#829,#334,.T.);
#1033=EDGE_CURVE('',#830,#833,#335,.T.);
#1034=EDGE_CURVE('',#834,#833,#336,.T.);
#1035=EDGE_CURVE('',#831,#834,#337,.T.);
#1036=EDGE_CURVE('',#829,#835,#338,.T.);
#1037=EDGE_CURVE('',#836,#835,#727,.T.);
#1038=EDGE_CURVE('',#836,#837,#339,.T.);
#1039=EDGE_CURVE('',#838,#837,#728,.T.);
#1040=EDGE_CURVE('',#838,#839,#340,.T.);
#1041=EDGE_CURVE('',#840,#839,#729,.T.);
#1042=EDGE_CURVE('',#841,#840,#341,.T.);
#1043=EDGE_CURVE('',#842,#841,#730,.T.);
#1044=EDGE_CURVE('',#843,#842,#342,.T.);
#1045=EDGE_CURVE('',#844,#843,#731,.T.);
#1046=EDGE_CURVE('',#845,#844,#343,.T.);
#1047=EDGE_CURVE('',#846,#845,#732,.T.);
#1048=EDGE_CURVE('',#847,#846,#344,.T.);
#1049=EDGE_CURVE('',#833,#847,#733,.T.);
#1050=EDGE_CURVE('',#848,#849,#734,.T.);
#1051=EDGE_CURVE('',#850,#848,#345,.T.);
#1052=EDGE_CURVE('',#851,#850,#735,.T.);
#1053=EDGE_CURVE('',#852,#851,#346,.T.);
#1054=EDGE_CURVE('',#853,#852,#736,.T.);
#1055=EDGE_CURVE('',#854,#853,#347,.T.);
#1056=EDGE_CURVE('',#855,#854,#737,.T.);
#1057=EDGE_CURVE('',#849,#855,#348,.T.);
#1058=EDGE_CURVE('',#856,#857,#738,.T.);
#1059=EDGE_CURVE('',#858,#856,#349,.T.);
#1060=EDGE_CURVE('',#859,#858,#739,.T.);
#1061=EDGE_CURVE('',#860,#859,#350,.T.);
#1062=EDGE_CURVE('',#861,#860,#740,.T.);
#1063=EDGE_CURVE('',#862,#861,#351,.T.);
#1064=EDGE_CURVE('',#863,#862,#741,.T.);
#1065=EDGE_CURVE('',#857,#863,#352,.T.);
#1066=EDGE_CURVE('',#864,#865,#742,.T.);
#1067=EDGE_CURVE('',#866,#864,#353,.T.);
#1068=EDGE_CURVE('',#867,#866,#743,.T.);
#1069=EDGE_CURVE('',#868,#867,#354,.T.);
#1070=EDGE_CURVE('',#869,#868,#744,.T.);
#1071=EDGE_CURVE('',#870,#869,#355,.T.);
#1072=EDGE_CURVE('',#871,#870,#745,.T.);
#1073=EDGE_CURVE('',#865,#871,#356,.T.);
#1074=EDGE_CURVE('',#872,#872,#746,.T.);
#1075=EDGE_CURVE('',#873,#873,#747,.T.);
#1076=EDGE_CURVE('',#874,#875,#748,.T.);
#1077=EDGE_CURVE('',#876,#874,#357,.T.);
#1078=EDGE_CURVE('',#877,#876,#749,.T.);
#1079=EDGE_CURVE('',#878,#877,#358,.T.);
#1080=EDGE_CURVE('',#879,#878,#750,.T.);
#1081=EDGE_CURVE('',#880,#879,#359,.T.);
#1082=EDGE_CURVE('',#881,#880,#751,.T.);
#1083=EDGE_CURVE('',#875,#881,#360,.T.);
#1084=EDGE_CURVE('',#882,#883,#752,.T.);
#1085=EDGE_CURVE('',#884,#882,#361,.T.);
#1086=EDGE_CURVE('',#885,#884,#753,.T.);
#1087=EDGE_CURVE('',#886,#885,#362,.T.);
#1088=EDGE_CURVE('',#887,#886,#754,.T.);
#1089=EDGE_CURVE('',#888,#887,#363,.T.);
#1090=EDGE_CURVE('',#889,#888,#755,.T.);
#1091=EDGE_CURVE('',#883,#889,#364,.T.);
#1092=EDGE_CURVE('',#890,#890,#756,.T.);
#1093=EDGE_CURVE('',#891,#892,#757,.T.);
#1094=EDGE_CURVE('',#893,#891,#365,.T.);
#1095=EDGE_CURVE('',#894,#893,#758,.T.);
#1096=EDGE_CURVE('',#895,#894,#366,.T.);
#1097=EDGE_CURVE('',#896,#895,#759,.T.);
#1098=EDGE_CURVE('',#897,#896,#367,.T.);
#1099=EDGE_CURVE('',#898,#897,#760,.T.);
#1100=EDGE_CURVE('',#892,#898,#368,.T.);
#1101=EDGE_CURVE('',#899,#899,#761,.T.);
#1102=EDGE_CURVE('',#900,#901,#762,.T.);
#1103=EDGE_CURVE('',#902,#900,#369,.T.);
#1104=EDGE_CURVE('',#903,#902,#763,.T.);
#1105=EDGE_CURVE('',#904,#903,#370,.T.);
#1106=EDGE_CURVE('',#905,#904,#764,.T.);
#1107=EDGE_CURVE('',#906,#905,#371,.T.);
#1108=EDGE_CURVE('',#907,#906,#765,.T.);
#1109=EDGE_CURVE('',#901,#907,#372,.T.);
#1110=EDGE_CURVE('',#908,#909,#766,.T.);
#1111=EDGE_CURVE('',#910,#908,#373,.T.);
#1112=EDGE_CURVE('',#911,#910,#767,.T.);
#1113=EDGE_CURVE('',#912,#911,#374,.T.);
#1114=EDGE_CURVE('',#913,#912,#768,.T.);
#1115=EDGE_CURVE('',#914,#913,#375,.T.);
#1116=EDGE_CURVE('',#915,#914,#769,.T.);
#1117=EDGE_CURVE('',#909,#915,#376,.T.);
#1118=EDGE_CURVE('',#916,#917,#770,.T.);
#1119=EDGE_CURVE('',#918,#916,#377,.T.);
#1120=EDGE_CURVE('',#919,#918,#771,.T.);
#1121=EDGE_CURVE('',#920,#919,#378,.T.);
#1122=EDGE_CURVE('',#921,#920,#772,.T.);
#1123=EDGE_CURVE('',#922,#921,#379,.T.);
#1124=EDGE_CURVE('',#923,#922,#773,.T.);
#1125=EDGE_CURVE('',#917,#923,#380,.T.);
#1126=EDGE_CURVE('',#924,#925,#774,.T.);
#1127=EDGE_CURVE('',#926,#924,#381,.T.);
#1128=EDGE_CURVE('',#927,#926,#775,.T.);
#1129=EDGE_CURVE('',#928,#927,#382,.T.);
#1130=EDGE_CURVE('',#929,#928,#776,.T.);
#1131=EDGE_CURVE('',#930,#929,#383,.T.);
#1132=EDGE_CURVE('',#931,#930,#777,.T.);
#1133=EDGE_CURVE('',#925,#931,#384,.T.);
#1134=EDGE_CURVE('',#932,#933,#778,.T.);
#1135=EDGE_CURVE('',#933,#840,#385,.T.);
#1136=EDGE_CURVE('',#839,#932,#386,.T.);
#1137=EDGE_CURVE('',#934,#933,#387,.T.);
#1138=EDGE_CURVE('',#841,#934,#388,.T.);
#1139=EDGE_CURVE('',#935,#936,#779,.T.);
#1140=EDGE_CURVE('',#936,#836,#389,.T.);
#1141=EDGE_CURVE('',#835,#935,#390,.T.);
#1142=EDGE_CURVE('',#937,#936,#391,.T.);
#1143=EDGE_CURVE('',#837,#937,#392,.T.);
#1144=EDGE_CURVE('',#937,#938,#780,.T.);
#1145=EDGE_CURVE('',#938,#838,#393,.T.);
#1146=EDGE_CURVE('',#932,#938,#394,.T.);
#1147=EDGE_CURVE('',#935,#832,#395,.T.);
#1148=EDGE_CURVE('',#939,#834,#781,.T.);
#1149=EDGE_CURVE('',#939,#940,#396,.T.);
#1150=EDGE_CURVE('',#941,#940,#782,.T.);
#1151=EDGE_CURVE('',#941,#942,#397,.T.);
#1152=EDGE_CURVE('',#943,#942,#783,.T.);
#1153=EDGE_CURVE('',#943,#944,#398,.T.);
#1154=EDGE_CURVE('',#934,#944,#784,.T.);
#1155=EDGE_CURVE('',#945,#946,#785,.T.);
#1156=EDGE_CURVE('',#946,#947,#399,.T.);
#1157=EDGE_CURVE('',#947,#948,#786,.T.);
#1158=EDGE_CURVE('',#948,#949,#400,.T.);
#1159=EDGE_CURVE('',#949,#950,#787,.T.);
#1160=EDGE_CURVE('',#950,#951,#401,.T.);
#1161=EDGE_CURVE('',#951,#952,#788,.T.);
#1162=EDGE_CURVE('',#952,#945,#402,.T.);
#1163=EDGE_CURVE('',#953,#954,#789,.T.);
#1164=EDGE_CURVE('',#954,#955,#403,.T.);
#1165=EDGE_CURVE('',#955,#956,#790,.T.);
#1166=EDGE_CURVE('',#956,#957,#404,.T.);
#1167=EDGE_CURVE('',#957,#958,#791,.T.);
#1168=EDGE_CURVE('',#958,#959,#405,.T.);
#1169=EDGE_CURVE('',#959,#960,#792,.T.);
#1170=EDGE_CURVE('',#960,#953,#406,.T.);
#1171=EDGE_CURVE('',#961,#962,#793,.T.);
#1172=EDGE_CURVE('',#962,#963,#407,.T.);
#1173=EDGE_CURVE('',#963,#964,#794,.T.);
#1174=EDGE_CURVE('',#964,#965,#408,.T.);
#1175=EDGE_CURVE('',#965,#966,#795,.T.);
#1176=EDGE_CURVE('',#966,#967,#409,.T.);
#1177=EDGE_CURVE('',#967,#968,#796,.T.);
#1178=EDGE_CURVE('',#968,#961,#410,.T.);
#1179=EDGE_CURVE('',#969,#970,#797,.T.);
#1180=EDGE_CURVE('',#970,#971,#411,.T.);
#1181=EDGE_CURVE('',#971,#972,#798,.T.);
#1182=EDGE_CURVE('',#972,#973,#412,.T.);
#1183=EDGE_CURVE('',#973,#974,#799,.T.);
#1184=EDGE_CURVE('',#974,#975,#413,.T.);
#1185=EDGE_CURVE('',#975,#976,#800,.T.);
#1186=EDGE_CURVE('',#976,#969,#414,.T.);
#1187=EDGE_CURVE('',#977,#977,#801,.T.);
#1188=EDGE_CURVE('',#978,#979,#802,.T.);
#1189=EDGE_CURVE('',#979,#980,#415,.T.);
#1190=EDGE_CURVE('',#980,#981,#803,.T.);
#1191=EDGE_CURVE('',#981,#982,#416,.T.);
#1192=EDGE_CURVE('',#982,#983,#804,.T.);
#1193=EDGE_CURVE('',#983,#984,#417,.T.);
#1194=EDGE_CURVE('',#984,#985,#805,.T.);
#1195=EDGE_CURVE('',#985,#978,#418,.T.);
#1196=EDGE_CURVE('',#986,#986,#806,.T.);
#1197=EDGE_CURVE('',#987,#988,#807,.T.);
#1198=EDGE_CURVE('',#988,#989,#419,.T.);
#1199=EDGE_CURVE('',#989,#990,#808,.T.);
#1200=EDGE_CURVE('',#990,#991,#420,.T.);
#1201=EDGE_CURVE('',#991,#992,#809,.T.);
#1202=EDGE_CURVE('',#992,#993,#421,.T.);
#1203=EDGE_CURVE('',#993,#994,#810,.T.);
#1204=EDGE_CURVE('',#994,#987,#422,.T.);
#1205=EDGE_CURVE('',#995,#996,#811,.T.);
#1206=EDGE_CURVE('',#996,#997,#423,.T.);
#1207=EDGE_CURVE('',#997,#998,#812,.T.);
#1208=EDGE_CURVE('',#998,#999,#424,.T.);
#1209=EDGE_CURVE('',#999,#1000,#813,.T.);
#1210=EDGE_CURVE('',#1000,#1001,#425,.T.);
#1211=EDGE_CURVE('',#1001,#1002,#814,.T.);
#1212=EDGE_CURVE('',#1002,#995,#426,.T.);
#1213=EDGE_CURVE('',#1003,#1003,#815,.T.);
#1214=EDGE_CURVE('',#1004,#1004,#816,.T.);
#1215=EDGE_CURVE('',#1005,#1006,#427,.T.);
#1216=EDGE_CURVE('',#1007,#1006,#817,.T.);
#1217=EDGE_CURVE('',#1007,#1008,#428,.T.);
#1218=EDGE_CURVE('',#1009,#1008,#818,.T.);
#1219=EDGE_CURVE('',#1009,#1010,#429,.T.);
#1220=EDGE_CURVE('',#1011,#1010,#819,.T.);
#1221=EDGE_CURVE('',#1011,#1012,#430,.T.);
#1222=EDGE_CURVE('',#1005,#1012,#820,.T.);
#1223=EDGE_CURVE('',#1013,#1014,#821,.T.);
#1224=EDGE_CURVE('',#1014,#1015,#431,.T.);
#1225=EDGE_CURVE('',#1015,#1016,#822,.T.);
#1226=EDGE_CURVE('',#1016,#1017,#432,.T.);
#1227=EDGE_CURVE('',#1017,#1018,#823,.T.);
#1228=EDGE_CURVE('',#1018,#1019,#433,.T.);
#1229=EDGE_CURVE('',#1019,#1020,#824,.T.);
#1230=EDGE_CURVE('',#1020,#1013,#434,.T.);
#1231=EDGE_CURVE('',#1021,#1022,#825,.T.);
#1232=EDGE_CURVE('',#1022,#1023,#435,.T.);
#1233=EDGE_CURVE('',#1023,#1024,#826,.T.);
#1234=EDGE_CURVE('',#1024,#1025,#436,.T.);
#1235=EDGE_CURVE('',#1025,#1026,#827,.T.);
#1236=EDGE_CURVE('',#1026,#1027,#437,.T.);
#1237=EDGE_CURVE('',#1027,#1028,#828,.T.);
#1238=EDGE_CURVE('',#1028,#1021,#438,.T.);
#1239=EDGE_CURVE('',#870,#1007,#439,.T.);
#1240=EDGE_CURVE('',#1006,#871,#440,.T.);
#1241=EDGE_CURVE('',#1008,#869,#441,.T.);
#1242=EDGE_CURVE('',#865,#1005,#442,.T.);
#1243=EDGE_CURVE('',#1012,#864,#443,.T.);
#1244=EDGE_CURVE('',#866,#1011,#444,.T.);
#1245=EDGE_CURVE('',#1010,#867,#445,.T.);
#1246=EDGE_CURVE('',#868,#1009,#446,.T.);
#1247=EDGE_CURVE('',#847,#939,#447,.T.);
#1248=EDGE_CURVE('',#940,#846,#448,.T.);
#1249=EDGE_CURVE('',#944,#842,#449,.T.);
#1250=EDGE_CURVE('',#843,#943,#450,.T.);
#1251=EDGE_CURVE('',#942,#844,#451,.T.);
#1252=EDGE_CURVE('',#845,#941,#452,.T.);
#1253=EDGE_CURVE('',#924,#945,#453,.T.);
#1254=EDGE_CURVE('',#926,#952,#454,.T.);
#1255=EDGE_CURVE('',#927,#951,#455,.T.);
#1256=EDGE_CURVE('',#928,#950,#456,.T.);
#1257=EDGE_CURVE('',#929,#949,#457,.T.);
#1258=EDGE_CURVE('',#930,#948,#458,.T.);
#1259=EDGE_CURVE('',#931,#947,#459,.T.);
#1260=EDGE_CURVE('',#925,#946,#460,.T.);
#1261=EDGE_CURVE('',#916,#953,#461,.T.);
#1262=EDGE_CURVE('',#918,#960,#462,.T.);
#1263=EDGE_CURVE('',#919,#959,#463,.T.);
#1264=EDGE_CURVE('',#920,#958,#464,.T.);
#1265=EDGE_CURVE('',#921,#957,#465,.T.);
#1266=EDGE_CURVE('',#922,#956,#466,.T.);
#1267=EDGE_CURVE('',#923,#955,#467,.T.);
#1268=EDGE_CURVE('',#917,#954,#468,.T.);
#1269=EDGE_CURVE('',#908,#961,#469,.T.);
#1270=EDGE_CURVE('',#910,#968,#470,.T.);
#1271=EDGE_CURVE('',#911,#967,#471,.T.);
#1272=EDGE_CURVE('',#912,#966,#472,.T.);
#1273=EDGE_CURVE('',#913,#965,#473,.T.);
#1274=EDGE_CURVE('',#914,#964,#474,.T.);
#1275=EDGE_CURVE('',#915,#963,#475,.T.);
#1276=EDGE_CURVE('',#909,#962,#476,.T.);
#1277=EDGE_CURVE('',#900,#969,#477,.T.);
#1278=EDGE_CURVE('',#902,#976,#478,.T.);
#1279=EDGE_CURVE('',#903,#975,#479,.T.);
#1280=EDGE_CURVE('',#904,#974,#480,.T.);
#1281=EDGE_CURVE('',#905,#973,#481,.T.);
#1282=EDGE_CURVE('',#906,#972,#482,.T.);
#1283=EDGE_CURVE('',#907,#971,#483,.T.);
#1284=EDGE_CURVE('',#901,#970,#484,.T.);
#1285=EDGE_CURVE('',#977,#899,#485,.T.);
#1286=EDGE_CURVE('',#891,#978,#486,.T.);
#1287=EDGE_CURVE('',#893,#985,#487,.T.);
#1288=EDGE_CURVE('',#894,#984,#488,.T.);
#1289=EDGE_CURVE('',#895,#983,#489,.T.);
#1290=EDGE_CURVE('',#896,#982,#490,.T.);
#1291=EDGE_CURVE('',#897,#981,#491,.T.);
#1292=EDGE_CURVE('',#898,#980,#492,.T.);
#1293=EDGE_CURVE('',#892,#979,#493,.T.);
#1294=EDGE_CURVE('',#986,#890,#494,.T.);
#1295=EDGE_CURVE('',#882,#987,#495,.T.);
#1296=EDGE_CURVE('',#884,#994,#496,.T.);
#1297=EDGE_CURVE('',#885,#993,#497,.T.);
#1298=EDGE_CURVE('',#886,#992,#498,.T.);
#1299=EDGE_CURVE('',#887,#991,#499,.T.);
#1300=EDGE_CURVE('',#888,#990,#500,.T.);
#1301=EDGE_CURVE('',#889,#989,#501,.T.);
#1302=EDGE_CURVE('',#883,#988,#502,.T.);
#1303=EDGE_CURVE('',#874,#995,#503,.T.);
#1304=EDGE_CURVE('',#876,#1002,#504,.T.);
#1305=EDGE_CURVE('',#877,#1001,#505,.T.);
#1306=EDGE_CURVE('',#878,#1000,#506,.T.);
#1307=EDGE_CURVE('',#879,#999,#507,.T.);
#1308=EDGE_CURVE('',#880,#998,#508,.T.);
#1309=EDGE_CURVE('',#881,#997,#509,.T.);
#1310=EDGE_CURVE('',#875,#996,#510,.T.);
#1311=EDGE_CURVE('',#1003,#873,#511,.T.);
#1312=EDGE_CURVE('',#1004,#872,#512,.T.);
#1313=EDGE_CURVE('',#856,#1013,#513,.T.);
#1314=EDGE_CURVE('',#858,#1020,#514,.T.);
#1315=EDGE_CURVE('',#859,#1019,#515,.T.);
#1316=EDGE_CURVE('',#860,#1018,#516,.T.);
#1317=EDGE_CURVE('',#861,#1017,#517,.T.);
#1318=EDGE_CURVE('',#862,#1016,#518,.T.);
#1319=EDGE_CURVE('',#863,#1015,#519,.T.);
#1320=EDGE_CURVE('',#857,#1014,#520,.T.);
#1321=EDGE_CURVE('',#848,#1021,#521,.T.);
#1322=EDGE_CURVE('',#850,#1028,#522,.T.);
#1323=EDGE_CURVE('',#851,#1027,#523,.T.);
#1324=EDGE_CURVE('',#852,#1026,#524,.T.);
#1325=EDGE_CURVE('',#853,#1025,#525,.T.);
#1326=EDGE_CURVE('',#854,#1024,#526,.T.);
#1327=EDGE_CURVE('',#855,#1023,#527,.T.);
#1328=EDGE_CURVE('',#849,#1022,#528,.T.);
#1329=ORIENTED_EDGE('',*,*,#1029,.T.);
#1330=ORIENTED_EDGE('',*,*,#1030,.T.);
#1331=ORIENTED_EDGE('',*,*,#1031,.T.);
#1332=ORIENTED_EDGE('',*,*,#1032,.T.);
#1333=ORIENTED_EDGE('',*,*,#1030,.F.);
#1334=ORIENTED_EDGE('',*,*,#1033,.T.);
#1335=ORIENTED_EDGE('',*,*,#1034,.F.);
#1336=ORIENTED_EDGE('',*,*,#1035,.F.);
#1337=ORIENTED_EDGE('',*,*,#1029,.F.);
#1338=ORIENTED_EDGE('',*,*,#1036,.T.);
#1339=ORIENTED_EDGE('',*,*,#1037,.F.);
#1340=ORIENTED_EDGE('',*,*,#1038,.T.);
#1341=ORIENTED_EDGE('',*,*,#1039,.F.);
#1342=ORIENTED_EDGE('',*,*,#1040,.T.);
#1343=ORIENTED_EDGE('',*,*,#1041,.F.);
#1344=ORIENTED_EDGE('',*,*,#1042,.F.);
#1345=ORIENTED_EDGE('',*,*,#1043,.F.);
#1346=ORIENTED_EDGE('',*,*,#1044,.F.);
#1347=ORIENTED_EDGE('',*,*,#1045,.F.);
#1348=ORIENTED_EDGE('',*,*,#1046,.F.);
#1349=ORIENTED_EDGE('',*,*,#1047,.F.);
#1350=ORIENTED_EDGE('',*,*,#1048,.F.);
#1351=ORIENTED_EDGE('',*,*,#1049,.F.);
#1352=ORIENTED_EDGE('',*,*,#1033,.F.);
#1353=ORIENTED_EDGE('',*,*,#1050,.F.);
#1354=ORIENTED_EDGE('',*,*,#1051,.F.);
#1355=ORIENTED_EDGE('',*,*,#1052,.F.);
#1356=ORIENTED_EDGE('',*,*,#1053,.F.);
#1357=ORIENTED_EDGE('',*,*,#1054,.F.);
#1358=ORIENTED_EDGE('',*,*,#1055,.F.);
#1359=ORIENTED_EDGE('',*,*,#1056,.F.);
#1360=ORIENTED_EDGE('',*,*,#1057,.F.);
#1361=ORIENTED_EDGE('',*,*,#1058,.F.);
#1362=ORIENTED_EDGE('',*,*,#1059,.F.);
#1363=ORIENTED_EDGE('',*,*,#1060,.F.);
#1364=ORIENTED_EDGE('',*,*,#1061,.F.);
#1365=ORIENTED_EDGE('',*,*,#1062,.F.);
#1366=ORIENTED_EDGE('',*,*,#1063,.F.);
#1367=ORIENTED_EDGE('',*,*,#1064,.F.);
#1368=ORIENTED_EDGE('',*,*,#1065,.F.);
#1369=ORIENTED_EDGE('',*,*,#1066,.F.);
#1370=ORIENTED_EDGE('',*,*,#1067,.F.);
#1371=ORIENTED_EDGE('',*,*,#1068,.F.);
#1372=ORIENTED_EDGE('',*,*,#1069,.F.);
#1373=ORIENTED_EDGE('',*,*,#1070,.F.);
#1374=ORIENTED_EDGE('',*,*,#1071,.F.);
#1375=ORIENTED_EDGE('',*,*,#1072,.F.);
#1376=ORIENTED_EDGE('',*,*,#1073,.F.);
#1377=ORIENTED_EDGE('',*,*,#1074,.F.);
#1378=ORIENTED_EDGE('',*,*,#1075,.F.);
#1379=ORIENTED_EDGE('',*,*,#1076,.F.);
#1380=ORIENTED_EDGE('',*,*,#1077,.F.);
#1381=ORIENTED_EDGE('',*,*,#1078,.F.);
#1382=ORIENTED_EDGE('',*,*,#1079,.F.);
#1383=ORIENTED_EDGE('',*,*,#1080,.F.);
#1384=ORIENTED_EDGE('',*,*,#1081,.F.);
#1385=ORIENTED_EDGE('',*,*,#1082,.F.);
#1386=ORIENTED_EDGE('',*,*,#1083,.F.);
#1387=ORIENTED_EDGE('',*,*,#1084,.F.);
#1388=ORIENTED_EDGE('',*,*,#1085,.F.);
#1389=ORIENTED_EDGE('',*,*,#1086,.F.);
#1390=ORIENTED_EDGE('',*,*,#1087,.F.);
#1391=ORIENTED_EDGE('',*,*,#1088,.F.);
#1392=ORIENTED_EDGE('',*,*,#1089,.F.);
#1393=ORIENTED_EDGE('',*,*,#1090,.F.);
#1394=ORIENTED_EDGE('',*,*,#1091,.F.);
#1395=ORIENTED_EDGE('',*,*,#1092,.F.);
#1396=ORIENTED_EDGE('',*,*,#1093,.F.);
#1397=ORIENTED_EDGE('',*,*,#1094,.F.);
#1398=ORIENTED_EDGE('',*,*,#1095,.F.);
#1399=ORIENTED_EDGE('',*,*,#1096,.F.);
#1400=ORIENTED_EDGE('',*,*,#1097,.F.);
#1401=ORIENTED_EDGE('',*,*,#1098,.F.);
#1402=ORIENTED_EDGE('',*,*,#1099,.F.);
#1403=ORIENTED_EDGE('',*,*,#1100,.F.);
#1404=ORIENTED_EDGE('',*,*,#1101,.F.);
#1405=ORIENTED_EDGE('',*,*,#1102,.F.);
#1406=ORIENTED_EDGE('',*,*,#1103,.F.);
#1407=ORIENTED_EDGE('',*,*,#1104,.F.);
#1408=ORIENTED_EDGE('',*,*,#1105,.F.);
#1409=ORIENTED_EDGE('',*,*,#1106,.F.);
#1410=ORIENTED_EDGE('',*,*,#1107,.F.);
#1411=ORIENTED_EDGE('',*,*,#1108,.F.);
#1412=ORIENTED_EDGE('',*,*,#1109,.F.);
#1413=ORIENTED_EDGE('',*,*,#1110,.F.);
#1414=ORIENTED_EDGE('',*,*,#1111,.F.);
#1415=ORIENTED_EDGE('',*,*,#1112,.F.);
#1416=ORIENTED_EDGE('',*,*,#1113,.F.);
#1417=ORIENTED_EDGE('',*,*,#1114,.F.);
#1418=ORIENTED_EDGE('',*,*,#1115,.F.);
#1419=ORIENTED_EDGE('',*,*,#1116,.F.);
#1420=ORIENTED_EDGE('',*,*,#1117,.F.);
#1421=ORIENTED_EDGE('',*,*,#1118,.F.);
#1422=ORIENTED_EDGE('',*,*,#1119,.F.);
#1423=ORIENTED_EDGE('',*,*,#1120,.F.);
#1424=ORIENTED_EDGE('',*,*,#1121,.F.);
#1425=ORIENTED_EDGE('',*,*,#1122,.F.);
#1426=ORIENTED_EDGE('',*,*,#1123,.F.);
#1427=ORIENTED_EDGE('',*,*,#1124,.F.);
#1428=ORIENTED_EDGE('',*,*,#1125,.F.);
#1429=ORIENTED_EDGE('',*,*,#1126,.F.);
#1430=ORIENTED_EDGE('',*,*,#1127,.F.);
#1431=ORIENTED_EDGE('',*,*,#1128,.F.);
#1432=ORIENTED_EDGE('',*,*,#1129,.F.);
#1433=ORIENTED_EDGE('',*,*,#1130,.F.);
#1434=ORIENTED_EDGE('',*,*,#1131,.F.);
#1435=ORIENTED_EDGE('',*,*,#1132,.F.);
#1436=ORIENTED_EDGE('',*,*,#1133,.F.);
#1437=ORIENTED_EDGE('',*,*,#1134,.T.);
#1438=ORIENTED_EDGE('',*,*,#1135,.T.);
#1439=ORIENTED_EDGE('',*,*,#1041,.T.);
#1440=ORIENTED_EDGE('',*,*,#1136,.T.);
#1441=ORIENTED_EDGE('',*,*,#1135,.F.);
#1442=ORIENTED_EDGE('',*,*,#1137,.F.);
#1443=ORIENTED_EDGE('',*,*,#1138,.F.);
#1444=ORIENTED_EDGE('',*,*,#1042,.T.);
#1445=ORIENTED_EDGE('',*,*,#1139,.T.);
#1446=ORIENTED_EDGE('',*,*,#1140,.T.);
#1447=ORIENTED_EDGE('',*,*,#1037,.T.);
#1448=ORIENTED_EDGE('',*,*,#1141,.T.);
#1449=ORIENTED_EDGE('',*,*,#1140,.F.);
#1450=ORIENTED_EDGE('',*,*,#1142,.F.);
#1451=ORIENTED_EDGE('',*,*,#1143,.F.);
#1452=ORIENTED_EDGE('',*,*,#1038,.F.);
#1453=ORIENTED_EDGE('',*,*,#1144,.T.);
#1454=ORIENTED_EDGE('',*,*,#1145,.T.);
#1455=ORIENTED_EDGE('',*,*,#1039,.T.);
#1456=ORIENTED_EDGE('',*,*,#1143,.T.);
#1457=ORIENTED_EDGE('',*,*,#1136,.F.);
#1458=ORIENTED_EDGE('',*,*,#1040,.F.);
#1459=ORIENTED_EDGE('',*,*,#1145,.F.);
#1460=ORIENTED_EDGE('',*,*,#1146,.F.);
#1461=ORIENTED_EDGE('',*,*,#1032,.F.);
#1462=ORIENTED_EDGE('',*,*,#1147,.F.);
#1463=ORIENTED_EDGE('',*,*,#1141,.F.);
#1464=ORIENTED_EDGE('',*,*,#1036,.F.);
#1465=ORIENTED_EDGE('',*,*,#1031,.F.);
#1466=ORIENTED_EDGE('',*,*,#1035,.T.);
#1467=ORIENTED_EDGE('',*,*,#1148,.F.);
#1468=ORIENTED_EDGE('',*,*,#1149,.T.);
#1469=ORIENTED_EDGE('',*,*,#1150,.F.);
#1470=ORIENTED_EDGE('',*,*,#1151,.T.);
#1471=ORIENTED_EDGE('',*,*,#1152,.F.);
#1472=ORIENTED_EDGE('',*,*,#1153,.T.);
#1473=ORIENTED_EDGE('',*,*,#1154,.F.);
#1474=ORIENTED_EDGE('',*,*,#1137,.T.);
#1475=ORIENTED_EDGE('',*,*,#1134,.F.);
#1476=ORIENTED_EDGE('',*,*,#1146,.T.);
#1477=ORIENTED_EDGE('',*,*,#1144,.F.);
#1478=ORIENTED_EDGE('',*,*,#1142,.T.);
#1479=ORIENTED_EDGE('',*,*,#1139,.F.);
#1480=ORIENTED_EDGE('',*,*,#1147,.T.);
#1481=ORIENTED_EDGE('',*,*,#1155,.T.);
#1482=ORIENTED_EDGE('',*,*,#1156,.T.);
#1483=ORIENTED_EDGE('',*,*,#1157,.T.);
#1484=ORIENTED_EDGE('',*,*,#1158,.T.);
#1485=ORIENTED_EDGE('',*,*,#1159,.T.);
#1486=ORIENTED_EDGE('',*,*,#1160,.T.);
#1487=ORIENTED_EDGE('',*,*,#1161,.T.);
#1488=ORIENTED_EDGE('',*,*,#1162,.T.);
#1489=ORIENTED_EDGE('',*,*,#1163,.T.);
#1490=ORIENTED_EDGE('',*,*,#1164,.T.);
#1491=ORIENTED_EDGE('',*,*,#1165,.T.);
#1492=ORIENTED_EDGE('',*,*,#1166,.T.);
#1493=ORIENTED_EDGE('',*,*,#1167,.T.);
#1494=ORIENTED_EDGE('',*,*,#1168,.T.);
#1495=ORIENTED_EDGE('',*,*,#1169,.T.);
#1496=ORIENTED_EDGE('',*,*,#1170,.T.);
#1497=ORIENTED_EDGE('',*,*,#1171,.T.);
#1498=ORIENTED_EDGE('',*,*,#1172,.T.);
#1499=ORIENTED_EDGE('',*,*,#1173,.T.);
#1500=ORIENTED_EDGE('',*,*,#1174,.T.);
#1501=ORIENTED_EDGE('',*,*,#1175,.T.);
#1502=ORIENTED_EDGE('',*,*,#1176,.T.);
#1503=ORIENTED_EDGE('',*,*,#1177,.T.);
#1504=ORIENTED_EDGE('',*,*,#1178,.T.);
#1505=ORIENTED_EDGE('',*,*,#1179,.T.);
#1506=ORIENTED_EDGE('',*,*,#1180,.T.);
#1507=ORIENTED_EDGE('',*,*,#1181,.T.);
#1508=ORIENTED_EDGE('',*,*,#1182,.T.);
#1509=ORIENTED_EDGE('',*,*,#1183,.T.);
#1510=ORIENTED_EDGE('',*,*,#1184,.T.);
#1511=ORIENTED_EDGE('',*,*,#1185,.T.);
#1512=ORIENTED_EDGE('',*,*,#1186,.T.);
#1513=ORIENTED_EDGE('',*,*,#1187,.T.);
#1514=ORIENTED_EDGE('',*,*,#1188,.T.);
#1515=ORIENTED_EDGE('',*,*,#1189,.T.);
#1516=ORIENTED_EDGE('',*,*,#1190,.T.);
#1517=ORIENTED_EDGE('',*,*,#1191,.T.);
#1518=ORIENTED_EDGE('',*,*,#1192,.T.);
#1519=ORIENTED_EDGE('',*,*,#1193,.T.);
#1520=ORIENTED_EDGE('',*,*,#1194,.T.);
#1521=ORIENTED_EDGE('',*,*,#1195,.T.);
#1522=ORIENTED_EDGE('',*,*,#1196,.T.);
#1523=ORIENTED_EDGE('',*,*,#1197,.T.);
#1524=ORIENTED_EDGE('',*,*,#1198,.T.);
#1525=ORIENTED_EDGE('',*,*,#1199,.T.);
#1526=ORIENTED_EDGE('',*,*,#1200,.T.);
#1527=ORIENTED_EDGE('',*,*,#1201,.T.);
#1528=ORIENTED_EDGE('',*,*,#1202,.T.);
#1529=ORIENTED_EDGE('',*,*,#1203,.T.);
#1530=ORIENTED_EDGE('',*,*,#1204,.T.);
#1531=ORIENTED_EDGE('',*,*,#1205,.T.);
#1532=ORIENTED_EDGE('',*,*,#1206,.T.);
#1533=ORIENTED_EDGE('',*,*,#1207,.T.);
#1534=ORIENTED_EDGE('',*,*,#1208,.T.);
#1535=ORIENTED_EDGE('',*,*,#1209,.T.);
#1536=ORIENTED_EDGE('',*,*,#1210,.T.);
#1537=ORIENTED_EDGE('',*,*,#1211,.T.);
#1538=ORIENTED_EDGE('',*,*,#1212,.T.);
#1539=ORIENTED_EDGE('',*,*,#1213,.T.);
#1540=ORIENTED_EDGE('',*,*,#1214,.T.);
#1541=ORIENTED_EDGE('',*,*,#1215,.T.);
#1542=ORIENTED_EDGE('',*,*,#1216,.F.);
#1543=ORIENTED_EDGE('',*,*,#1217,.T.);
#1544=ORIENTED_EDGE('',*,*,#1218,.F.);
#1545=ORIENTED_EDGE('',*,*,#1219,.T.);
#1546=ORIENTED_EDGE('',*,*,#1220,.F.);
#1547=ORIENTED_EDGE('',*,*,#1221,.T.);
#1548=ORIENTED_EDGE('',*,*,#1222,.F.);
#1549=ORIENTED_EDGE('',*,*,#1223,.T.);
#1550=ORIENTED_EDGE('',*,*,#1224,.T.);
#1551=ORIENTED_EDGE('',*,*,#1225,.T.);
#1552=ORIENTED_EDGE('',*,*,#1226,.T.);
#1553=ORIENTED_EDGE('',*,*,#1227,.T.);
#1554=ORIENTED_EDGE('',*,*,#1228,.T.);
#1555=ORIENTED_EDGE('',*,*,#1229,.T.);
#1556=ORIENTED_EDGE('',*,*,#1230,.T.);
#1557=ORIENTED_EDGE('',*,*,#1231,.T.);
#1558=ORIENTED_EDGE('',*,*,#1232,.T.);
#1559=ORIENTED_EDGE('',*,*,#1233,.T.);
#1560=ORIENTED_EDGE('',*,*,#1234,.T.);
#1561=ORIENTED_EDGE('',*,*,#1235,.T.);
#1562=ORIENTED_EDGE('',*,*,#1236,.T.);
#1563=ORIENTED_EDGE('',*,*,#1237,.T.);
#1564=ORIENTED_EDGE('',*,*,#1238,.T.);
#1565=ORIENTED_EDGE('',*,*,#1072,.T.);
#1566=ORIENTED_EDGE('',*,*,#1239,.T.);
#1567=ORIENTED_EDGE('',*,*,#1216,.T.);
#1568=ORIENTED_EDGE('',*,*,#1240,.T.);
#1569=ORIENTED_EDGE('',*,*,#1239,.F.);
#1570=ORIENTED_EDGE('',*,*,#1071,.T.);
#1571=ORIENTED_EDGE('',*,*,#1241,.F.);
#1572=ORIENTED_EDGE('',*,*,#1217,.F.);
#1573=ORIENTED_EDGE('',*,*,#1066,.T.);
#1574=ORIENTED_EDGE('',*,*,#1242,.T.);
#1575=ORIENTED_EDGE('',*,*,#1222,.T.);
#1576=ORIENTED_EDGE('',*,*,#1243,.T.);
#1577=ORIENTED_EDGE('',*,*,#1240,.F.);
#1578=ORIENTED_EDGE('',*,*,#1215,.F.);
#1579=ORIENTED_EDGE('',*,*,#1242,.F.);
#1580=ORIENTED_EDGE('',*,*,#1073,.T.);
#1581=ORIENTED_EDGE('',*,*,#1068,.T.);
#1582=ORIENTED_EDGE('',*,*,#1244,.T.);
#1583=ORIENTED_EDGE('',*,*,#1220,.T.);
#1584=ORIENTED_EDGE('',*,*,#1245,.T.);
#1585=ORIENTED_EDGE('',*,*,#1243,.F.);
#1586=ORIENTED_EDGE('',*,*,#1221,.F.);
#1587=ORIENTED_EDGE('',*,*,#1244,.F.);
#1588=ORIENTED_EDGE('',*,*,#1067,.T.);
#1589=ORIENTED_EDGE('',*,*,#1070,.T.);
#1590=ORIENTED_EDGE('',*,*,#1246,.T.);
#1591=ORIENTED_EDGE('',*,*,#1218,.T.);
#1592=ORIENTED_EDGE('',*,*,#1241,.T.);
#1593=ORIENTED_EDGE('',*,*,#1245,.F.);
#1594=ORIENTED_EDGE('',*,*,#1219,.F.);
#1595=ORIENTED_EDGE('',*,*,#1246,.F.);
#1596=ORIENTED_EDGE('',*,*,#1069,.T.);
#1597=ORIENTED_EDGE('',*,*,#1148,.T.);
#1598=ORIENTED_EDGE('',*,*,#1034,.T.);
#1599=ORIENTED_EDGE('',*,*,#1049,.T.);
#1600=ORIENTED_EDGE('',*,*,#1247,.T.);
#1601=ORIENTED_EDGE('',*,*,#1247,.F.);
#1602=ORIENTED_EDGE('',*,*,#1048,.T.);
#1603=ORIENTED_EDGE('',*,*,#1248,.F.);
#1604=ORIENTED_EDGE('',*,*,#1149,.F.);
#1605=ORIENTED_EDGE('',*,*,#1154,.T.);
#1606=ORIENTED_EDGE('',*,*,#1249,.T.);
#1607=ORIENTED_EDGE('',*,*,#1043,.T.);
#1608=ORIENTED_EDGE('',*,*,#1138,.T.);
#1609=ORIENTED_EDGE('',*,*,#1249,.F.);
#1610=ORIENTED_EDGE('',*,*,#1153,.F.);
#1611=ORIENTED_EDGE('',*,*,#1250,.F.);
#1612=ORIENTED_EDGE('',*,*,#1044,.T.);
#1613=ORIENTED_EDGE('',*,*,#1152,.T.);
#1614=ORIENTED_EDGE('',*,*,#1251,.T.);
#1615=ORIENTED_EDGE('',*,*,#1045,.T.);
#1616=ORIENTED_EDGE('',*,*,#1250,.T.);
#1617=ORIENTED_EDGE('',*,*,#1150,.T.);
#1618=ORIENTED_EDGE('',*,*,#1248,.T.);
#1619=ORIENTED_EDGE('',*,*,#1047,.T.);
#1620=ORIENTED_EDGE('',*,*,#1252,.T.);
#1621=ORIENTED_EDGE('',*,*,#1127,.T.);
#1622=ORIENTED_EDGE('',*,*,#1253,.T.);
#1623=ORIENTED_EDGE('',*,*,#1162,.F.);
#1624=ORIENTED_EDGE('',*,*,#1254,.F.);
#1625=ORIENTED_EDGE('',*,*,#1128,.T.);
#1626=ORIENTED_EDGE('',*,*,#1254,.T.);
#1627=ORIENTED_EDGE('',*,*,#1161,.F.);
#1628=ORIENTED_EDGE('',*,*,#1255,.F.);
#1629=ORIENTED_EDGE('',*,*,#1129,.T.);
#1630=ORIENTED_EDGE('',*,*,#1255,.T.);
#1631=ORIENTED_EDGE('',*,*,#1160,.F.);
#1632=ORIENTED_EDGE('',*,*,#1256,.F.);
#1633=ORIENTED_EDGE('',*,*,#1130,.T.);
#1634=ORIENTED_EDGE('',*,*,#1256,.T.);
#1635=ORIENTED_EDGE('',*,*,#1159,.F.);
#1636=ORIENTED_EDGE('',*,*,#1257,.F.);
#1637=ORIENTED_EDGE('',*,*,#1131,.T.);
#1638=ORIENTED_EDGE('',*,*,#1257,.T.);
#1639=ORIENTED_EDGE('',*,*,#1158,.F.);
#1640=ORIENTED_EDGE('',*,*,#1258,.F.);
#1641=ORIENTED_EDGE('',*,*,#1132,.T.);
#1642=ORIENTED_EDGE('',*,*,#1258,.T.);
#1643=ORIENTED_EDGE('',*,*,#1157,.F.);
#1644=ORIENTED_EDGE('',*,*,#1259,.F.);
#1645=ORIENTED_EDGE('',*,*,#1133,.T.);
#1646=ORIENTED_EDGE('',*,*,#1259,.T.);
#1647=ORIENTED_EDGE('',*,*,#1156,.F.);
#1648=ORIENTED_EDGE('',*,*,#1260,.F.);
#1649=ORIENTED_EDGE('',*,*,#1126,.T.);
#1650=ORIENTED_EDGE('',*,*,#1260,.T.);
#1651=ORIENTED_EDGE('',*,*,#1155,.F.);
#1652=ORIENTED_EDGE('',*,*,#1253,.F.);
#1653=ORIENTED_EDGE('',*,*,#1119,.T.);
#1654=ORIENTED_EDGE('',*,*,#1261,.T.);
#1655=ORIENTED_EDGE('',*,*,#1170,.F.);
#1656=ORIENTED_EDGE('',*,*,#1262,.F.);
#1657=ORIENTED_EDGE('',*,*,#1120,.T.);
#1658=ORIENTED_EDGE('',*,*,#1262,.T.);
#1659=ORIENTED_EDGE('',*,*,#1169,.F.);
#1660=ORIENTED_EDGE('',*,*,#1263,.F.);
#1661=ORIENTED_EDGE('',*,*,#1121,.T.);
#1662=ORIENTED_EDGE('',*,*,#1263,.T.);
#1663=ORIENTED_EDGE('',*,*,#1168,.F.);
#1664=ORIENTED_EDGE('',*,*,#1264,.F.);
#1665=ORIENTED_EDGE('',*,*,#1122,.T.);
#1666=ORIENTED_EDGE('',*,*,#1264,.T.);
#1667=ORIENTED_EDGE('',*,*,#1167,.F.);
#1668=ORIENTED_EDGE('',*,*,#1265,.F.);
#1669=ORIENTED_EDGE('',*,*,#1123,.T.);
#1670=ORIENTED_EDGE('',*,*,#1265,.T.);
#1671=ORIENTED_EDGE('',*,*,#1166,.F.);
#1672=ORIENTED_EDGE('',*,*,#1266,.F.);
#1673=ORIENTED_EDGE('',*,*,#1124,.T.);
#1674=ORIENTED_EDGE('',*,*,#1266,.T.);
#1675=ORIENTED_EDGE('',*,*,#1165,.F.);
#1676=ORIENTED_EDGE('',*,*,#1267,.F.);
#1677=ORIENTED_EDGE('',*,*,#1125,.T.);
#1678=ORIENTED_EDGE('',*,*,#1267,.T.);
#1679=ORIENTED_EDGE('',*,*,#1164,.F.);
#1680=ORIENTED_EDGE('',*,*,#1268,.F.);
#1681=ORIENTED_EDGE('',*,*,#1118,.T.);
#1682=ORIENTED_EDGE('',*,*,#1268,.T.);
#1683=ORIENTED_EDGE('',*,*,#1163,.F.);
#1684=ORIENTED_EDGE('',*,*,#1261,.F.);
#1685=ORIENTED_EDGE('',*,*,#1111,.T.);
#1686=ORIENTED_EDGE('',*,*,#1269,.T.);
#1687=ORIENTED_EDGE('',*,*,#1178,.F.);
#1688=ORIENTED_EDGE('',*,*,#1270,.F.);
#1689=ORIENTED_EDGE('',*,*,#1112,.T.);
#1690=ORIENTED_EDGE('',*,*,#1270,.T.);
#1691=ORIENTED_EDGE('',*,*,#1177,.F.);
#1692=ORIENTED_EDGE('',*,*,#1271,.F.);
#1693=ORIENTED_EDGE('',*,*,#1113,.T.);
#1694=ORIENTED_EDGE('',*,*,#1271,.T.);
#1695=ORIENTED_EDGE('',*,*,#1176,.F.);
#1696=ORIENTED_EDGE('',*,*,#1272,.F.);
#1697=ORIENTED_EDGE('',*,*,#1114,.T.);
#1698=ORIENTED_EDGE('',*,*,#1272,.T.);
#1699=ORIENTED_EDGE('',*,*,#1175,.F.);
#1700=ORIENTED_EDGE('',*,*,#1273,.F.);
#1701=ORIENTED_EDGE('',*,*,#1115,.T.);
#1702=ORIENTED_EDGE('',*,*,#1273,.T.);
#1703=ORIENTED_EDGE('',*,*,#1174,.F.);
#1704=ORIENTED_EDGE('',*,*,#1274,.F.);
#1705=ORIENTED_EDGE('',*,*,#1116,.T.);
#1706=ORIENTED_EDGE('',*,*,#1274,.T.);
#1707=ORIENTED_EDGE('',*,*,#1173,.F.);
#1708=ORIENTED_EDGE('',*,*,#1275,.F.);
#1709=ORIENTED_EDGE('',*,*,#1117,.T.);
#1710=ORIENTED_EDGE('',*,*,#1275,.T.);
#1711=ORIENTED_EDGE('',*,*,#1172,.F.);
#1712=ORIENTED_EDGE('',*,*,#1276,.F.);
#1713=ORIENTED_EDGE('',*,*,#1110,.T.);
#1714=ORIENTED_EDGE('',*,*,#1276,.T.);
#1715=ORIENTED_EDGE('',*,*,#1171,.F.);
#1716=ORIENTED_EDGE('',*,*,#1269,.F.);
#1717=ORIENTED_EDGE('',*,*,#1103,.T.);
#1718=ORIENTED_EDGE('',*,*,#1277,.T.);
#1719=ORIENTED_EDGE('',*,*,#1186,.F.);
#1720=ORIENTED_EDGE('',*,*,#1278,.F.);
#1721=ORIENTED_EDGE('',*,*,#1104,.T.);
#1722=ORIENTED_EDGE('',*,*,#1278,.T.);
#1723=ORIENTED_EDGE('',*,*,#1185,.F.);
#1724=ORIENTED_EDGE('',*,*,#1279,.F.);
#1725=ORIENTED_EDGE('',*,*,#1105,.T.);
#1726=ORIENTED_EDGE('',*,*,#1279,.T.);
#1727=ORIENTED_EDGE('',*,*,#1184,.F.);
#1728=ORIENTED_EDGE('',*,*,#1280,.F.);
#1729=ORIENTED_EDGE('',*,*,#1106,.T.);
#1730=ORIENTED_EDGE('',*,*,#1280,.T.);
#1731=ORIENTED_EDGE('',*,*,#1183,.F.);
#1732=ORIENTED_EDGE('',*,*,#1281,.F.);
#1733=ORIENTED_EDGE('',*,*,#1107,.T.);
#1734=ORIENTED_EDGE('',*,*,#1281,.T.);
#1735=ORIENTED_EDGE('',*,*,#1182,.F.);
#1736=ORIENTED_EDGE('',*,*,#1282,.F.);
#1737=ORIENTED_EDGE('',*,*,#1108,.T.);
#1738=ORIENTED_EDGE('',*,*,#1282,.T.);
#1739=ORIENTED_EDGE('',*,*,#1181,.F.);
#1740=ORIENTED_EDGE('',*,*,#1283,.F.);
#1741=ORIENTED_EDGE('',*,*,#1109,.T.);
#1742=ORIENTED_EDGE('',*,*,#1283,.T.);
#1743=ORIENTED_EDGE('',*,*,#1180,.F.);
#1744=ORIENTED_EDGE('',*,*,#1284,.F.);
#1745=ORIENTED_EDGE('',*,*,#1102,.T.);
#1746=ORIENTED_EDGE('',*,*,#1284,.T.);
#1747=ORIENTED_EDGE('',*,*,#1179,.F.);
#1748=ORIENTED_EDGE('',*,*,#1277,.F.);
#1749=ORIENTED_EDGE('',*,*,#1187,.F.);
#1750=ORIENTED_EDGE('',*,*,#1285,.T.);
#1751=ORIENTED_EDGE('',*,*,#1101,.T.);
#1752=ORIENTED_EDGE('',*,*,#1285,.F.);
#1753=ORIENTED_EDGE('',*,*,#1094,.T.);
#1754=ORIENTED_EDGE('',*,*,#1286,.T.);
#1755=ORIENTED_EDGE('',*,*,#1195,.F.);
#1756=ORIENTED_EDGE('',*,*,#1287,.F.);
#1757=ORIENTED_EDGE('',*,*,#1095,.T.);
#1758=ORIENTED_EDGE('',*,*,#1287,.T.);
#1759=ORIENTED_EDGE('',*,*,#1194,.F.);
#1760=ORIENTED_EDGE('',*,*,#1288,.F.);
#1761=ORIENTED_EDGE('',*,*,#1096,.T.);
#1762=ORIENTED_EDGE('',*,*,#1288,.T.);
#1763=ORIENTED_EDGE('',*,*,#1193,.F.);
#1764=ORIENTED_EDGE('',*,*,#1289,.F.);
#1765=ORIENTED_EDGE('',*,*,#1097,.T.);
#1766=ORIENTED_EDGE('',*,*,#1289,.T.);
#1767=ORIENTED_EDGE('',*,*,#1192,.F.);
#1768=ORIENTED_EDGE('',*,*,#1290,.F.);
#1769=ORIENTED_EDGE('',*,*,#1098,.T.);
#1770=ORIENTED_EDGE('',*,*,#1290,.T.);
#1771=ORIENTED_EDGE('',*,*,#1191,.F.);
#1772=ORIENTED_EDGE('',*,*,#1291,.F.);
#1773=ORIENTED_EDGE('',*,*,#1099,.T.);
#1774=ORIENTED_EDGE('',*,*,#1291,.T.);
#1775=ORIENTED_EDGE('',*,*,#1190,.F.);
#1776=ORIENTED_EDGE('',*,*,#1292,.F.);
#1777=ORIENTED_EDGE('',*,*,#1100,.T.);
#1778=ORIENTED_EDGE('',*,*,#1292,.T.);
#1779=ORIENTED_EDGE('',*,*,#1189,.F.);
#1780=ORIENTED_EDGE('',*,*,#1293,.F.);
#1781=ORIENTED_EDGE('',*,*,#1093,.T.);
#1782=ORIENTED_EDGE('',*,*,#1293,.T.);
#1783=ORIENTED_EDGE('',*,*,#1188,.F.);
#1784=ORIENTED_EDGE('',*,*,#1286,.F.);
#1785=ORIENTED_EDGE('',*,*,#1196,.F.);
#1786=ORIENTED_EDGE('',*,*,#1294,.T.);
#1787=ORIENTED_EDGE('',*,*,#1092,.T.);
#1788=ORIENTED_EDGE('',*,*,#1294,.F.);
#1789=ORIENTED_EDGE('',*,*,#1085,.T.);
#1790=ORIENTED_EDGE('',*,*,#1295,.T.);
#1791=ORIENTED_EDGE('',*,*,#1204,.F.);
#1792=ORIENTED_EDGE('',*,*,#1296,.F.);
#1793=ORIENTED_EDGE('',*,*,#1086,.T.);
#1794=ORIENTED_EDGE('',*,*,#1296,.T.);
#1795=ORIENTED_EDGE('',*,*,#1203,.F.);
#1796=ORIENTED_EDGE('',*,*,#1297,.F.);
#1797=ORIENTED_EDGE('',*,*,#1087,.T.);
#1798=ORIENTED_EDGE('',*,*,#1297,.T.);
#1799=ORIENTED_EDGE('',*,*,#1202,.F.);
#1800=ORIENTED_EDGE('',*,*,#1298,.F.);
#1801=ORIENTED_EDGE('',*,*,#1088,.T.);
#1802=ORIENTED_EDGE('',*,*,#1298,.T.);
#1803=ORIENTED_EDGE('',*,*,#1201,.F.);
#1804=ORIENTED_EDGE('',*,*,#1299,.F.);
#1805=ORIENTED_EDGE('',*,*,#1089,.T.);
#1806=ORIENTED_EDGE('',*,*,#1299,.T.);
#1807=ORIENTED_EDGE('',*,*,#1200,.F.);
#1808=ORIENTED_EDGE('',*,*,#1300,.F.);
#1809=ORIENTED_EDGE('',*,*,#1090,.T.);
#1810=ORIENTED_EDGE('',*,*,#1300,.T.);
#1811=ORIENTED_EDGE('',*,*,#1199,.F.);
#1812=ORIENTED_EDGE('',*,*,#1301,.F.);
#1813=ORIENTED_EDGE('',*,*,#1091,.T.);
#1814=ORIENTED_EDGE('',*,*,#1301,.T.);
#1815=ORIENTED_EDGE('',*,*,#1198,.F.);
#1816=ORIENTED_EDGE('',*,*,#1302,.F.);
#1817=ORIENTED_EDGE('',*,*,#1084,.T.);
#1818=ORIENTED_EDGE('',*,*,#1302,.T.);
#1819=ORIENTED_EDGE('',*,*,#1197,.F.);
#1820=ORIENTED_EDGE('',*,*,#1295,.F.);
#1821=ORIENTED_EDGE('',*,*,#1077,.T.);
#1822=ORIENTED_EDGE('',*,*,#1303,.T.);
#1823=ORIENTED_EDGE('',*,*,#1212,.F.);
#1824=ORIENTED_EDGE('',*,*,#1304,.F.);
#1825=ORIENTED_EDGE('',*,*,#1078,.T.);
#1826=ORIENTED_EDGE('',*,*,#1304,.T.);
#1827=ORIENTED_EDGE('',*,*,#1211,.F.);
#1828=ORIENTED_EDGE('',*,*,#1305,.F.);
#1829=ORIENTED_EDGE('',*,*,#1079,.T.);
#1830=ORIENTED_EDGE('',*,*,#1305,.T.);
#1831=ORIENTED_EDGE('',*,*,#1210,.F.);
#1832=ORIENTED_EDGE('',*,*,#1306,.F.);
#1833=ORIENTED_EDGE('',*,*,#1080,.T.);
#1834=ORIENTED_EDGE('',*,*,#1306,.T.);
#1835=ORIENTED_EDGE('',*,*,#1209,.F.);
#1836=ORIENTED_EDGE('',*,*,#1307,.F.);
#1837=ORIENTED_EDGE('',*,*,#1081,.T.);
#1838=ORIENTED_EDGE('',*,*,#1307,.T.);
#1839=ORIENTED_EDGE('',*,*,#1208,.F.);
#1840=ORIENTED_EDGE('',*,*,#1308,.F.);
#1841=ORIENTED_EDGE('',*,*,#1082,.T.);
#1842=ORIENTED_EDGE('',*,*,#1308,.T.);
#1843=ORIENTED_EDGE('',*,*,#1207,.F.);
#1844=ORIENTED_EDGE('',*,*,#1309,.F.);
#1845=ORIENTED_EDGE('',*,*,#1083,.T.);
#1846=ORIENTED_EDGE('',*,*,#1309,.T.);
#1847=ORIENTED_EDGE('',*,*,#1206,.F.);
#1848=ORIENTED_EDGE('',*,*,#1310,.F.);
#1849=ORIENTED_EDGE('',*,*,#1076,.T.);
#1850=ORIENTED_EDGE('',*,*,#1310,.T.);
#1851=ORIENTED_EDGE('',*,*,#1205,.F.);
#1852=ORIENTED_EDGE('',*,*,#1303,.F.);
#1853=ORIENTED_EDGE('',*,*,#1213,.F.);
#1854=ORIENTED_EDGE('',*,*,#1311,.T.);
#1855=ORIENTED_EDGE('',*,*,#1075,.T.);
#1856=ORIENTED_EDGE('',*,*,#1311,.F.);
#1857=ORIENTED_EDGE('',*,*,#1214,.F.);
#1858=ORIENTED_EDGE('',*,*,#1312,.T.);
#1859=ORIENTED_EDGE('',*,*,#1074,.T.);
#1860=ORIENTED_EDGE('',*,*,#1312,.F.);
#1861=ORIENTED_EDGE('',*,*,#1059,.T.);
#1862=ORIENTED_EDGE('',*,*,#1313,.T.);
#1863=ORIENTED_EDGE('',*,*,#1230,.F.);
#1864=ORIENTED_EDGE('',*,*,#1314,.F.);
#1865=ORIENTED_EDGE('',*,*,#1060,.T.);
#1866=ORIENTED_EDGE('',*,*,#1314,.T.);
#1867=ORIENTED_EDGE('',*,*,#1229,.F.);
#1868=ORIENTED_EDGE('',*,*,#1315,.F.);
#1869=ORIENTED_EDGE('',*,*,#1061,.T.);
#1870=ORIENTED_EDGE('',*,*,#1315,.T.);
#1871=ORIENTED_EDGE('',*,*,#1228,.F.);
#1872=ORIENTED_EDGE('',*,*,#1316,.F.);
#1873=ORIENTED_EDGE('',*,*,#1062,.T.);
#1874=ORIENTED_EDGE('',*,*,#1316,.T.);
#1875=ORIENTED_EDGE('',*,*,#1227,.F.);
#1876=ORIENTED_EDGE('',*,*,#1317,.F.);
#1877=ORIENTED_EDGE('',*,*,#1063,.T.);
#1878=ORIENTED_EDGE('',*,*,#1317,.T.);
#1879=ORIENTED_EDGE('',*,*,#1226,.F.);
#1880=ORIENTED_EDGE('',*,*,#1318,.F.);
#1881=ORIENTED_EDGE('',*,*,#1064,.T.);
#1882=ORIENTED_EDGE('',*,*,#1318,.T.);
#1883=ORIENTED_EDGE('',*,*,#1225,.F.);
#1884=ORIENTED_EDGE('',*,*,#1319,.F.);
#1885=ORIENTED_EDGE('',*,*,#1065,.T.);
#1886=ORIENTED_EDGE('',*,*,#1319,.T.);
#1887=ORIENTED_EDGE('',*,*,#1224,.F.);
#1888=ORIENTED_EDGE('',*,*,#1320,.F.);
#1889=ORIENTED_EDGE('',*,*,#1058,.T.);
#1890=ORIENTED_EDGE('',*,*,#1320,.T.);
#1891=ORIENTED_EDGE('',*,*,#1223,.F.);
#1892=ORIENTED_EDGE('',*,*,#1313,.F.);
#1893=ORIENTED_EDGE('',*,*,#1051,.T.);
#1894=ORIENTED_EDGE('',*,*,#1321,.T.);
#1895=ORIENTED_EDGE('',*,*,#1238,.F.);
#1896=ORIENTED_EDGE('',*,*,#1322,.F.);
#1897=ORIENTED_EDGE('',*,*,#1052,.T.);
#1898=ORIENTED_EDGE('',*,*,#1322,.T.);
#1899=ORIENTED_EDGE('',*,*,#1237,.F.);
#1900=ORIENTED_EDGE('',*,*,#1323,.F.);
#1901=ORIENTED_EDGE('',*,*,#1053,.T.);
#1902=ORIENTED_EDGE('',*,*,#1323,.T.);
#1903=ORIENTED_EDGE('',*,*,#1236,.F.);
#1904=ORIENTED_EDGE('',*,*,#1324,.F.);
#1905=ORIENTED_EDGE('',*,*,#1054,.T.);
#1906=ORIENTED_EDGE('',*,*,#1324,.T.);
#1907=ORIENTED_EDGE('',*,*,#1235,.F.);
#1908=ORIENTED_EDGE('',*,*,#1325,.F.);
#1909=ORIENTED_EDGE('',*,*,#1055,.T.);
#1910=ORIENTED_EDGE('',*,*,#1325,.T.);
#1911=ORIENTED_EDGE('',*,*,#1234,.F.);
#1912=ORIENTED_EDGE('',*,*,#1326,.F.);
#1913=ORIENTED_EDGE('',*,*,#1056,.T.);
#1914=ORIENTED_EDGE('',*,*,#1326,.T.);
#1915=ORIENTED_EDGE('',*,*,#1233,.F.);
#1916=ORIENTED_EDGE('',*,*,#1327,.F.);
#1917=ORIENTED_EDGE('',*,*,#1057,.T.);
#1918=ORIENTED_EDGE('',*,*,#1327,.T.);
#1919=ORIENTED_EDGE('',*,*,#1232,.F.);
#1920=ORIENTED_EDGE('',*,*,#1328,.F.);
#1921=ORIENTED_EDGE('',*,*,#1050,.T.);
#1922=ORIENTED_EDGE('',*,*,#1328,.T.);
#1923=ORIENTED_EDGE('',*,*,#1231,.F.);
#1924=ORIENTED_EDGE('',*,*,#1321,.F.);
#1925=ORIENTED_EDGE('',*,*,#1251,.F.);
#1926=ORIENTED_EDGE('',*,*,#1151,.F.);
#1927=ORIENTED_EDGE('',*,*,#1252,.F.);
#1928=ORIENTED_EDGE('',*,*,#1046,.T.);
#1929=CYLINDRICAL_SURFACE('',#2098,2.);
#1930=CYLINDRICAL_SURFACE('',#2154,2.);
#1931=CYLINDRICAL_SURFACE('',#2157,2.);
#1932=CYLINDRICAL_SURFACE('',#2160,2.);
#1933=CYLINDRICAL_SURFACE('',#2213,1.);
#1934=CYLINDRICAL_SURFACE('',#2215,1.);
#1935=CYLINDRICAL_SURFACE('',#2217,1.);
#1936=CYLINDRICAL_SURFACE('',#2219,1.);
#1937=CYLINDRICAL_SURFACE('',#2221,5.);
#1938=CYLINDRICAL_SURFACE('',#2223,5.);
#1939=CYLINDRICAL_SURFACE('',#2225,5.);
#1940=CYLINDRICAL_SURFACE('',#2226,5.);
#1941=CYLINDRICAL_SURFACE('',#2228,0.499999999999998);
#1942=CYLINDRICAL_SURFACE('',#2230,0.500000000000003);
#1943=CYLINDRICAL_SURFACE('',#2232,0.499999999999998);
#1944=CYLINDRICAL_SURFACE('',#2234,0.499999999999998);
#1945=CYLINDRICAL_SURFACE('',#2236,0.499999999999998);
#1946=CYLINDRICAL_SURFACE('',#2238,0.499999999999998);
#1947=CYLINDRICAL_SURFACE('',#2240,0.499999999999998);
#1948=CYLINDRICAL_SURFACE('',#2242,0.5);
#1949=CYLINDRICAL_SURFACE('',#2244,0.5);
#1950=CYLINDRICAL_SURFACE('',#2246,0.500000000000003);
#1951=CYLINDRICAL_SURFACE('',#2248,0.499999999999998);
#1952=CYLINDRICAL_SURFACE('',#2250,0.499999999999998);
#1953=CYLINDRICAL_SURFACE('',#2252,0.499999999999998);
#1954=CYLINDRICAL_SURFACE('',#2254,0.499999999999998);
#1955=CYLINDRICAL_SURFACE('',#2256,0.500000000000003);
#1956=CYLINDRICAL_SURFACE('',#2258,0.499999999999998);
#1957=CYLINDRICAL_SURFACE('',#2259,1.7);
#1958=CYLINDRICAL_SURFACE('',#2261,0.499999999999998);
#1959=CYLINDRICAL_SURFACE('',#2263,0.499999999999998);
#1960=CYLINDRICAL_SURFACE('',#2265,0.499999999999998);
#1961=CYLINDRICAL_SURFACE('',#2267,0.499999999999998);
#1962=CYLINDRICAL_SURFACE('',#2268,1.7);
#1963=CYLINDRICAL_SURFACE('',#2270,0.499999999999998);
#1964=CYLINDRICAL_SURFACE('',#2272,0.500000000000003);
#1965=CYLINDRICAL_SURFACE('',#2274,0.500000000000003);
#1966=CYLINDRICAL_SURFACE('',#2276,0.499999999999998);
#1967=CYLINDRICAL_SURFACE('',#2278,0.499999999999998);
#1968=CYLINDRICAL_SURFACE('',#2280,0.499999999999998);
#1969=CYLINDRICAL_SURFACE('',#2282,0.500000000000003);
#1970=CYLINDRICAL_SURFACE('',#2284,0.5);
#1971=CYLINDRICAL_SURFACE('',#2285,1.7);
#1972=CYLINDRICAL_SURFACE('',#2286,1.70000000000001);
#1973=CYLINDRICAL_SURFACE('',#2288,0.5);
#1974=CYLINDRICAL_SURFACE('',#2290,0.499999999999998);
#1975=CYLINDRICAL_SURFACE('',#2292,0.499999999999998);
#1976=CYLINDRICAL_SURFACE('',#2294,0.5);
#1977=CYLINDRICAL_SURFACE('',#2296,0.5);
#1978=CYLINDRICAL_SURFACE('',#2298,0.499999999999998);
#1979=CYLINDRICAL_SURFACE('',#2300,0.499999999999998);
#1980=CYLINDRICAL_SURFACE('',#2302,0.499999999999998);
#1981=ADVANCED_FACE('',(#101),#1929,.T.);
#1982=ADVANCED_FACE('',(#102),#51,.T.);
#1983=ADVANCED_FACE('',(#103,#23,#24,#25,#26,#27,#28,#29,#30,#31,#32,#33,
#34,#35,#36),#52,.F.);
#1984=ADVANCED_FACE('',(#104),#1930,.T.);
#1985=ADVANCED_FACE('',(#105),#53,.T.);
#1986=ADVANCED_FACE('',(#106),#1931,.F.);
#1987=ADVANCED_FACE('',(#107),#54,.F.);
#1988=ADVANCED_FACE('',(#108),#1932,.F.);
#1989=ADVANCED_FACE('',(#109),#55,.F.);
#1990=ADVANCED_FACE('',(#110),#56,.F.);
#1991=ADVANCED_FACE('',(#111,#37,#38,#39,#40,#41,#42,#43,#44,#45,#46,#47,
#48,#49,#50),#57,.T.);
#1992=ADVANCED_FACE('',(#112),#1933,.F.);
#1993=ADVANCED_FACE('',(#113),#58,.T.);
#1994=ADVANCED_FACE('',(#114),#1934,.F.);
#1995=ADVANCED_FACE('',(#115),#59,.T.);
#1996=ADVANCED_FACE('',(#116),#1935,.F.);
#1997=ADVANCED_FACE('',(#117),#60,.T.);
#1998=ADVANCED_FACE('',(#118),#1936,.F.);
#1999=ADVANCED_FACE('',(#119),#61,.T.);
#2000=ADVANCED_FACE('',(#120),#1937,.T.);
#2001=ADVANCED_FACE('',(#121),#62,.T.);
#2002=ADVANCED_FACE('',(#122),#1938,.T.);
#2003=ADVANCED_FACE('',(#123),#63,.T.);
#2004=ADVANCED_FACE('',(#124),#1939,.T.);
#2005=ADVANCED_FACE('',(#125),#1940,.T.);
#2006=ADVANCED_FACE('',(#126),#64,.T.);
#2007=ADVANCED_FACE('',(#127),#1941,.F.);
#2008=ADVANCED_FACE('',(#128),#65,.T.);
#2009=ADVANCED_FACE('',(#129),#1942,.F.);
#2010=ADVANCED_FACE('',(#130),#66,.T.);
#2011=ADVANCED_FACE('',(#131),#1943,.F.);
#2012=ADVANCED_FACE('',(#132),#67,.T.);
#2013=ADVANCED_FACE('',(#133),#1944,.F.);
#2014=ADVANCED_FACE('',(#134),#68,.T.);
#2015=ADVANCED_FACE('',(#135),#1945,.F.);
#2016=ADVANCED_FACE('',(#136),#69,.T.);
#2017=ADVANCED_FACE('',(#137),#1946,.F.);
#2018=ADVANCED_FACE('',(#138),#70,.T.);
#2019=ADVANCED_FACE('',(#139),#1947,.F.);
#2020=ADVANCED_FACE('',(#140),#71,.T.);
#2021=ADVANCED_FACE('',(#141),#1948,.F.);
#2022=ADVANCED_FACE('',(#142),#72,.T.);
#2023=ADVANCED_FACE('',(#143),#1949,.F.);
#2024=ADVANCED_FACE('',(#144),#73,.T.);
#2025=ADVANCED_FACE('',(#145),#1950,.F.);
#2026=ADVANCED_FACE('',(#146),#74,.T.);
#2027=ADVANCED_FACE('',(#147),#1951,.F.);
#2028=ADVANCED_FACE('',(#148),#75,.T.);
#2029=ADVANCED_FACE('',(#149),#1952,.F.);
#2030=ADVANCED_FACE('',(#150),#76,.T.);
#2031=ADVANCED_FACE('',(#151),#1953,.F.);
#2032=ADVANCED_FACE('',(#152),#77,.T.);
#2033=ADVANCED_FACE('',(#153),#1954,.F.);
#2034=ADVANCED_FACE('',(#154),#78,.T.);
#2035=ADVANCED_FACE('',(#155),#1955,.F.);
#2036=ADVANCED_FACE('',(#156),#79,.T.);
#2037=ADVANCED_FACE('',(#157),#1956,.F.);
#2038=ADVANCED_FACE('',(#158),#1957,.F.);
#2039=ADVANCED_FACE('',(#159),#80,.T.);
#2040=ADVANCED_FACE('',(#160),#1958,.F.);
#2041=ADVANCED_FACE('',(#161),#81,.T.);
#2042=ADVANCED_FACE('',(#162),#1959,.F.);
#2043=ADVANCED_FACE('',(#163),#82,.T.);
#2044=ADVANCED_FACE('',(#164),#1960,.F.);
#2045=ADVANCED_FACE('',(#165),#83,.T.);
#2046=ADVANCED_FACE('',(#166),#1961,.F.);
#2047=ADVANCED_FACE('',(#167),#1962,.F.);
#2048=ADVANCED_FACE('',(#168),#84,.T.);
#2049=ADVANCED_FACE('',(#169),#1963,.F.);
#2050=ADVANCED_FACE('',(#170),#85,.T.);
#2051=ADVANCED_FACE('',(#171),#1964,.F.);
#2052=ADVANCED_FACE('',(#172),#86,.T.);
#2053=ADVANCED_FACE('',(#173),#1965,.F.);
#2054=ADVANCED_FACE('',(#174),#87,.T.);
#2055=ADVANCED_FACE('',(#175),#1966,.F.);
#2056=ADVANCED_FACE('',(#176),#88,.T.);
#2057=ADVANCED_FACE('',(#177),#1967,.F.);
#2058=ADVANCED_FACE('',(#178),#89,.T.);
#2059=ADVANCED_FACE('',(#179),#1968,.F.);
#2060=ADVANCED_FACE('',(#180),#90,.T.);
#2061=ADVANCED_FACE('',(#181),#1969,.F.);
#2062=ADVANCED_FACE('',(#182),#91,.T.);
#2063=ADVANCED_FACE('',(#183),#1970,.F.);
#2064=ADVANCED_FACE('',(#184),#1971,.F.);
#2065=ADVANCED_FACE('',(#185),#1972,.F.);
#2066=ADVANCED_FACE('',(#186),#92,.T.);
#2067=ADVANCED_FACE('',(#187),#1973,.F.);
#2068=ADVANCED_FACE('',(#188),#93,.T.);
#2069=ADVANCED_FACE('',(#189),#1974,.F.);
#2070=ADVANCED_FACE('',(#190),#94,.T.);
#2071=ADVANCED_FACE('',(#191),#1975,.F.);
#2072=ADVANCED_FACE('',(#192),#95,.T.);
#2073=ADVANCED_FACE('',(#193),#1976,.F.);
#2074=ADVANCED_FACE('',(#194),#96,.T.);
#2075=ADVANCED_FACE('',(#195),#1977,.F.);
#2076=ADVANCED_FACE('',(#196),#97,.T.);
#2077=ADVANCED_FACE('',(#197),#1978,.F.);
#2078=ADVANCED_FACE('',(#198),#98,.T.);
#2079=ADVANCED_FACE('',(#199),#1979,.F.);
#2080=ADVANCED_FACE('',(#200),#99,.T.);
#2081=ADVANCED_FACE('',(#201),#1980,.F.);
#2082=ADVANCED_FACE('',(#202),#100,.T.);
#2083=CLOSED_SHELL('',(#1981,#1982,#1983,#1984,#1985,#1986,#1987,#1988,
#1989,#1990,#1991,#1992,#1993,#1994,#1995,#1996,#1997,#1998,#1999,#2000,
#2001,#2002,#2003,#2004,#2005,#2006,#2007,#2008,#2009,#2010,#2011,#2012,
#2013,#2014,#2015,#2016,#2017,#2018,#2019,#2020,#2021,#2022,#2023,#2024,
#2025,#2026,#2027,#2028,#2029,#2030,#2031,#2032,#2033,#2034,#2035,#2036,
#2037,#2038,#2039,#2040,#2041,#2042,#2043,#2044,#2045,#2046,#2047,#2048,
#2049,#2050,#2051,#2052,#2053,#2054,#2055,#2056,#2057,#2058,#2059,#2060,
#2061,#2062,#2063,#2064,#2065,#2066,#2067,#2068,#2069,#2070,#2071,#2072,
#2073,#2074,#2075,#2076,#2077,#2078,#2079,#2080,#2081,#2082));
#2084=DERIVED_UNIT_ELEMENT(#2086,1.);
#2085=DERIVED_UNIT_ELEMENT(#3535,-3.);
#2086=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#2087=DERIVED_UNIT((#2084,#2085));
#2088=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#2087);
#2089=PROPERTY_DEFINITION_REPRESENTATION(#2094,#2091);
#2090=PROPERTY_DEFINITION_REPRESENTATION(#2095,#2092);
#2091=REPRESENTATION('material name',(#2093),#3532);
#2092=REPRESENTATION('density',(#2088),#3532);
#2093=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#2094=PROPERTY_DEFINITION('material property','material name',#3547);
#2095=PROPERTY_DEFINITION('material property','density of part',#3547);
#2096=AXIS2_PLACEMENT_3D('',#2922,#2306,#2307);
#2097=AXIS2_PLACEMENT_3D('',#2923,#2308,#2309);
#2098=AXIS2_PLACEMENT_3D('',#2924,#2310,#2311);
#2099=AXIS2_PLACEMENT_3D('',#2927,#2312,#2313);
#2100=AXIS2_PLACEMENT_3D('',#2931,#2315,#2316);
#2101=AXIS2_PLACEMENT_3D('',#2933,#2318,#2319);
#2102=AXIS2_PLACEMENT_3D('',#2939,#2323,#2324);
#2103=AXIS2_PLACEMENT_3D('',#2943,#2326,#2327);
#2104=AXIS2_PLACEMENT_3D('',#2947,#2329,#2330);
#2105=AXIS2_PLACEMENT_3D('',#2951,#2332,#2333);
#2106=AXIS2_PLACEMENT_3D('',#2955,#2335,#2336);
#2107=AXIS2_PLACEMENT_3D('',#2959,#2338,#2339);
#2108=AXIS2_PLACEMENT_3D('',#2963,#2341,#2342);
#2109=AXIS2_PLACEMENT_3D('',#2966,#2344,#2345);
#2110=AXIS2_PLACEMENT_3D('',#2969,#2346,#2347);
#2111=AXIS2_PLACEMENT_3D('',#2973,#2349,#2350);
#2112=AXIS2_PLACEMENT_3D('',#2977,#2352,#2353);
#2113=AXIS2_PLACEMENT_3D('',#2981,#2355,#2356);
#2114=AXIS2_PLACEMENT_3D('',#2985,#2358,#2359);
#2115=AXIS2_PLACEMENT_3D('',#2989,#2361,#2362);
#2116=AXIS2_PLACEMENT_3D('',#2993,#2364,#2365);
#2117=AXIS2_PLACEMENT_3D('',#2997,#2367,#2368);
#2118=AXIS2_PLACEMENT_3D('',#3001,#2370,#2371);
#2119=AXIS2_PLACEMENT_3D('',#3005,#2373,#2374);
#2120=AXIS2_PLACEMENT_3D('',#3009,#2376,#2377);
#2121=AXIS2_PLACEMENT_3D('',#3013,#2379,#2380);
#2122=AXIS2_PLACEMENT_3D('',#3016,#2382,#2383);
#2123=AXIS2_PLACEMENT_3D('',#3018,#2384,#2385);
#2124=AXIS2_PLACEMENT_3D('',#3021,#2386,#2387);
#2125=AXIS2_PLACEMENT_3D('',#3025,#2389,#2390);
#2126=AXIS2_PLACEMENT_3D('',#3029,#2392,#2393);
#2127=AXIS2_PLACEMENT_3D('',#3033,#2395,#2396);
#2128=AXIS2_PLACEMENT_3D('',#3037,#2398,#2399);
#2129=AXIS2_PLACEMENT_3D('',#3041,#2401,#2402);
#2130=AXIS2_PLACEMENT_3D('',#3045,#2404,#2405);
#2131=AXIS2_PLACEMENT_3D('',#3049,#2407,#2408);
#2132=AXIS2_PLACEMENT_3D('',#3052,#2410,#2411);
#2133=AXIS2_PLACEMENT_3D('',#3055,#2412,#2413);
#2134=AXIS2_PLACEMENT_3D('',#3059,#2415,#2416);
#2135=AXIS2_PLACEMENT_3D('',#3063,#2418,#2419);
#2136=AXIS2_PLACEMENT_3D('',#3067,#2421,#2422);
#2137=AXIS2_PLACEMENT_3D('',#3070,#2424,#2425);
#2138=AXIS2_PLACEMENT_3D('',#3073,#2426,#2427);
#2139=AXIS2_PLACEMENT_3D('',#3077,#2429,#2430);
#2140=AXIS2_PLACEMENT_3D('',#3081,#2432,#2433);
#2141=AXIS2_PLACEMENT_3D('',#3085,#2435,#2436);
#2142=AXIS2_PLACEMENT_3D('',#3089,#2438,#2439);
#2143=AXIS2_PLACEMENT_3D('',#3093,#2441,#2442);
#2144=AXIS2_PLACEMENT_3D('',#3097,#2444,#2445);
#2145=AXIS2_PLACEMENT_3D('',#3101,#2447,#2448);
#2146=AXIS2_PLACEMENT_3D('',#3105,#2450,#2451);
#2147=AXIS2_PLACEMENT_3D('',#3109,#2453,#2454);
#2148=AXIS2_PLACEMENT_3D('',#3113,#2456,#2457);
#2149=AXIS2_PLACEMENT_3D('',#3117,#2459,#2460);
#2150=AXIS2_PLACEMENT_3D('',#3121,#2462,#2463);
#2151=AXIS2_PLACEMENT_3D('',#3125,#2465,#2466);
#2152=AXIS2_PLACEMENT_3D('',#3129,#2468,#2469);
#2153=AXIS2_PLACEMENT_3D('',#3133,#2471,#2472);
#2154=AXIS2_PLACEMENT_3D('',#3135,#2474,#2475);
#2155=AXIS2_PLACEMENT_3D('',#3138,#2476,#2477);
#2156=AXIS2_PLACEMENT_3D('',#3141,#2480,#2481);
#2157=AXIS2_PLACEMENT_3D('',#3145,#2484,#2485);
#2158=AXIS2_PLACEMENT_3D('',#3148,#2486,#2487);
#2159=AXIS2_PLACEMENT_3D('',#3151,#2490,#2491);
#2160=AXIS2_PLACEMENT_3D('',#3155,#2494,#2495);
#2161=AXIS2_PLACEMENT_3D('',#3157,#2496,#2497);
#2162=AXIS2_PLACEMENT_3D('',#3159,#2499,#2500);
#2163=AXIS2_PLACEMENT_3D('',#3161,#2502,#2503);
#2164=AXIS2_PLACEMENT_3D('',#3163,#2505,#2506);
#2165=AXIS2_PLACEMENT_3D('',#3165,#2507,#2508);
#2166=AXIS2_PLACEMENT_3D('',#3169,#2510,#2511);
#2167=AXIS2_PLACEMENT_3D('',#3173,#2513,#2514);
#2168=AXIS2_PLACEMENT_3D('',#3176,#2516,#2517);
#2169=AXIS2_PLACEMENT_3D('',#3179,#2518,#2519);
#2170=AXIS2_PLACEMENT_3D('',#3183,#2521,#2522);
#2171=AXIS2_PLACEMENT_3D('',#3187,#2524,#2525);
#2172=AXIS2_PLACEMENT_3D('',#3191,#2527,#2528);
#2173=AXIS2_PLACEMENT_3D('',#3195,#2530,#2531);
#2174=AXIS2_PLACEMENT_3D('',#3199,#2533,#2534);
#2175=AXIS2_PLACEMENT_3D('',#3203,#2536,#2537);
#2176=AXIS2_PLACEMENT_3D('',#3207,#2539,#2540);
#2177=AXIS2_PLACEMENT_3D('',#3211,#2542,#2543);
#2178=AXIS2_PLACEMENT_3D('',#3215,#2545,#2546);
#2179=AXIS2_PLACEMENT_3D('',#3219,#2548,#2549);
#2180=AXIS2_PLACEMENT_3D('',#3223,#2551,#2552);
#2181=AXIS2_PLACEMENT_3D('',#3227,#2554,#2555);
#2182=AXIS2_PLACEMENT_3D('',#3231,#2557,#2558);
#2183=AXIS2_PLACEMENT_3D('',#3235,#2560,#2561);
#2184=AXIS2_PLACEMENT_3D('',#3239,#2563,#2564);
#2185=AXIS2_PLACEMENT_3D('',#3242,#2566,#2567);
#2186=AXIS2_PLACEMENT_3D('',#3245,#2568,#2569);
#2187=AXIS2_PLACEMENT_3D('',#3249,#2571,#2572);
#2188=AXIS2_PLACEMENT_3D('',#3253,#2574,#2575);
#2189=AXIS2_PLACEMENT_3D('',#3257,#2577,#2578);
#2190=AXIS2_PLACEMENT_3D('',#3260,#2580,#2581);
#2191=AXIS2_PLACEMENT_3D('',#3263,#2582,#2583);
#2192=AXIS2_PLACEMENT_3D('',#3267,#2585,#2586);
#2193=AXIS2_PLACEMENT_3D('',#3271,#2588,#2589);
#2194=AXIS2_PLACEMENT_3D('',#3275,#2591,#2592);
#2195=AXIS2_PLACEMENT_3D('',#3279,#2594,#2595);
#2196=AXIS2_PLACEMENT_3D('',#3283,#2597,#2598);
#2197=AXIS2_PLACEMENT_3D('',#3287,#2600,#2601);
#2198=AXIS2_PLACEMENT_3D('',#3291,#2603,#2604);
#2199=AXIS2_PLACEMENT_3D('',#3294,#2606,#2607);
#2200=AXIS2_PLACEMENT_3D('',#3296,#2608,#2609);
#2201=AXIS2_PLACEMENT_3D('',#3301,#2611,#2612);
#2202=AXIS2_PLACEMENT_3D('',#3305,#2614,#2615);
#2203=AXIS2_PLACEMENT_3D('',#3309,#2617,#2618);
#2204=AXIS2_PLACEMENT_3D('',#3312,#2620,#2621);
#2205=AXIS2_PLACEMENT_3D('',#3315,#2622,#2623);
#2206=AXIS2_PLACEMENT_3D('',#3319,#2625,#2626);
#2207=AXIS2_PLACEMENT_3D('',#3323,#2628,#2629);
#2208=AXIS2_PLACEMENT_3D('',#3327,#2631,#2632);
#2209=AXIS2_PLACEMENT_3D('',#3331,#2634,#2635);
#2210=AXIS2_PLACEMENT_3D('',#3335,#2637,#2638);
#2211=AXIS2_PLACEMENT_3D('',#3339,#2640,#2641);
#2212=AXIS2_PLACEMENT_3D('',#3343,#2643,#2644);
#2213=AXIS2_PLACEMENT_3D('',#3345,#2646,#2647);
#2214=AXIS2_PLACEMENT_3D('',#3348,#2650,#2651);
#2215=AXIS2_PLACEMENT_3D('',#3350,#2653,#2654);
#2216=AXIS2_PLACEMENT_3D('',#3353,#2657,#2658);
#2217=AXIS2_PLACEMENT_3D('',#3354,#2659,#2660);
#2218=AXIS2_PLACEMENT_3D('',#3357,#2663,#2664);
#2219=AXIS2_PLACEMENT_3D('',#3358,#2665,#2666);
#2220=AXIS2_PLACEMENT_3D('',#3360,#2668,#2669);
#2221=AXIS2_PLACEMENT_3D('',#3361,#2670,#2671);
#2222=AXIS2_PLACEMENT_3D('',#3363,#2673,#2674);
#2223=AXIS2_PLACEMENT_3D('',#3365,#2676,#2677);
#2224=AXIS2_PLACEMENT_3D('',#3367,#2679,#2680);
#2225=AXIS2_PLACEMENT_3D('',#3369,#2682,#2683);
#2226=AXIS2_PLACEMENT_3D('',#3371,#2685,#2686);
#2227=AXIS2_PLACEMENT_3D('',#3373,#2688,#2689);
#2228=AXIS2_PLACEMENT_3D('',#3376,#2692,#2693);
#2229=AXIS2_PLACEMENT_3D('',#3378,#2695,#2696);
#2230=AXIS2_PLACEMENT_3D('',#3380,#2698,#2699);
#2231=AXIS2_PLACEMENT_3D('',#3382,#2701,#2702);
#2232=AXIS2_PLACEMENT_3D('',#3384,#2704,#2705);
#2233=AXIS2_PLACEMENT_3D('',#3386,#2707,#2708);
#2234=AXIS2_PLACEMENT_3D('',#3388,#2710,#2711);
#2235=AXIS2_PLACEMENT_3D('',#3389,#2712,#2713);
#2236=AXIS2_PLACEMENT_3D('',#3392,#2716,#2717);
#2237=AXIS2_PLACEMENT_3D('',#3394,#2719,#2720);
#2238=AXIS2_PLACEMENT_3D('',#3396,#2722,#2723);
#2239=AXIS2_PLACEMENT_3D('',#3398,#2725,#2726);
#2240=AXIS2_PLACEMENT_3D('',#3400,#2728,#2729);
#2241=AXIS2_PLACEMENT_3D('',#3402,#2731,#2732);
#2242=AXIS2_PLACEMENT_3D('',#3404,#2734,#2735);
#2243=AXIS2_PLACEMENT_3D('',#3405,#2736,#2737);
#2244=AXIS2_PLACEMENT_3D('',#3408,#2740,#2741);
#2245=AXIS2_PLACEMENT_3D('',#3410,#2743,#2744);
#2246=AXIS2_PLACEMENT_3D('',#3412,#2746,#2747);
#2247=AXIS2_PLACEMENT_3D('',#3414,#2749,#2750);
#2248=AXIS2_PLACEMENT_3D('',#3416,#2752,#2753);
#2249=AXIS2_PLACEMENT_3D('',#3418,#2755,#2756);
#2250=AXIS2_PLACEMENT_3D('',#3420,#2758,#2759);
#2251=AXIS2_PLACEMENT_3D('',#3421,#2760,#2761);
#2252=AXIS2_PLACEMENT_3D('',#3424,#2764,#2765);
#2253=AXIS2_PLACEMENT_3D('',#3426,#2767,#2768);
#2254=AXIS2_PLACEMENT_3D('',#3428,#2770,#2771);
#2255=AXIS2_PLACEMENT_3D('',#3430,#2773,#2774);
#2256=AXIS2_PLACEMENT_3D('',#3432,#2776,#2777);
#2257=AXIS2_PLACEMENT_3D('',#3434,#2779,#2780);
#2258=AXIS2_PLACEMENT_3D('',#3436,#2782,#2783);
#2259=AXIS2_PLACEMENT_3D('',#3437,#2784,#2785);
#2260=AXIS2_PLACEMENT_3D('',#3439,#2787,#2788);
#2261=AXIS2_PLACEMENT_3D('',#3442,#2791,#2792);
#2262=AXIS2_PLACEMENT_3D('',#3444,#2794,#2795);
#2263=AXIS2_PLACEMENT_3D('',#3446,#2797,#2798);
#2264=AXIS2_PLACEMENT_3D('',#3448,#2800,#2801);
#2265=AXIS2_PLACEMENT_3D('',#3450,#2803,#2804);
#2266=AXIS2_PLACEMENT_3D('',#3452,#2806,#2807);
#2267=AXIS2_PLACEMENT_3D('',#3454,#2809,#2810);
#2268=AXIS2_PLACEMENT_3D('',#3455,#2811,#2812);
#2269=AXIS2_PLACEMENT_3D('',#3457,#2814,#2815);
#2270=AXIS2_PLACEMENT_3D('',#3460,#2818,#2819);
#2271=AXIS2_PLACEMENT_3D('',#3462,#2821,#2822);
#2272=AXIS2_PLACEMENT_3D('',#3464,#2824,#2825);
#2273=AXIS2_PLACEMENT_3D('',#3466,#2827,#2828);
#2274=AXIS2_PLACEMENT_3D('',#3468,#2830,#2831);
#2275=AXIS2_PLACEMENT_3D('',#3470,#2833,#2834);
#2276=AXIS2_PLACEMENT_3D('',#3472,#2836,#2837);
#2277=AXIS2_PLACEMENT_3D('',#3473,#2838,#2839);
#2278=AXIS2_PLACEMENT_3D('',#3476,#2842,#2843);
#2279=AXIS2_PLACEMENT_3D('',#3478,#2845,#2846);
#2280=AXIS2_PLACEMENT_3D('',#3480,#2848,#2849);
#2281=AXIS2_PLACEMENT_3D('',#3482,#2851,#2852);
#2282=AXIS2_PLACEMENT_3D('',#3484,#2854,#2855);
#2283=AXIS2_PLACEMENT_3D('',#3486,#2857,#2858);
#2284=AXIS2_PLACEMENT_3D('',#3488,#2860,#2861);
#2285=AXIS2_PLACEMENT_3D('',#3489,#2862,#2863);
#2286=AXIS2_PLACEMENT_3D('',#3491,#2865,#2866);
#2287=AXIS2_PLACEMENT_3D('',#3493,#2868,#2869);
#2288=AXIS2_PLACEMENT_3D('',#3496,#2872,#2873);
#2289=AXIS2_PLACEMENT_3D('',#3498,#2875,#2876);
#2290=AXIS2_PLACEMENT_3D('',#3500,#2878,#2879);
#2291=AXIS2_PLACEMENT_3D('',#3502,#2881,#2882);
#2292=AXIS2_PLACEMENT_3D('',#3504,#2884,#2885);
#2293=AXIS2_PLACEMENT_3D('',#3506,#2887,#2888);
#2294=AXIS2_PLACEMENT_3D('',#3508,#2890,#2891);
#2295=AXIS2_PLACEMENT_3D('',#3509,#2892,#2893);
#2296=AXIS2_PLACEMENT_3D('',#3512,#2896,#2897);
#2297=AXIS2_PLACEMENT_3D('',#3514,#2899,#2900);
#2298=AXIS2_PLACEMENT_3D('',#3516,#2902,#2903);
#2299=AXIS2_PLACEMENT_3D('',#3518,#2905,#2906);
#2300=AXIS2_PLACEMENT_3D('',#3520,#2908,#2909);
#2301=AXIS2_PLACEMENT_3D('',#3522,#2911,#2912);
#2302=AXIS2_PLACEMENT_3D('',#3524,#2914,#2915);
#2303=AXIS2_PLACEMENT_3D('',#3525,#2916,#2917);
#2304=AXIS2_PLACEMENT_3D('',#3526,#2918,#2919);
#2305=AXIS2_PLACEMENT_3D('',#3527,#2920,#2921);
#2306=DIRECTION('axis',(0.,0.,1.));
#2307=DIRECTION('refdir',(1.,0.,0.));
#2308=DIRECTION('axis',(0.,0.,1.));
#2309=DIRECTION('refdir',(1.,0.,0.));
#2310=DIRECTION('center_axis',(0.,0.,-1.));
#2311=DIRECTION('ref_axis',(0.707106781186546,0.707106781186549,0.));
#2312=DIRECTION('center_axis',(0.,0.,1.));
#2313=DIRECTION('ref_axis',(0.707106781186546,0.707106781186549,0.));
#2314=DIRECTION('',(0.,0.,1.));
#2315=DIRECTION('center_axis',(0.,0.,-1.));
#2316=DIRECTION('ref_axis',(0.707106781186546,0.707106781186549,0.));
#2317=DIRECTION('',(0.,0.,-1.));
#2318=DIRECTION('center_axis',(0.,1.,0.));
#2319=DIRECTION('ref_axis',(-1.,0.,0.));
#2320=DIRECTION('',(-1.,0.,0.));
#2321=DIRECTION('',(0.,0.,-1.));
#2322=DIRECTION('',(-1.,0.,0.));
#2323=DIRECTION('center_axis',(0.,0.,1.));
#2324=DIRECTION('ref_axis',(1.,0.,0.));
#2325=DIRECTION('',(0.,-1.,0.));
#2326=DIRECTION('center_axis',(0.,0.,-1.));
#2327=DIRECTION('ref_axis',(-0.707106781186548,-0.707106781186548,0.));
#2328=DIRECTION('',(1.,0.,0.));
#2329=DIRECTION('center_axis',(0.,0.,-1.));
#2330=DIRECTION('ref_axis',(0.707106781186549,-0.707106781186546,0.));
#2331=DIRECTION('',(0.,1.,0.));
#2332=DIRECTION('center_axis',(0.,0.,1.));
#2333=DIRECTION('ref_axis',(-0.707106781186548,0.707106781186548,0.));
#2334=DIRECTION('',(-1.,0.,0.));
#2335=DIRECTION('center_axis',(0.,0.,1.));
#2336=DIRECTION('ref_axis',(0.707106781186547,0.707106781186547,0.));
#2337=DIRECTION('',(0.,1.,0.));
#2338=DIRECTION('center_axis',(0.,0.,1.));
#2339=DIRECTION('ref_axis',(0.707106781186547,-0.707106781186547,0.));
#2340=DIRECTION('',(1.,0.,0.));
#2341=DIRECTION('center_axis',(0.,0.,1.));
#2342=DIRECTION('ref_axis',(-0.707106781186547,-0.707106781186547,0.));
#2343=DIRECTION('',(0.,-1.,0.));
#2344=DIRECTION('center_axis',(0.,0.,1.));
#2345=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186547,0.));
#2346=DIRECTION('center_axis',(0.,0.,-1.));
#2347=DIRECTION('ref_axis',(0.,1.,0.));
#2348=DIRECTION('',(0.,1.,0.));
#2349=DIRECTION('center_axis',(0.,0.,-1.));
#2350=DIRECTION('ref_axis',(-1.,0.,0.));
#2351=DIRECTION('',(-1.,0.,0.));
#2352=DIRECTION('center_axis',(0.,0.,-1.));
#2353=DIRECTION('ref_axis',(0.,-1.,0.));
#2354=DIRECTION('',(0.,-1.,0.));
#2355=DIRECTION('center_axis',(0.,0.,-1.));
#2356=DIRECTION('ref_axis',(1.,0.,0.));
#2357=DIRECTION('',(1.,0.,0.));
#2358=DIRECTION('center_axis',(0.,0.,-1.));
#2359=DIRECTION('ref_axis',(0.,1.,0.));
#2360=DIRECTION('',(0.,1.,0.));
#2361=DIRECTION('center_axis',(0.,0.,-1.));
#2362=DIRECTION('ref_axis',(-1.,0.,0.));
#2363=DIRECTION('',(-1.,0.,0.));
#2364=DIRECTION('center_axis',(0.,0.,-1.));
#2365=DIRECTION('ref_axis',(0.,-1.,0.));
#2366=DIRECTION('',(0.,-1.,0.));
#2367=DIRECTION('center_axis',(0.,0.,-1.));
#2368=DIRECTION('ref_axis',(1.,0.,0.));
#2369=DIRECTION('',(1.,0.,0.));
#2370=DIRECTION('center_axis',(0.,0.,-1.));
#2371=DIRECTION('ref_axis',(0.707106781186546,-0.707106781186549,0.));
#2372=DIRECTION('',(0.,-1.,0.));
#2373=DIRECTION('center_axis',(0.,0.,-1.));
#2374=DIRECTION('ref_axis',(0.707106781186546,0.707106781186549,0.));
#2375=DIRECTION('',(1.,0.,0.));
#2376=DIRECTION('center_axis',(0.,0.,-1.));
#2377=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186548,0.));
#2378=DIRECTION('',(0.,1.,0.));
#2379=DIRECTION('center_axis',(0.,0.,-1.));
#2380=DIRECTION('ref_axis',(-0.707106781186548,-0.707106781186547,0.));
#2381=DIRECTION('',(-1.,0.,0.));
#2382=DIRECTION('center_axis',(0.,0.,-1.));
#2383=DIRECTION('ref_axis',(1.,0.,0.));
#2384=DIRECTION('center_axis',(0.,0.,-1.));
#2385=DIRECTION('ref_axis',(1.,0.,0.));
#2386=DIRECTION('center_axis',(0.,0.,-1.));
#2387=DIRECTION('ref_axis',(0.,1.,0.));
#2388=DIRECTION('',(0.,1.,0.));
#2389=DIRECTION('center_axis',(0.,0.,-1.));
#2390=DIRECTION('ref_axis',(-1.,0.,0.));
#2391=DIRECTION('',(-1.,0.,0.));
#2392=DIRECTION('center_axis',(0.,0.,-1.));
#2393=DIRECTION('ref_axis',(0.,-1.,0.));
#2394=DIRECTION('',(0.,-1.,0.));
#2395=DIRECTION('center_axis',(0.,0.,-1.));
#2396=DIRECTION('ref_axis',(1.,0.,0.));
#2397=DIRECTION('',(1.,0.,0.));
#2398=DIRECTION('center_axis',(0.,0.,-1.));
#2399=DIRECTION('ref_axis',(0.,1.,0.));
#2400=DIRECTION('',(0.,1.,0.));
#2401=DIRECTION('center_axis',(0.,0.,-1.));
#2402=DIRECTION('ref_axis',(-1.,0.,0.));
#2403=DIRECTION('',(-1.,0.,0.));
#2404=DIRECTION('center_axis',(0.,0.,-1.));
#2405=DIRECTION('ref_axis',(0.,-1.,0.));
#2406=DIRECTION('',(0.,-1.,0.));
#2407=DIRECTION('center_axis',(0.,0.,-1.));
#2408=DIRECTION('ref_axis',(1.,0.,0.));
#2409=DIRECTION('',(1.,0.,0.));
#2410=DIRECTION('center_axis',(0.,0.,-1.));
#2411=DIRECTION('ref_axis',(1.,0.,0.));
#2412=DIRECTION('center_axis',(0.,0.,-1.));
#2413=DIRECTION('ref_axis',(0.,1.,0.));
#2414=DIRECTION('',(0.,1.,0.));
#2415=DIRECTION('center_axis',(0.,0.,-1.));
#2416=DIRECTION('ref_axis',(-1.,0.,0.));
#2417=DIRECTION('',(-1.,0.,0.));
#2418=DIRECTION('center_axis',(0.,0.,-1.));
#2419=DIRECTION('ref_axis',(0.,-1.,0.));
#2420=DIRECTION('',(0.,-1.,0.));
#2421=DIRECTION('center_axis',(0.,0.,-1.));
#2422=DIRECTION('ref_axis',(1.,0.,0.));
#2423=DIRECTION('',(1.,0.,0.));
#2424=DIRECTION('center_axis',(0.,0.,-1.));
#2425=DIRECTION('ref_axis',(1.,0.,0.));
#2426=DIRECTION('center_axis',(0.,0.,-1.));
#2427=DIRECTION('ref_axis',(0.,1.,0.));
#2428=DIRECTION('',(0.,1.,0.));
#2429=DIRECTION('center_axis',(0.,0.,-1.));
#2430=DIRECTION('ref_axis',(-1.,0.,0.));
#2431=DIRECTION('',(-1.,0.,0.));
#2432=DIRECTION('center_axis',(0.,0.,-1.));
#2433=DIRECTION('ref_axis',(0.,-1.,0.));
#2434=DIRECTION('',(0.,-1.,0.));
#2435=DIRECTION('center_axis',(0.,0.,-1.));
#2436=DIRECTION('ref_axis',(1.,0.,0.));
#2437=DIRECTION('',(1.,0.,0.));
#2438=DIRECTION('center_axis',(0.,0.,-1.));
#2439=DIRECTION('ref_axis',(0.,1.,0.));
#2440=DIRECTION('',(0.,1.,0.));
#2441=DIRECTION('center_axis',(0.,0.,-1.));
#2442=DIRECTION('ref_axis',(-1.,0.,0.));
#2443=DIRECTION('',(-1.,0.,0.));
#2444=DIRECTION('center_axis',(0.,0.,-1.));
#2445=DIRECTION('ref_axis',(0.,-1.,0.));
#2446=DIRECTION('',(0.,-1.,0.));
#2447=DIRECTION('center_axis',(0.,0.,-1.));
#2448=DIRECTION('ref_axis',(1.,0.,0.));
#2449=DIRECTION('',(1.,0.,0.));
#2450=DIRECTION('center_axis',(0.,0.,-1.));
#2451=DIRECTION('ref_axis',(0.,1.,0.));
#2452=DIRECTION('',(0.,1.,0.));
#2453=DIRECTION('center_axis',(0.,0.,-1.));
#2454=DIRECTION('ref_axis',(-1.,0.,0.));
#2455=DIRECTION('',(-1.,0.,0.));
#2456=DIRECTION('center_axis',(0.,0.,-1.));
#2457=DIRECTION('ref_axis',(0.,-1.,0.));
#2458=DIRECTION('',(0.,-1.,0.));
#2459=DIRECTION('center_axis',(0.,0.,-1.));
#2460=DIRECTION('ref_axis',(1.,0.,0.));
#2461=DIRECTION('',(1.,0.,0.));
#2462=DIRECTION('center_axis',(0.,0.,-1.));
#2463=DIRECTION('ref_axis',(0.,1.,0.));
#2464=DIRECTION('',(0.,1.,0.));
#2465=DIRECTION('center_axis',(0.,0.,-1.));
#2466=DIRECTION('ref_axis',(-1.,0.,0.));
#2467=DIRECTION('',(-1.,0.,0.));
#2468=DIRECTION('center_axis',(0.,0.,-1.));
#2469=DIRECTION('ref_axis',(0.,-1.,0.));
#2470=DIRECTION('',(0.,-1.,0.));
#2471=DIRECTION('center_axis',(0.,0.,-1.));
#2472=DIRECTION('ref_axis',(1.,0.,0.));
#2473=DIRECTION('',(1.,0.,0.));
#2474=DIRECTION('center_axis',(0.,0.,1.));
#2475=DIRECTION('ref_axis',(-0.707106781186548,0.707106781186548,0.));
#2476=DIRECTION('center_axis',(0.,0.,-1.));
#2477=DIRECTION('ref_axis',(-0.707106781186548,0.707106781186548,0.));
#2478=DIRECTION('',(0.,0.,-1.));
#2479=DIRECTION('',(0.,0.,1.));
#2480=DIRECTION('center_axis',(0.,1.,0.));
#2481=DIRECTION('ref_axis',(-1.,0.,0.));
#2482=DIRECTION('',(-1.,0.,0.));
#2483=DIRECTION('',(0.,0.,1.));
#2484=DIRECTION('center_axis',(0.,0.,-1.));
#2485=DIRECTION('ref_axis',(-0.707106781186548,-0.707106781186548,0.));
#2486=DIRECTION('center_axis',(0.,0.,1.));
#2487=DIRECTION('ref_axis',(-0.707106781186548,-0.707106781186548,0.));
#2488=DIRECTION('',(0.,0.,-1.));
#2489=DIRECTION('',(0.,0.,1.));
#2490=DIRECTION('center_axis',(0.,-1.,0.));
#2491=DIRECTION('ref_axis',(-1.,0.,0.));
#2492=DIRECTION('',(-1.,0.,0.));
#2493=DIRECTION('',(0.,0.,1.));
#2494=DIRECTION('center_axis',(0.,0.,-1.));
#2495=DIRECTION('ref_axis',(0.707106781186549,-0.707106781186546,0.));
#2496=DIRECTION('center_axis',(0.,0.,1.));
#2497=DIRECTION('ref_axis',(0.707106781186549,-0.707106781186546,0.));
#2498=DIRECTION('',(0.,0.,-1.));
#2499=DIRECTION('center_axis',(1.,0.,0.));
#2500=DIRECTION('ref_axis',(0.,-1.,0.));
#2501=DIRECTION('',(0.,-1.,0.));
#2502=DIRECTION('center_axis',(-1.,0.,0.));
#2503=DIRECTION('ref_axis',(0.,1.,0.));
#2504=DIRECTION('',(0.,1.,0.));
#2505=DIRECTION('center_axis',(0.,0.,1.));
#2506=DIRECTION('ref_axis',(1.,0.,0.));
#2507=DIRECTION('center_axis',(0.,0.,-1.));
#2508=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186547,0.));
#2509=DIRECTION('',(0.,-1.,0.));
#2510=DIRECTION('center_axis',(0.,0.,-1.));
#2511=DIRECTION('ref_axis',(-0.707106781186547,-0.707106781186547,0.));
#2512=DIRECTION('',(1.,0.,0.));
#2513=DIRECTION('center_axis',(0.,0.,-1.));
#2514=DIRECTION('ref_axis',(0.707106781186547,-0.707106781186547,0.));
#2515=DIRECTION('',(0.,1.,0.));
#2516=DIRECTION('center_axis',(0.,0.,-1.));
#2517=DIRECTION('ref_axis',(0.707106781186547,0.707106781186547,0.));
#2518=DIRECTION('center_axis',(0.,0.,-1.));
#2519=DIRECTION('ref_axis',(0.,1.,0.));
#2520=DIRECTION('',(1.,0.,0.));
#2521=DIRECTION('center_axis',(0.,0.,-1.));
#2522=DIRECTION('ref_axis',(1.,0.,0.));
#2523=DIRECTION('',(0.,-1.,0.));
#2524=DIRECTION('center_axis',(0.,0.,-1.));
#2525=DIRECTION('ref_axis',(0.,-1.,0.));
#2526=DIRECTION('',(-1.,0.,0.));
#2527=DIRECTION('center_axis',(0.,0.,-1.));
#2528=DIRECTION('ref_axis',(-1.,0.,0.));
#2529=DIRECTION('',(0.,1.,0.));
#2530=DIRECTION('center_axis',(0.,0.,-1.));
#2531=DIRECTION('ref_axis',(0.,1.,0.));
#2532=DIRECTION('',(1.,0.,0.));
#2533=DIRECTION('center_axis',(0.,0.,-1.));
#2534=DIRECTION('ref_axis',(1.,0.,0.));
#2535=DIRECTION('',(0.,-1.,0.));
#2536=DIRECTION('center_axis',(0.,0.,-1.));
#2537=DIRECTION('ref_axis',(0.,-1.,0.));
#2538=DIRECTION('',(-1.,0.,0.));
#2539=DIRECTION('center_axis',(0.,0.,-1.));
#2540=DIRECTION('ref_axis',(-1.,0.,0.));
#2541=DIRECTION('',(0.,1.,0.));
#2542=DIRECTION('center_axis',(0.,0.,-1.));
#2543=DIRECTION('ref_axis',(0.,1.,0.));
#2544=DIRECTION('',(1.,0.,0.));
#2545=DIRECTION('center_axis',(0.,0.,-1.));
#2546=DIRECTION('ref_axis',(1.,0.,0.));
#2547=DIRECTION('',(0.,-1.,0.));
#2548=DIRECTION('center_axis',(0.,0.,-1.));
#2549=DIRECTION('ref_axis',(0.,-1.,0.));
#2550=DIRECTION('',(-1.,0.,0.));
#2551=DIRECTION('center_axis',(0.,0.,-1.));
#2552=DIRECTION('ref_axis',(-1.,0.,0.));
#2553=DIRECTION('',(0.,1.,0.));
#2554=DIRECTION('center_axis',(0.,0.,-1.));
#2555=DIRECTION('ref_axis',(0.,1.,0.));
#2556=DIRECTION('',(1.,0.,0.));
#2557=DIRECTION('center_axis',(0.,0.,-1.));
#2558=DIRECTION('ref_axis',(1.,0.,0.));
#2559=DIRECTION('',(0.,-1.,0.));
#2560=DIRECTION('center_axis',(0.,0.,-1.));
#2561=DIRECTION('ref_axis',(0.,-1.,0.));
#2562=DIRECTION('',(-1.,0.,0.));
#2563=DIRECTION('center_axis',(0.,0.,-1.));
#2564=DIRECTION('ref_axis',(-1.,0.,0.));
#2565=DIRECTION('',(0.,1.,0.));
#2566=DIRECTION('center_axis',(0.,0.,-1.));
#2567=DIRECTION('ref_axis',(1.,0.,0.));
#2568=DIRECTION('center_axis',(0.,0.,-1.));
#2569=DIRECTION('ref_axis',(0.,1.,0.));
#2570=DIRECTION('',(1.,0.,0.));
#2571=DIRECTION('center_axis',(0.,0.,-1.));
#2572=DIRECTION('ref_axis',(1.,0.,0.));
#2573=DIRECTION('',(0.,-1.,0.));
#2574=DIRECTION('center_axis',(0.,0.,-1.));
#2575=DIRECTION('ref_axis',(0.,-1.,0.));
#2576=DIRECTION('',(-1.,0.,0.));
#2577=DIRECTION('center_axis',(0.,0.,-1.));
#2578=DIRECTION('ref_axis',(-1.,0.,0.));
#2579=DIRECTION('',(0.,1.,0.));
#2580=DIRECTION('center_axis',(0.,0.,-1.));
#2581=DIRECTION('ref_axis',(1.,0.,0.));
#2582=DIRECTION('center_axis',(0.,0.,-1.));
#2583=DIRECTION('ref_axis',(0.,1.,0.));
#2584=DIRECTION('',(1.,0.,0.));
#2585=DIRECTION('center_axis',(0.,0.,-1.));
#2586=DIRECTION('ref_axis',(1.,0.,0.));
#2587=DIRECTION('',(0.,-1.,0.));
#2588=DIRECTION('center_axis',(0.,0.,-1.));
#2589=DIRECTION('ref_axis',(0.,-1.,0.));
#2590=DIRECTION('',(-1.,0.,0.));
#2591=DIRECTION('center_axis',(0.,0.,-1.));
#2592=DIRECTION('ref_axis',(-1.,0.,0.));
#2593=DIRECTION('',(0.,1.,0.));
#2594=DIRECTION('center_axis',(0.,0.,-1.));
#2595=DIRECTION('ref_axis',(0.,1.,0.));
#2596=DIRECTION('',(1.,0.,0.));
#2597=DIRECTION('center_axis',(0.,0.,-1.));
#2598=DIRECTION('ref_axis',(1.,0.,0.));
#2599=DIRECTION('',(0.,-1.,0.));
#2600=DIRECTION('center_axis',(0.,0.,-1.));
#2601=DIRECTION('ref_axis',(0.,-1.,0.));
#2602=DIRECTION('',(-1.,0.,0.));
#2603=DIRECTION('center_axis',(0.,0.,-1.));
#2604=DIRECTION('ref_axis',(-1.,0.,0.));
#2605=DIRECTION('',(0.,1.,0.));
#2606=DIRECTION('center_axis',(0.,0.,-1.));
#2607=DIRECTION('ref_axis',(1.,0.,0.));
#2608=DIRECTION('center_axis',(0.,0.,-1.));
#2609=DIRECTION('ref_axis',(1.,0.,0.));
#2610=DIRECTION('',(-1.,0.,0.));
#2611=DIRECTION('center_axis',(0.,0.,1.));
#2612=DIRECTION('ref_axis',(-0.707106781186548,-0.707106781186547,0.));
#2613=DIRECTION('',(0.,1.,0.));
#2614=DIRECTION('center_axis',(0.,0.,1.));
#2615=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186548,0.));
#2616=DIRECTION('',(1.,0.,0.));
#2617=DIRECTION('center_axis',(0.,0.,1.));
#2618=DIRECTION('ref_axis',(0.707106781186546,0.707106781186549,0.));
#2619=DIRECTION('',(0.,-1.,0.));
#2620=DIRECTION('center_axis',(0.,0.,1.));
#2621=DIRECTION('ref_axis',(0.707106781186546,-0.707106781186549,0.));
#2622=DIRECTION('center_axis',(0.,0.,-1.));
#2623=DIRECTION('ref_axis',(0.,1.,0.));
#2624=DIRECTION('',(1.,0.,0.));
#2625=DIRECTION('center_axis',(0.,0.,-1.));
#2626=DIRECTION('ref_axis',(1.,0.,0.));
#2627=DIRECTION('',(0.,-1.,0.));
#2628=DIRECTION('center_axis',(0.,0.,-1.));
#2629=DIRECTION('ref_axis',(0.,-1.,0.));
#2630=DIRECTION('',(-1.,0.,0.));
#2631=DIRECTION('center_axis',(0.,0.,-1.));
#2632=DIRECTION('ref_axis',(-1.,0.,0.));
#2633=DIRECTION('',(0.,1.,0.));
#2634=DIRECTION('center_axis',(0.,0.,-1.));
#2635=DIRECTION('ref_axis',(0.,1.,0.));
#2636=DIRECTION('',(1.,0.,0.));
#2637=DIRECTION('center_axis',(0.,0.,-1.));
#2638=DIRECTION('ref_axis',(1.,0.,0.));
#2639=DIRECTION('',(0.,-1.,0.));
#2640=DIRECTION('center_axis',(0.,0.,-1.));
#2641=DIRECTION('ref_axis',(0.,-1.,0.));
#2642=DIRECTION('',(-1.,0.,0.));
#2643=DIRECTION('center_axis',(0.,0.,-1.));
#2644=DIRECTION('ref_axis',(-1.,0.,0.));
#2645=DIRECTION('',(0.,1.,0.));
#2646=DIRECTION('center_axis',(0.,0.,1.));
#2647=DIRECTION('ref_axis',(-0.707106781186548,-0.707106781186547,0.));
#2648=DIRECTION('',(0.,0.,1.));
#2649=DIRECTION('',(0.,0.,-1.));
#2650=DIRECTION('center_axis',(1.,0.,0.));
#2651=DIRECTION('ref_axis',(0.,1.,0.));
#2652=DIRECTION('',(0.,0.,-1.));
#2653=DIRECTION('center_axis',(0.,0.,1.));
#2654=DIRECTION('ref_axis',(0.707106781186546,-0.707106781186549,0.));
#2655=DIRECTION('',(0.,0.,1.));
#2656=DIRECTION('',(0.,0.,-1.));
#2657=DIRECTION('center_axis',(0.,1.,0.));
#2658=DIRECTION('ref_axis',(-1.,0.,0.));
#2659=DIRECTION('center_axis',(0.,0.,1.));
#2660=DIRECTION('ref_axis',(0.707106781186546,0.707106781186549,0.));
#2661=DIRECTION('',(0.,0.,1.));
#2662=DIRECTION('',(0.,0.,-1.));
#2663=DIRECTION('center_axis',(-1.,0.,0.));
#2664=DIRECTION('ref_axis',(0.,-1.,0.));
#2665=DIRECTION('center_axis',(0.,0.,1.));
#2666=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186548,0.));
#2667=DIRECTION('',(0.,0.,1.));
#2668=DIRECTION('center_axis',(0.,-1.,0.));
#2669=DIRECTION('ref_axis',(1.,0.,0.));
#2670=DIRECTION('center_axis',(0.,0.,1.));
#2671=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186547,0.));
#2672=DIRECTION('',(0.,0.,1.));
#2673=DIRECTION('center_axis',(-1.,0.,0.));
#2674=DIRECTION('ref_axis',(0.,-1.,0.));
#2675=DIRECTION('',(0.,0.,-1.));
#2676=DIRECTION('center_axis',(0.,0.,1.));
#2677=DIRECTION('ref_axis',(0.707106781186547,0.707106781186547,0.));
#2678=DIRECTION('',(0.,0.,-1.));
#2679=DIRECTION('center_axis',(1.,0.,0.));
#2680=DIRECTION('ref_axis',(0.,1.,0.));
#2681=DIRECTION('',(0.,0.,1.));
#2682=DIRECTION('center_axis',(0.,0.,1.));
#2683=DIRECTION('ref_axis',(0.707106781186547,-0.707106781186547,0.));
#2684=DIRECTION('',(0.,0.,-1.));
#2685=DIRECTION('center_axis',(0.,0.,1.));
#2686=DIRECTION('ref_axis',(-0.707106781186547,-0.707106781186547,0.));
#2687=DIRECTION('',(0.,0.,1.));
#2688=DIRECTION('center_axis',(1.,0.,0.));
#2689=DIRECTION('ref_axis',(0.,1.,0.));
#2690=DIRECTION('',(0.,0.,1.));
#2691=DIRECTION('',(0.,0.,1.));
#2692=DIRECTION('center_axis',(0.,0.,1.));
#2693=DIRECTION('ref_axis',(-1.,0.,0.));
#2694=DIRECTION('',(0.,0.,1.));
#2695=DIRECTION('center_axis',(0.,1.,0.));
#2696=DIRECTION('ref_axis',(-1.,0.,0.));
#2697=DIRECTION('',(0.,0.,1.));
#2698=DIRECTION('center_axis',(0.,0.,1.));
#2699=DIRECTION('ref_axis',(0.,-1.,0.));
#2700=DIRECTION('',(0.,0.,1.));
#2701=DIRECTION('center_axis',(-1.,0.,0.));
#2702=DIRECTION('ref_axis',(0.,-1.,0.));
#2703=DIRECTION('',(0.,0.,1.));
#2704=DIRECTION('center_axis',(0.,0.,1.));
#2705=DIRECTION('ref_axis',(1.,0.,0.));
#2706=DIRECTION('',(0.,0.,1.));
#2707=DIRECTION('center_axis',(0.,-1.,0.));
#2708=DIRECTION('ref_axis',(1.,0.,0.));
#2709=DIRECTION('',(0.,0.,1.));
#2710=DIRECTION('center_axis',(0.,0.,1.));
#2711=DIRECTION('ref_axis',(0.,1.,0.));
#2712=DIRECTION('center_axis',(1.,0.,0.));
#2713=DIRECTION('ref_axis',(0.,1.,0.));
#2714=DIRECTION('',(0.,0.,1.));
#2715=DIRECTION('',(0.,0.,1.));
#2716=DIRECTION('center_axis',(0.,0.,1.));
#2717=DIRECTION('ref_axis',(-1.,0.,0.));
#2718=DIRECTION('',(0.,0.,1.));
#2719=DIRECTION('center_axis',(0.,1.,0.));
#2720=DIRECTION('ref_axis',(-1.,0.,0.));
#2721=DIRECTION('',(0.,0.,1.));
#2722=DIRECTION('center_axis',(0.,0.,1.));
#2723=DIRECTION('ref_axis',(0.,-1.,0.));
#2724=DIRECTION('',(0.,0.,1.));
#2725=DIRECTION('center_axis',(-1.,0.,0.));
#2726=DIRECTION('ref_axis',(0.,-1.,0.));
#2727=DIRECTION('',(0.,0.,1.));
#2728=DIRECTION('center_axis',(0.,0.,1.));
#2729=DIRECTION('ref_axis',(1.,0.,0.));
#2730=DIRECTION('',(0.,0.,1.));
#2731=DIRECTION('center_axis',(0.,-1.,0.));
#2732=DIRECTION('ref_axis',(1.,0.,0.));
#2733=DIRECTION('',(0.,0.,1.));
#2734=DIRECTION('center_axis',(0.,0.,1.));
#2735=DIRECTION('ref_axis',(0.,1.,0.));
#2736=DIRECTION('center_axis',(1.,0.,0.));
#2737=DIRECTION('ref_axis',(0.,1.,0.));
#2738=DIRECTION('',(0.,0.,1.));
#2739=DIRECTION('',(0.,0.,1.));
#2740=DIRECTION('center_axis',(0.,0.,1.));
#2741=DIRECTION('ref_axis',(-1.,0.,0.));
#2742=DIRECTION('',(0.,0.,1.));
#2743=DIRECTION('center_axis',(0.,1.,0.));
#2744=DIRECTION('ref_axis',(-1.,0.,0.));
#2745=DIRECTION('',(0.,0.,1.));
#2746=DIRECTION('center_axis',(0.,0.,1.));
#2747=DIRECTION('ref_axis',(0.,-1.,0.));
#2748=DIRECTION('',(0.,0.,1.));
#2749=DIRECTION('center_axis',(-1.,0.,0.));
#2750=DIRECTION('ref_axis',(0.,-1.,0.));
#2751=DIRECTION('',(0.,0.,1.));
#2752=DIRECTION('center_axis',(0.,0.,1.));
#2753=DIRECTION('ref_axis',(1.,0.,0.));
#2754=DIRECTION('',(0.,0.,1.));
#2755=DIRECTION('center_axis',(0.,-1.,0.));
#2756=DIRECTION('ref_axis',(1.,0.,0.));
#2757=DIRECTION('',(0.,0.,1.));
#2758=DIRECTION('center_axis',(0.,0.,1.));
#2759=DIRECTION('ref_axis',(0.,1.,0.));
#2760=DIRECTION('center_axis',(1.,0.,0.));
#2761=DIRECTION('ref_axis',(0.,1.,0.));
#2762=DIRECTION('',(0.,0.,1.));
#2763=DIRECTION('',(0.,0.,1.));
#2764=DIRECTION('center_axis',(0.,0.,1.));
#2765=DIRECTION('ref_axis',(-1.,0.,0.));
#2766=DIRECTION('',(0.,0.,1.));
#2767=DIRECTION('center_axis',(0.,1.,0.));
#2768=DIRECTION('ref_axis',(-1.,0.,0.));
#2769=DIRECTION('',(0.,0.,1.));
#2770=DIRECTION('center_axis',(0.,0.,1.));
#2771=DIRECTION('ref_axis',(0.,-1.,0.));
#2772=DIRECTION('',(0.,0.,1.));
#2773=DIRECTION('center_axis',(-1.,0.,0.));
#2774=DIRECTION('ref_axis',(0.,-1.,0.));
#2775=DIRECTION('',(0.,0.,1.));
#2776=DIRECTION('center_axis',(0.,0.,1.));
#2777=DIRECTION('ref_axis',(1.,0.,0.));
#2778=DIRECTION('',(0.,0.,1.));
#2779=DIRECTION('center_axis',(0.,-1.,0.));
#2780=DIRECTION('ref_axis',(1.,0.,0.));
#2781=DIRECTION('',(0.,0.,1.));
#2782=DIRECTION('center_axis',(0.,0.,1.));
#2783=DIRECTION('ref_axis',(0.,1.,0.));
#2784=DIRECTION('center_axis',(0.,0.,1.));
#2785=DIRECTION('ref_axis',(1.,0.,0.));
#2786=DIRECTION('',(0.,0.,-1.));
#2787=DIRECTION('center_axis',(1.,0.,0.));
#2788=DIRECTION('ref_axis',(0.,1.,0.));
#2789=DIRECTION('',(0.,0.,1.));
#2790=DIRECTION('',(0.,0.,1.));
#2791=DIRECTION('center_axis',(0.,0.,1.));
#2792=DIRECTION('ref_axis',(-1.,0.,0.));
#2793=DIRECTION('',(0.,0.,1.));
#2794=DIRECTION('center_axis',(0.,1.,0.));
#2795=DIRECTION('ref_axis',(-1.,0.,0.));
#2796=DIRECTION('',(0.,0.,1.));
#2797=DIRECTION('center_axis',(0.,0.,1.));
#2798=DIRECTION('ref_axis',(0.,-1.,0.));
#2799=DIRECTION('',(0.,0.,1.));
#2800=DIRECTION('center_axis',(-1.,0.,0.));
#2801=DIRECTION('ref_axis',(0.,-1.,0.));
#2802=DIRECTION('',(0.,0.,1.));
#2803=DIRECTION('center_axis',(0.,0.,1.));
#2804=DIRECTION('ref_axis',(1.,0.,0.));
#2805=DIRECTION('',(0.,0.,1.));
#2806=DIRECTION('center_axis',(0.,-1.,0.));
#2807=DIRECTION('ref_axis',(1.,0.,0.));
#2808=DIRECTION('',(0.,0.,1.));
#2809=DIRECTION('center_axis',(0.,0.,1.));
#2810=DIRECTION('ref_axis',(0.,1.,0.));
#2811=DIRECTION('center_axis',(0.,0.,1.));
#2812=DIRECTION('ref_axis',(1.,0.,0.));
#2813=DIRECTION('',(0.,0.,-1.));
#2814=DIRECTION('center_axis',(1.,0.,0.));
#2815=DIRECTION('ref_axis',(0.,1.,0.));
#2816=DIRECTION('',(0.,0.,1.));
#2817=DIRECTION('',(0.,0.,1.));
#2818=DIRECTION('center_axis',(0.,0.,1.));
#2819=DIRECTION('ref_axis',(-1.,0.,0.));
#2820=DIRECTION('',(0.,0.,1.));
#2821=DIRECTION('center_axis',(0.,1.,0.));
#2822=DIRECTION('ref_axis',(-1.,0.,0.));
#2823=DIRECTION('',(0.,0.,1.));
#2824=DIRECTION('center_axis',(0.,0.,1.));
#2825=DIRECTION('ref_axis',(0.,-1.,0.));
#2826=DIRECTION('',(0.,0.,1.));
#2827=DIRECTION('center_axis',(-1.,0.,0.));
#2828=DIRECTION('ref_axis',(0.,-1.,0.));
#2829=DIRECTION('',(0.,0.,1.));
#2830=DIRECTION('center_axis',(0.,0.,1.));
#2831=DIRECTION('ref_axis',(1.,0.,0.));
#2832=DIRECTION('',(0.,0.,1.));
#2833=DIRECTION('center_axis',(0.,-1.,0.));
#2834=DIRECTION('ref_axis',(1.,0.,0.));
#2835=DIRECTION('',(0.,0.,1.));
#2836=DIRECTION('center_axis',(0.,0.,1.));
#2837=DIRECTION('ref_axis',(0.,1.,0.));
#2838=DIRECTION('center_axis',(1.,0.,0.));
#2839=DIRECTION('ref_axis',(0.,1.,0.));
#2840=DIRECTION('',(0.,0.,1.));
#2841=DIRECTION('',(0.,0.,1.));
#2842=DIRECTION('center_axis',(0.,0.,1.));
#2843=DIRECTION('ref_axis',(-1.,0.,0.));
#2844=DIRECTION('',(0.,0.,1.));
#2845=DIRECTION('center_axis',(0.,1.,0.));
#2846=DIRECTION('ref_axis',(-1.,0.,0.));
#2847=DIRECTION('',(0.,0.,1.));
#2848=DIRECTION('center_axis',(0.,0.,1.));
#2849=DIRECTION('ref_axis',(0.,-1.,0.));
#2850=DIRECTION('',(0.,0.,1.));
#2851=DIRECTION('center_axis',(-1.,0.,0.));
#2852=DIRECTION('ref_axis',(0.,-1.,0.));
#2853=DIRECTION('',(0.,0.,1.));
#2854=DIRECTION('center_axis',(0.,0.,1.));
#2855=DIRECTION('ref_axis',(1.,0.,0.));
#2856=DIRECTION('',(0.,0.,1.));
#2857=DIRECTION('center_axis',(0.,-1.,0.));
#2858=DIRECTION('ref_axis',(1.,0.,0.));
#2859=DIRECTION('',(0.,0.,1.));
#2860=DIRECTION('center_axis',(0.,0.,1.));
#2861=DIRECTION('ref_axis',(0.,1.,0.));
#2862=DIRECTION('center_axis',(0.,0.,1.));
#2863=DIRECTION('ref_axis',(1.,0.,0.));
#2864=DIRECTION('',(0.,0.,-1.));
#2865=DIRECTION('center_axis',(0.,0.,1.));
#2866=DIRECTION('ref_axis',(1.,0.,0.));
#2867=DIRECTION('',(0.,0.,-1.));
#2868=DIRECTION('center_axis',(1.,0.,0.));
#2869=DIRECTION('ref_axis',(0.,1.,0.));
#2870=DIRECTION('',(0.,0.,1.));
#2871=DIRECTION('',(0.,0.,1.));
#2872=DIRECTION('center_axis',(0.,0.,1.));
#2873=DIRECTION('ref_axis',(-1.,0.,0.));
#2874=DIRECTION('',(0.,0.,1.));
#2875=DIRECTION('center_axis',(0.,1.,0.));
#2876=DIRECTION('ref_axis',(-1.,0.,0.));
#2877=DIRECTION('',(0.,0.,1.));
#2878=DIRECTION('center_axis',(0.,0.,1.));
#2879=DIRECTION('ref_axis',(0.,-1.,0.));
#2880=DIRECTION('',(0.,0.,1.));
#2881=DIRECTION('center_axis',(-1.,0.,0.));
#2882=DIRECTION('ref_axis',(0.,-1.,0.));
#2883=DIRECTION('',(0.,0.,1.));
#2884=DIRECTION('center_axis',(0.,0.,1.));
#2885=DIRECTION('ref_axis',(1.,0.,0.));
#2886=DIRECTION('',(0.,0.,1.));
#2887=DIRECTION('center_axis',(0.,-1.,0.));
#2888=DIRECTION('ref_axis',(1.,0.,0.));
#2889=DIRECTION('',(0.,0.,1.));
#2890=DIRECTION('center_axis',(0.,0.,1.));
#2891=DIRECTION('ref_axis',(0.,1.,0.));
#2892=DIRECTION('center_axis',(1.,0.,0.));
#2893=DIRECTION('ref_axis',(0.,1.,0.));
#2894=DIRECTION('',(0.,0.,1.));
#2895=DIRECTION('',(0.,0.,1.));
#2896=DIRECTION('center_axis',(0.,0.,1.));
#2897=DIRECTION('ref_axis',(-1.,0.,0.));
#2898=DIRECTION('',(0.,0.,1.));
#2899=DIRECTION('center_axis',(0.,1.,0.));
#2900=DIRECTION('ref_axis',(-1.,0.,0.));
#2901=DIRECTION('',(0.,0.,1.));
#2902=DIRECTION('center_axis',(0.,0.,1.));
#2903=DIRECTION('ref_axis',(0.,-1.,0.));
#2904=DIRECTION('',(0.,0.,1.));
#2905=DIRECTION('center_axis',(-1.,0.,0.));
#2906=DIRECTION('ref_axis',(0.,-1.,0.));
#2907=DIRECTION('',(0.,0.,1.));
#2908=DIRECTION('center_axis',(0.,0.,1.));
#2909=DIRECTION('ref_axis',(1.,0.,0.));
#2910=DIRECTION('',(0.,0.,1.));
#2911=DIRECTION('center_axis',(0.,-1.,0.));
#2912=DIRECTION('ref_axis',(1.,0.,0.));
#2913=DIRECTION('',(0.,0.,1.));
#2914=DIRECTION('center_axis',(0.,0.,1.));
#2915=DIRECTION('ref_axis',(0.,1.,0.));
#2916=DIRECTION('center_axis',(0.,-1.,0.));
#2917=DIRECTION('ref_axis',(1.,0.,0.));
#2918=DIRECTION('',(0.,0.,1.));
#2919=DIRECTION('',(1.,0.,0.));
#2920=DIRECTION('center_axis',(0.,0.,1.));
#2921=DIRECTION('ref_axis',(1.,0.,0.));
#2922=CARTESIAN_POINT('',(0.,0.,0.));
#2923=CARTESIAN_POINT('',(0.,0.,0.));
#2924=CARTESIAN_POINT('Origin',(18.753,44.786,0.75));
#2925=CARTESIAN_POINT('',(20.753,44.786,0.));
#2926=CARTESIAN_POINT('',(18.753,46.786,0.));
#2927=CARTESIAN_POINT('Origin',(18.753,44.786,0.));
#2928=CARTESIAN_POINT('',(18.753,46.786,1.5));
#2929=CARTESIAN_POINT('',(18.753,46.786,0.75));
#2930=CARTESIAN_POINT('',(20.753,44.786,1.5));
#2931=CARTESIAN_POINT('Origin',(18.753,44.786,1.5));
#2932=CARTESIAN_POINT('',(20.753,44.786,0.75));
#2933=CARTESIAN_POINT('Origin',(70.441,46.786,0.));
#2934=CARTESIAN_POINT('',(-6.472,46.786,0.));
#2935=CARTESIAN_POINT('',(70.441,46.786,0.));
#2936=CARTESIAN_POINT('',(-6.472,46.786,1.5));
#2937=CARTESIAN_POINT('',(-6.472,46.786,0.));
#2938=CARTESIAN_POINT('',(70.441,46.786,1.5));
#2939=CARTESIAN_POINT('Origin',(29.4845,-10.839,0.));
#2940=CARTESIAN_POINT('',(20.753,30.286,0.));
#2941=CARTESIAN_POINT('',(20.753,8.72349999999999,0.));
#2942=CARTESIAN_POINT('',(22.753,28.286,0.));
#2943=CARTESIAN_POINT('Origin',(22.753,30.286,0.));
#2944=CARTESIAN_POINT('',(37.253,28.286,0.));
#2945=CARTESIAN_POINT('',(34.36875,28.286,0.));
#2946=CARTESIAN_POINT('',(39.253,30.286,0.));
#2947=CARTESIAN_POINT('Origin',(37.253,30.286,0.));
#2948=CARTESIAN_POINT('',(39.253,44.786,0.));
#2949=CARTESIAN_POINT('',(39.253,17.9735,0.));
#2950=CARTESIAN_POINT('',(41.253,46.786,0.));
#2951=CARTESIAN_POINT('Origin',(41.253,44.786,0.));
#2952=CARTESIAN_POINT('',(65.441,46.786,0.));
#2953=CARTESIAN_POINT('',(70.441,46.786,0.));
#2954=CARTESIAN_POINT('',(70.441,41.786,0.));
#2955=CARTESIAN_POINT('Origin',(65.441,41.786,0.));
#2956=CARTESIAN_POINT('',(70.441,-63.464,0.));
#2957=CARTESIAN_POINT('',(70.441,-68.464,0.));
#2958=CARTESIAN_POINT('',(65.441,-68.464,0.));
#2959=CARTESIAN_POINT('Origin',(65.441,-63.464,0.));
#2960=CARTESIAN_POINT('',(-6.472,-68.464,0.));
#2961=CARTESIAN_POINT('',(-11.472,-68.464,0.));
#2962=CARTESIAN_POINT('',(-11.472,-63.464,0.));
#2963=CARTESIAN_POINT('Origin',(-6.472,-63.464,0.));
#2964=CARTESIAN_POINT('',(-11.472,41.786,0.));
#2965=CARTESIAN_POINT('',(-11.472,46.786,0.));
#2966=CARTESIAN_POINT('Origin',(-6.472,41.786,0.));
#2967=CARTESIAN_POINT('',(2.525,-41.125,0.));
#2968=CARTESIAN_POINT('',(3.025,-40.625,0.));
#2969=CARTESIAN_POINT('Origin',(3.025,-41.125,0.));
#2970=CARTESIAN_POINT('',(2.525,-54.125,0.));
#2971=CARTESIAN_POINT('',(2.525,-41.125,0.));
#2972=CARTESIAN_POINT('',(3.025,-54.625,0.));
#2973=CARTESIAN_POINT('Origin',(3.025,-54.125,0.));
#2974=CARTESIAN_POINT('',(16.025,-54.625,0.));
#2975=CARTESIAN_POINT('',(3.025,-54.625,0.));
#2976=CARTESIAN_POINT('',(16.525,-54.125,0.));
#2977=CARTESIAN_POINT('Origin',(16.025,-54.125,0.));
#2978=CARTESIAN_POINT('',(16.525,-41.125,0.));
#2979=CARTESIAN_POINT('',(16.525,-41.125,0.));
#2980=CARTESIAN_POINT('',(16.025,-40.625,0.));
#2981=CARTESIAN_POINT('Origin',(16.025,-41.125,0.));
#2982=CARTESIAN_POINT('',(3.025,-40.625,0.));
#2983=CARTESIAN_POINT('',(2.525,-3.025,0.));
#2984=CARTESIAN_POINT('',(3.025,-2.525,0.));
#2985=CARTESIAN_POINT('Origin',(3.025,-3.025,0.));
#2986=CARTESIAN_POINT('',(2.525,-16.025,0.));
#2987=CARTESIAN_POINT('',(2.525,-3.025,0.));
#2988=CARTESIAN_POINT('',(3.025,-16.525,0.));
#2989=CARTESIAN_POINT('Origin',(3.025,-16.025,0.));
#2990=CARTESIAN_POINT('',(16.025,-16.525,0.));
#2991=CARTESIAN_POINT('',(3.025,-16.525,0.));
#2992=CARTESIAN_POINT('',(16.525,-16.025,0.));
#2993=CARTESIAN_POINT('Origin',(16.025,-16.025,0.));
#2994=CARTESIAN_POINT('',(16.525,-3.025,0.));
#2995=CARTESIAN_POINT('',(16.525,-3.025,0.));
#2996=CARTESIAN_POINT('',(16.025,-2.525,0.));
#2997=CARTESIAN_POINT('Origin',(16.025,-3.025,0.));
#2998=CARTESIAN_POINT('',(3.025,-2.525,0.));
#2999=CARTESIAN_POINT('',(58.395,2.28599999999999,0.));
#3000=CARTESIAN_POINT('',(57.395,1.28599999999999,0.));
#3001=CARTESIAN_POINT('Origin',(57.395,2.28599999999999,0.));
#3002=CARTESIAN_POINT('',(58.395,14.286,0.));
#3003=CARTESIAN_POINT('',(58.395,15.286,0.));
#3004=CARTESIAN_POINT('',(57.395,15.286,0.));
#3005=CARTESIAN_POINT('Origin',(57.395,14.286,0.));
#3006=CARTESIAN_POINT('',(1.574,15.286,0.));
#3007=CARTESIAN_POINT('',(0.573999999999999,15.286,0.));
#3008=CARTESIAN_POINT('',(0.573999999999999,14.286,0.));
#3009=CARTESIAN_POINT('Origin',(1.574,14.286,0.));
#3010=CARTESIAN_POINT('',(0.573999999999999,2.28599999999999,0.));
#3011=CARTESIAN_POINT('',(0.573999999999999,1.28599999999999,0.));
#3012=CARTESIAN_POINT('',(1.574,1.28599999999999,0.));
#3013=CARTESIAN_POINT('Origin',(1.574,2.28599999999999,0.));
#3014=CARTESIAN_POINT('',(58.395,1.28599999999999,0.));
#3015=CARTESIAN_POINT('',(63.741,-63.464,0.));
#3016=CARTESIAN_POINT('Origin',(65.441,-63.464,0.));
#3017=CARTESIAN_POINT('',(-8.172,41.786,0.));
#3018=CARTESIAN_POINT('Origin',(-6.472,41.786,0.));
#3019=CARTESIAN_POINT('',(21.575,-3.025,0.));
#3020=CARTESIAN_POINT('',(22.075,-2.525,0.));
#3021=CARTESIAN_POINT('Origin',(22.075,-3.025,0.));
#3022=CARTESIAN_POINT('',(21.575,-16.025,0.));
#3023=CARTESIAN_POINT('',(21.575,-3.025,0.));
#3024=CARTESIAN_POINT('',(22.075,-16.525,0.));
#3025=CARTESIAN_POINT('Origin',(22.075,-16.025,0.));
#3026=CARTESIAN_POINT('',(35.075,-16.525,0.));
#3027=CARTESIAN_POINT('',(22.075,-16.525,0.));
#3028=CARTESIAN_POINT('',(35.575,-16.025,0.));
#3029=CARTESIAN_POINT('Origin',(35.075,-16.025,0.));
#3030=CARTESIAN_POINT('',(35.575,-3.025,0.));
#3031=CARTESIAN_POINT('',(35.575,-3.025,0.));
#3032=CARTESIAN_POINT('',(35.075,-2.525,0.));
#3033=CARTESIAN_POINT('Origin',(35.075,-3.025,0.));
#3034=CARTESIAN_POINT('',(22.075,-2.525,0.));
#3035=CARTESIAN_POINT('',(21.575,-22.075,0.));
#3036=CARTESIAN_POINT('',(22.075,-21.575,0.));
#3037=CARTESIAN_POINT('Origin',(22.075,-22.075,0.));
#3038=CARTESIAN_POINT('',(21.575,-35.075,0.));
#3039=CARTESIAN_POINT('',(21.575,-22.075,0.));
#3040=CARTESIAN_POINT('',(22.075,-35.575,0.));
#3041=CARTESIAN_POINT('Origin',(22.075,-35.075,0.));
#3042=CARTESIAN_POINT('',(35.075,-35.575,0.));
#3043=CARTESIAN_POINT('',(22.075,-35.575,0.));
#3044=CARTESIAN_POINT('',(35.575,-35.075,0.));
#3045=CARTESIAN_POINT('Origin',(35.075,-35.075,0.));
#3046=CARTESIAN_POINT('',(35.575,-22.075,0.));
#3047=CARTESIAN_POINT('',(35.575,-22.075,0.));
#3048=CARTESIAN_POINT('',(35.075,-21.575,0.));
#3049=CARTESIAN_POINT('Origin',(35.075,-22.075,0.));
#3050=CARTESIAN_POINT('',(22.075,-21.575,0.));
#3051=CARTESIAN_POINT('',(63.741,41.786,0.));
#3052=CARTESIAN_POINT('Origin',(65.441,41.786,0.));
#3053=CARTESIAN_POINT('',(40.625,-41.125,0.));
#3054=CARTESIAN_POINT('',(41.125,-40.625,0.));
#3055=CARTESIAN_POINT('Origin',(41.125,-41.125,0.));
#3056=CARTESIAN_POINT('',(40.625,-54.125,0.));
#3057=CARTESIAN_POINT('',(40.625,-41.125,0.));
#3058=CARTESIAN_POINT('',(41.125,-54.625,0.));
#3059=CARTESIAN_POINT('Origin',(41.125,-54.125,0.));
#3060=CARTESIAN_POINT('',(54.125,-54.625,0.));
#3061=CARTESIAN_POINT('',(41.125,-54.625,0.));
#3062=CARTESIAN_POINT('',(54.625,-54.125,0.));
#3063=CARTESIAN_POINT('Origin',(54.125,-54.125,0.));
#3064=CARTESIAN_POINT('',(54.625,-41.125,0.));
#3065=CARTESIAN_POINT('',(54.625,-41.125,0.));
#3066=CARTESIAN_POINT('',(54.125,-40.625,0.));
#3067=CARTESIAN_POINT('Origin',(54.125,-41.125,0.));
#3068=CARTESIAN_POINT('',(41.125,-40.625,0.));
#3069=CARTESIAN_POINT('',(-8.172,-63.464,0.));
#3070=CARTESIAN_POINT('Origin',(-6.472,-63.464,0.));
#3071=CARTESIAN_POINT('',(21.575,-41.125,0.));
#3072=CARTESIAN_POINT('',(22.075,-40.625,0.));
#3073=CARTESIAN_POINT('Origin',(22.075,-41.125,0.));
#3074=CARTESIAN_POINT('',(21.575,-54.125,0.));
#3075=CARTESIAN_POINT('',(21.575,-41.125,0.));
#3076=CARTESIAN_POINT('',(22.075,-54.625,0.));
#3077=CARTESIAN_POINT('Origin',(22.075,-54.125,0.));
#3078=CARTESIAN_POINT('',(35.075,-54.625,0.));
#3079=CARTESIAN_POINT('',(22.075,-54.625,0.));
#3080=CARTESIAN_POINT('',(35.575,-54.125,0.));
#3081=CARTESIAN_POINT('Origin',(35.075,-54.125,0.));
#3082=CARTESIAN_POINT('',(35.575,-41.125,0.));
#3083=CARTESIAN_POINT('',(35.575,-41.125,0.));
#3084=CARTESIAN_POINT('',(35.075,-40.625,0.));
#3085=CARTESIAN_POINT('Origin',(35.075,-41.125,0.));
#3086=CARTESIAN_POINT('',(22.075,-40.625,0.));
#3087=CARTESIAN_POINT('',(2.525,-22.075,0.));
#3088=CARTESIAN_POINT('',(3.025,-21.575,0.));
#3089=CARTESIAN_POINT('Origin',(3.025,-22.075,0.));
#3090=CARTESIAN_POINT('',(2.525,-35.075,0.));
#3091=CARTESIAN_POINT('',(2.525,-22.075,0.));
#3092=CARTESIAN_POINT('',(3.025,-35.575,0.));
#3093=CARTESIAN_POINT('Origin',(3.025,-35.075,0.));
#3094=CARTESIAN_POINT('',(16.025,-35.575,0.));
#3095=CARTESIAN_POINT('',(3.025,-35.575,0.));
#3096=CARTESIAN_POINT('',(16.525,-35.075,0.));
#3097=CARTESIAN_POINT('Origin',(16.025,-35.075,0.));
#3098=CARTESIAN_POINT('',(16.525,-22.075,0.));
#3099=CARTESIAN_POINT('',(16.525,-22.075,0.));
#3100=CARTESIAN_POINT('',(16.025,-21.575,0.));
#3101=CARTESIAN_POINT('Origin',(16.025,-22.075,0.));
#3102=CARTESIAN_POINT('',(3.025,-21.575,0.));
#3103=CARTESIAN_POINT('',(40.625,-3.025,0.));
#3104=CARTESIAN_POINT('',(41.125,-2.525,0.));
#3105=CARTESIAN_POINT('Origin',(41.125,-3.025,0.));
#3106=CARTESIAN_POINT('',(40.625,-16.025,0.));
#3107=CARTESIAN_POINT('',(40.625,-3.025,0.));
#3108=CARTESIAN_POINT('',(41.125,-16.525,0.));
#3109=CARTESIAN_POINT('Origin',(41.125,-16.025,0.));
#3110=CARTESIAN_POINT('',(54.125,-16.525,0.));
#3111=CARTESIAN_POINT('',(41.125,-16.525,0.));
#3112=CARTESIAN_POINT('',(54.625,-16.025,0.));
#3113=CARTESIAN_POINT('Origin',(54.125,-16.025,0.));
#3114=CARTESIAN_POINT('',(54.625,-3.025,0.));
#3115=CARTESIAN_POINT('',(54.625,-3.025,0.));
#3116=CARTESIAN_POINT('',(54.125,-2.525,0.));
#3117=CARTESIAN_POINT('Origin',(54.125,-3.025,0.));
#3118=CARTESIAN_POINT('',(41.125,-2.525,0.));
#3119=CARTESIAN_POINT('',(40.625,-22.075,0.));
#3120=CARTESIAN_POINT('',(41.125,-21.575,0.));
#3121=CARTESIAN_POINT('Origin',(41.125,-22.075,0.));
#3122=CARTESIAN_POINT('',(40.625,-35.075,0.));
#3123=CARTESIAN_POINT('',(40.625,-22.075,0.));
#3124=CARTESIAN_POINT('',(41.125,-35.575,0.));
#3125=CARTESIAN_POINT('Origin',(41.125,-35.075,0.));
#3126=CARTESIAN_POINT('',(54.125,-35.575,0.));
#3127=CARTESIAN_POINT('',(41.125,-35.575,0.));
#3128=CARTESIAN_POINT('',(54.625,-35.075,0.));
#3129=CARTESIAN_POINT('Origin',(54.125,-35.075,0.));
#3130=CARTESIAN_POINT('',(54.625,-22.075,0.));
#3131=CARTESIAN_POINT('',(54.625,-22.075,0.));
#3132=CARTESIAN_POINT('',(54.125,-21.575,0.));
#3133=CARTESIAN_POINT('Origin',(54.125,-22.075,0.));
#3134=CARTESIAN_POINT('',(41.125,-21.575,0.));
#3135=CARTESIAN_POINT('Origin',(41.253,44.786,0.75));
#3136=CARTESIAN_POINT('',(39.253,44.786,1.5));
#3137=CARTESIAN_POINT('',(41.253,46.786,1.5));
#3138=CARTESIAN_POINT('Origin',(41.253,44.786,1.5));
#3139=CARTESIAN_POINT('',(41.253,46.786,0.75));
#3140=CARTESIAN_POINT('',(39.253,44.786,0.75));
#3141=CARTESIAN_POINT('Origin',(70.441,46.786,0.));
#3142=CARTESIAN_POINT('',(65.441,46.786,1.5));
#3143=CARTESIAN_POINT('',(70.441,46.786,1.5));
#3144=CARTESIAN_POINT('',(65.441,46.786,0.));
#3145=CARTESIAN_POINT('Origin',(22.753,30.286,1.5));
#3146=CARTESIAN_POINT('',(20.753,30.286,1.5));
#3147=CARTESIAN_POINT('',(22.753,28.286,1.5));
#3148=CARTESIAN_POINT('Origin',(22.753,30.286,1.5));
#3149=CARTESIAN_POINT('',(22.753,28.286,1.5));
#3150=CARTESIAN_POINT('',(20.753,30.286,1.5));
#3151=CARTESIAN_POINT('Origin',(39.253,28.286,1.5));
#3152=CARTESIAN_POINT('',(37.253,28.286,1.5));
#3153=CARTESIAN_POINT('',(34.36875,28.286,1.5));
#3154=CARTESIAN_POINT('',(37.253,28.286,1.5));
#3155=CARTESIAN_POINT('Origin',(37.253,30.286,1.5));
#3156=CARTESIAN_POINT('',(39.253,30.286,1.5));
#3157=CARTESIAN_POINT('Origin',(37.253,30.286,1.5));
#3158=CARTESIAN_POINT('',(39.253,30.286,1.5));
#3159=CARTESIAN_POINT('Origin',(39.253,46.786,1.5));
#3160=CARTESIAN_POINT('',(39.253,17.9735,1.5));
#3161=CARTESIAN_POINT('Origin',(20.753,28.286,1.5));
#3162=CARTESIAN_POINT('',(20.753,8.72349999999999,1.5));
#3163=CARTESIAN_POINT('Origin',(29.4845,-10.839,1.5));
#3164=CARTESIAN_POINT('',(-11.472,41.786,1.5));
#3165=CARTESIAN_POINT('Origin',(-6.472,41.786,1.5));
#3166=CARTESIAN_POINT('',(-11.472,-63.464,1.5));
#3167=CARTESIAN_POINT('',(-11.472,46.786,1.5));
#3168=CARTESIAN_POINT('',(-6.472,-68.464,1.5));
#3169=CARTESIAN_POINT('Origin',(-6.472,-63.464,1.5));
#3170=CARTESIAN_POINT('',(65.441,-68.464,1.5));
#3171=CARTESIAN_POINT('',(-11.472,-68.464,1.5));
#3172=CARTESIAN_POINT('',(70.441,-63.464,1.5));
#3173=CARTESIAN_POINT('Origin',(65.441,-63.464,1.5));
#3174=CARTESIAN_POINT('',(70.441,41.786,1.5));
#3175=CARTESIAN_POINT('',(70.441,-68.464,1.5));
#3176=CARTESIAN_POINT('Origin',(65.441,41.786,1.5));
#3177=CARTESIAN_POINT('',(40.625,-22.075,1.5));
#3178=CARTESIAN_POINT('',(41.125,-21.575,1.5));
#3179=CARTESIAN_POINT('Origin',(41.125,-22.075,1.5));
#3180=CARTESIAN_POINT('',(54.125,-21.575,1.5));
#3181=CARTESIAN_POINT('',(41.125,-21.575,1.5));
#3182=CARTESIAN_POINT('',(54.625,-22.075,1.5));
#3183=CARTESIAN_POINT('Origin',(54.125,-22.075,1.5));
#3184=CARTESIAN_POINT('',(54.625,-35.075,1.5));
#3185=CARTESIAN_POINT('',(54.625,-22.075,1.5));
#3186=CARTESIAN_POINT('',(54.125,-35.575,1.5));
#3187=CARTESIAN_POINT('Origin',(54.125,-35.075,1.5));
#3188=CARTESIAN_POINT('',(41.125,-35.575,1.5));
#3189=CARTESIAN_POINT('',(41.125,-35.575,1.5));
#3190=CARTESIAN_POINT('',(40.625,-35.075,1.5));
#3191=CARTESIAN_POINT('Origin',(41.125,-35.075,1.5));
#3192=CARTESIAN_POINT('',(40.625,-22.075,1.5));
#3193=CARTESIAN_POINT('',(40.625,-3.025,1.5));
#3194=CARTESIAN_POINT('',(41.125,-2.525,1.5));
#3195=CARTESIAN_POINT('Origin',(41.125,-3.025,1.5));
#3196=CARTESIAN_POINT('',(54.125,-2.525,1.5));
#3197=CARTESIAN_POINT('',(41.125,-2.525,1.5));
#3198=CARTESIAN_POINT('',(54.625,-3.025,1.5));
#3199=CARTESIAN_POINT('Origin',(54.125,-3.025,1.5));
#3200=CARTESIAN_POINT('',(54.625,-16.025,1.5));
#3201=CARTESIAN_POINT('',(54.625,-3.025,1.5));
#3202=CARTESIAN_POINT('',(54.125,-16.525,1.5));
#3203=CARTESIAN_POINT('Origin',(54.125,-16.025,1.5));
#3204=CARTESIAN_POINT('',(41.125,-16.525,1.5));
#3205=CARTESIAN_POINT('',(41.125,-16.525,1.5));
#3206=CARTESIAN_POINT('',(40.625,-16.025,1.5));
#3207=CARTESIAN_POINT('Origin',(41.125,-16.025,1.5));
#3208=CARTESIAN_POINT('',(40.625,-3.025,1.5));
#3209=CARTESIAN_POINT('',(2.525,-22.075,1.5));
#3210=CARTESIAN_POINT('',(3.025,-21.575,1.5));
#3211=CARTESIAN_POINT('Origin',(3.025,-22.075,1.5));
#3212=CARTESIAN_POINT('',(16.025,-21.575,1.5));
#3213=CARTESIAN_POINT('',(3.025,-21.575,1.5));
#3214=CARTESIAN_POINT('',(16.525,-22.075,1.5));
#3215=CARTESIAN_POINT('Origin',(16.025,-22.075,1.5));
#3216=CARTESIAN_POINT('',(16.525,-35.075,1.5));
#3217=CARTESIAN_POINT('',(16.525,-22.075,1.5));
#3218=CARTESIAN_POINT('',(16.025,-35.575,1.5));
#3219=CARTESIAN_POINT('Origin',(16.025,-35.075,1.5));
#3220=CARTESIAN_POINT('',(3.025,-35.575,1.5));
#3221=CARTESIAN_POINT('',(3.025,-35.575,1.5));
#3222=CARTESIAN_POINT('',(2.525,-35.075,1.5));
#3223=CARTESIAN_POINT('Origin',(3.025,-35.075,1.5));
#3224=CARTESIAN_POINT('',(2.525,-22.075,1.5));
#3225=CARTESIAN_POINT('',(21.575,-41.125,1.5));
#3226=CARTESIAN_POINT('',(22.075,-40.625,1.5));
#3227=CARTESIAN_POINT('Origin',(22.075,-41.125,1.5));
#3228=CARTESIAN_POINT('',(35.075,-40.625,1.5));
#3229=CARTESIAN_POINT('',(22.075,-40.625,1.5));
#3230=CARTESIAN_POINT('',(35.575,-41.125,1.5));
#3231=CARTESIAN_POINT('Origin',(35.075,-41.125,1.5));
#3232=CARTESIAN_POINT('',(35.575,-54.125,1.5));
#3233=CARTESIAN_POINT('',(35.575,-41.125,1.5));
#3234=CARTESIAN_POINT('',(35.075,-54.625,1.5));
#3235=CARTESIAN_POINT('Origin',(35.075,-54.125,1.5));
#3236=CARTESIAN_POINT('',(22.075,-54.625,1.5));
#3237=CARTESIAN_POINT('',(22.075,-54.625,1.5));
#3238=CARTESIAN_POINT('',(21.575,-54.125,1.5));
#3239=CARTESIAN_POINT('Origin',(22.075,-54.125,1.5));
#3240=CARTESIAN_POINT('',(21.575,-41.125,1.5));
#3241=CARTESIAN_POINT('',(-8.172,-63.464,1.5));
#3242=CARTESIAN_POINT('Origin',(-6.472,-63.464,1.5));
#3243=CARTESIAN_POINT('',(40.625,-41.125,1.5));
#3244=CARTESIAN_POINT('',(41.125,-40.625,1.5));
#3245=CARTESIAN_POINT('Origin',(41.125,-41.125,1.5));
#3246=CARTESIAN_POINT('',(54.125,-40.625,1.5));
#3247=CARTESIAN_POINT('',(41.125,-40.625,1.5));
#3248=CARTESIAN_POINT('',(54.625,-41.125,1.5));
#3249=CARTESIAN_POINT('Origin',(54.125,-41.125,1.5));
#3250=CARTESIAN_POINT('',(54.625,-54.125,1.5));
#3251=CARTESIAN_POINT('',(54.625,-41.125,1.5));
#3252=CARTESIAN_POINT('',(54.125,-54.625,1.5));
#3253=CARTESIAN_POINT('Origin',(54.125,-54.125,1.5));
#3254=CARTESIAN_POINT('',(41.125,-54.625,1.5));
#3255=CARTESIAN_POINT('',(41.125,-54.625,1.5));
#3256=CARTESIAN_POINT('',(40.625,-54.125,1.5));
#3257=CARTESIAN_POINT('Origin',(41.125,-54.125,1.5));
#3258=CARTESIAN_POINT('',(40.625,-41.125,1.5));
#3259=CARTESIAN_POINT('',(63.741,41.786,1.5));
#3260=CARTESIAN_POINT('Origin',(65.441,41.786,1.5));
#3261=CARTESIAN_POINT('',(21.575,-22.075,1.5));
#3262=CARTESIAN_POINT('',(22.075,-21.575,1.5));
#3263=CARTESIAN_POINT('Origin',(22.075,-22.075,1.5));
#3264=CARTESIAN_POINT('',(35.075,-21.575,1.5));
#3265=CARTESIAN_POINT('',(22.075,-21.575,1.5));
#3266=CARTESIAN_POINT('',(35.575,-22.075,1.5));
#3267=CARTESIAN_POINT('Origin',(35.075,-22.075,1.5));
#3268=CARTESIAN_POINT('',(35.575,-35.075,1.5));
#3269=CARTESIAN_POINT('',(35.575,-22.075,1.5));
#3270=CARTESIAN_POINT('',(35.075,-35.575,1.5));
#3271=CARTESIAN_POINT('Origin',(35.075,-35.075,1.5));
#3272=CARTESIAN_POINT('',(22.075,-35.575,1.5));
#3273=CARTESIAN_POINT('',(22.075,-35.575,1.5));
#3274=CARTESIAN_POINT('',(21.575,-35.075,1.5));
#3275=CARTESIAN_POINT('Origin',(22.075,-35.075,1.5));
#3276=CARTESIAN_POINT('',(21.575,-22.075,1.5));
#3277=CARTESIAN_POINT('',(21.575,-3.025,1.5));
#3278=CARTESIAN_POINT('',(22.075,-2.525,1.5));
#3279=CARTESIAN_POINT('Origin',(22.075,-3.025,1.5));
#3280=CARTESIAN_POINT('',(35.075,-2.525,1.5));
#3281=CARTESIAN_POINT('',(22.075,-2.525,1.5));
#3282=CARTESIAN_POINT('',(35.575,-3.025,1.5));
#3283=CARTESIAN_POINT('Origin',(35.075,-3.025,1.5));
#3284=CARTESIAN_POINT('',(35.575,-16.025,1.5));
#3285=CARTESIAN_POINT('',(35.575,-3.025,1.5));
#3286=CARTESIAN_POINT('',(35.075,-16.525,1.5));
#3287=CARTESIAN_POINT('Origin',(35.075,-16.025,1.5));
#3288=CARTESIAN_POINT('',(22.075,-16.525,1.5));
#3289=CARTESIAN_POINT('',(22.075,-16.525,1.5));
#3290=CARTESIAN_POINT('',(21.575,-16.025,1.5));
#3291=CARTESIAN_POINT('Origin',(22.075,-16.025,1.5));
#3292=CARTESIAN_POINT('',(21.575,-3.025,1.5));
#3293=CARTESIAN_POINT('',(-8.172,41.786,1.5));
#3294=CARTESIAN_POINT('Origin',(-6.472,41.786,1.5));
#3295=CARTESIAN_POINT('',(63.741,-63.464,1.5));
#3296=CARTESIAN_POINT('Origin',(65.441,-63.464,1.5));
#3297=CARTESIAN_POINT('',(57.395,1.28599999999999,1.5));
#3298=CARTESIAN_POINT('',(1.574,1.28599999999999,1.5));
#3299=CARTESIAN_POINT('',(58.395,1.28599999999999,1.5));
#3300=CARTESIAN_POINT('',(0.573999999999999,2.28599999999999,1.5));
#3301=CARTESIAN_POINT('Origin',(1.574,2.28599999999999,1.5));
#3302=CARTESIAN_POINT('',(0.573999999999999,14.286,1.5));
#3303=CARTESIAN_POINT('',(0.573999999999999,1.28599999999999,1.5));
#3304=CARTESIAN_POINT('',(1.574,15.286,1.5));
#3305=CARTESIAN_POINT('Origin',(1.574,14.286,1.5));
#3306=CARTESIAN_POINT('',(57.395,15.286,1.5));
#3307=CARTESIAN_POINT('',(0.573999999999999,15.286,1.5));
#3308=CARTESIAN_POINT('',(58.395,14.286,1.5));
#3309=CARTESIAN_POINT('Origin',(57.395,14.286,1.5));
#3310=CARTESIAN_POINT('',(58.395,2.28599999999999,1.5));
#3311=CARTESIAN_POINT('',(58.395,15.286,1.5));
#3312=CARTESIAN_POINT('Origin',(57.395,2.28599999999999,1.5));
#3313=CARTESIAN_POINT('',(2.525,-3.025,1.5));
#3314=CARTESIAN_POINT('',(3.025,-2.525,1.5));
#3315=CARTESIAN_POINT('Origin',(3.025,-3.025,1.5));
#3316=CARTESIAN_POINT('',(16.025,-2.525,1.5));
#3317=CARTESIAN_POINT('',(3.025,-2.525,1.5));
#3318=CARTESIAN_POINT('',(16.525,-3.025,1.5));
#3319=CARTESIAN_POINT('Origin',(16.025,-3.025,1.5));
#3320=CARTESIAN_POINT('',(16.525,-16.025,1.5));
#3321=CARTESIAN_POINT('',(16.525,-3.025,1.5));
#3322=CARTESIAN_POINT('',(16.025,-16.525,1.5));
#3323=CARTESIAN_POINT('Origin',(16.025,-16.025,1.5));
#3324=CARTESIAN_POINT('',(3.025,-16.525,1.5));
#3325=CARTESIAN_POINT('',(3.025,-16.525,1.5));
#3326=CARTESIAN_POINT('',(2.525,-16.025,1.5));
#3327=CARTESIAN_POINT('Origin',(3.025,-16.025,1.5));
#3328=CARTESIAN_POINT('',(2.525,-3.025,1.5));
#3329=CARTESIAN_POINT('',(2.525,-41.125,1.5));
#3330=CARTESIAN_POINT('',(3.025,-40.625,1.5));
#3331=CARTESIAN_POINT('Origin',(3.025,-41.125,1.5));
#3332=CARTESIAN_POINT('',(16.025,-40.625,1.5));
#3333=CARTESIAN_POINT('',(3.025,-40.625,1.5));
#3334=CARTESIAN_POINT('',(16.525,-41.125,1.5));
#3335=CARTESIAN_POINT('Origin',(16.025,-41.125,1.5));
#3336=CARTESIAN_POINT('',(16.525,-54.125,1.5));
#3337=CARTESIAN_POINT('',(16.525,-41.125,1.5));
#3338=CARTESIAN_POINT('',(16.025,-54.625,1.5));
#3339=CARTESIAN_POINT('Origin',(16.025,-54.125,1.5));
#3340=CARTESIAN_POINT('',(3.025,-54.625,1.5));
#3341=CARTESIAN_POINT('',(3.025,-54.625,1.5));
#3342=CARTESIAN_POINT('',(2.525,-54.125,1.5));
#3343=CARTESIAN_POINT('Origin',(3.025,-54.125,1.5));
#3344=CARTESIAN_POINT('',(2.525,-41.125,1.5));
#3345=CARTESIAN_POINT('Origin',(1.574,2.28599999999999,0.));
#3346=CARTESIAN_POINT('',(0.573999999999999,2.28599999999999,0.));
#3347=CARTESIAN_POINT('',(1.574,1.28599999999999,0.));
#3348=CARTESIAN_POINT('Origin',(0.573999999999999,1.28599999999999,0.));
#3349=CARTESIAN_POINT('',(0.573999999999999,14.286,0.));
#3350=CARTESIAN_POINT('Origin',(57.395,2.28599999999999,0.));
#3351=CARTESIAN_POINT('',(57.395,1.28599999999999,0.));
#3352=CARTESIAN_POINT('',(58.395,2.28599999999999,0.));
#3353=CARTESIAN_POINT('Origin',(58.395,1.28599999999999,0.));
#3354=CARTESIAN_POINT('Origin',(57.395,14.286,0.));
#3355=CARTESIAN_POINT('',(58.395,14.286,0.));
#3356=CARTESIAN_POINT('',(57.395,15.286,0.));
#3357=CARTESIAN_POINT('Origin',(58.395,15.286,0.));
#3358=CARTESIAN_POINT('Origin',(1.574,14.286,0.));
#3359=CARTESIAN_POINT('',(1.574,15.286,0.));
#3360=CARTESIAN_POINT('Origin',(0.573999999999999,15.286,0.));
#3361=CARTESIAN_POINT('Origin',(-6.472,41.786,0.));
#3362=CARTESIAN_POINT('',(-11.472,41.786,0.));
#3363=CARTESIAN_POINT('Origin',(-11.472,46.786,0.));
#3364=CARTESIAN_POINT('',(-11.472,-63.464,0.));
#3365=CARTESIAN_POINT('Origin',(65.441,41.786,0.));
#3366=CARTESIAN_POINT('',(70.441,41.786,0.));
#3367=CARTESIAN_POINT('Origin',(70.441,-68.464,0.));
#3368=CARTESIAN_POINT('',(70.441,-63.464,0.));
#3369=CARTESIAN_POINT('Origin',(65.441,-63.464,0.));
#3370=CARTESIAN_POINT('',(65.441,-68.464,0.));
#3371=CARTESIAN_POINT('Origin',(-6.472,-63.464,0.));
#3372=CARTESIAN_POINT('',(-6.472,-68.464,0.));
#3373=CARTESIAN_POINT('Origin',(40.625,-35.075,0.));
#3374=CARTESIAN_POINT('',(40.625,-22.075,0.));
#3375=CARTESIAN_POINT('',(40.625,-35.075,0.));
#3376=CARTESIAN_POINT('Origin',(41.125,-35.075,0.));
#3377=CARTESIAN_POINT('',(41.125,-35.575,0.));
#3378=CARTESIAN_POINT('Origin',(54.125,-35.575,0.));
#3379=CARTESIAN_POINT('',(54.125,-35.575,0.));
#3380=CARTESIAN_POINT('Origin',(54.125,-35.075,0.));
#3381=CARTESIAN_POINT('',(54.625,-35.075,0.));
#3382=CARTESIAN_POINT('Origin',(54.625,-22.075,0.));
#3383=CARTESIAN_POINT('',(54.625,-22.075,0.));
#3384=CARTESIAN_POINT('Origin',(54.125,-22.075,0.));
#3385=CARTESIAN_POINT('',(54.125,-21.575,0.));
#3386=CARTESIAN_POINT('Origin',(41.125,-21.575,0.));
#3387=CARTESIAN_POINT('',(41.125,-21.575,0.));
#3388=CARTESIAN_POINT('Origin',(41.125,-22.075,0.));
#3389=CARTESIAN_POINT('Origin',(40.625,-16.025,0.));
#3390=CARTESIAN_POINT('',(40.625,-3.025,0.));
#3391=CARTESIAN_POINT('',(40.625,-16.025,0.));
#3392=CARTESIAN_POINT('Origin',(41.125,-16.025,0.));
#3393=CARTESIAN_POINT('',(41.125,-16.525,0.));
#3394=CARTESIAN_POINT('Origin',(54.125,-16.525,0.));
#3395=CARTESIAN_POINT('',(54.125,-16.525,0.));
#3396=CARTESIAN_POINT('Origin',(54.125,-16.025,0.));
#3397=CARTESIAN_POINT('',(54.625,-16.025,0.));
#3398=CARTESIAN_POINT('Origin',(54.625,-3.025,0.));
#3399=CARTESIAN_POINT('',(54.625,-3.025,0.));
#3400=CARTESIAN_POINT('Origin',(54.125,-3.025,0.));
#3401=CARTESIAN_POINT('',(54.125,-2.525,0.));
#3402=CARTESIAN_POINT('Origin',(41.125,-2.525,0.));
#3403=CARTESIAN_POINT('',(41.125,-2.525,0.));
#3404=CARTESIAN_POINT('Origin',(41.125,-3.025,0.));
#3405=CARTESIAN_POINT('Origin',(2.525,-35.075,0.));
#3406=CARTESIAN_POINT('',(2.525,-22.075,0.));
#3407=CARTESIAN_POINT('',(2.525,-35.075,0.));
#3408=CARTESIAN_POINT('Origin',(3.025,-35.075,0.));
#3409=CARTESIAN_POINT('',(3.025,-35.575,0.));
#3410=CARTESIAN_POINT('Origin',(16.025,-35.575,0.));
#3411=CARTESIAN_POINT('',(16.025,-35.575,0.));
#3412=CARTESIAN_POINT('Origin',(16.025,-35.075,0.));
#3413=CARTESIAN_POINT('',(16.525,-35.075,0.));
#3414=CARTESIAN_POINT('Origin',(16.525,-22.075,0.));
#3415=CARTESIAN_POINT('',(16.525,-22.075,0.));
#3416=CARTESIAN_POINT('Origin',(16.025,-22.075,0.));
#3417=CARTESIAN_POINT('',(16.025,-21.575,0.));
#3418=CARTESIAN_POINT('Origin',(3.025,-21.575,0.));
#3419=CARTESIAN_POINT('',(3.025,-21.575,0.));
#3420=CARTESIAN_POINT('Origin',(3.025,-22.075,0.));
#3421=CARTESIAN_POINT('Origin',(21.575,-54.125,0.));
#3422=CARTESIAN_POINT('',(21.575,-41.125,0.));
#3423=CARTESIAN_POINT('',(21.575,-54.125,0.));
#3424=CARTESIAN_POINT('Origin',(22.075,-54.125,0.));
#3425=CARTESIAN_POINT('',(22.075,-54.625,0.));
#3426=CARTESIAN_POINT('Origin',(35.075,-54.625,0.));
#3427=CARTESIAN_POINT('',(35.075,-54.625,0.));
#3428=CARTESIAN_POINT('Origin',(35.075,-54.125,0.));
#3429=CARTESIAN_POINT('',(35.575,-54.125,0.));
#3430=CARTESIAN_POINT('Origin',(35.575,-41.125,0.));
#3431=CARTESIAN_POINT('',(35.575,-41.125,0.));
#3432=CARTESIAN_POINT('Origin',(35.075,-41.125,0.));
#3433=CARTESIAN_POINT('',(35.075,-40.625,0.));
#3434=CARTESIAN_POINT('Origin',(22.075,-40.625,0.));
#3435=CARTESIAN_POINT('',(22.075,-40.625,0.));
#3436=CARTESIAN_POINT('Origin',(22.075,-41.125,0.));
#3437=CARTESIAN_POINT('Origin',(-6.472,-63.464,0.));
#3438=CARTESIAN_POINT('',(-8.172,-63.464,0.));
#3439=CARTESIAN_POINT('Origin',(40.625,-54.125,0.));
#3440=CARTESIAN_POINT('',(40.625,-41.125,0.));
#3441=CARTESIAN_POINT('',(40.625,-54.125,0.));
#3442=CARTESIAN_POINT('Origin',(41.125,-54.125,0.));
#3443=CARTESIAN_POINT('',(41.125,-54.625,0.));
#3444=CARTESIAN_POINT('Origin',(54.125,-54.625,0.));
#3445=CARTESIAN_POINT('',(54.125,-54.625,0.));
#3446=CARTESIAN_POINT('Origin',(54.125,-54.125,0.));
#3447=CARTESIAN_POINT('',(54.625,-54.125,0.));
#3448=CARTESIAN_POINT('Origin',(54.625,-41.125,0.));
#3449=CARTESIAN_POINT('',(54.625,-41.125,0.));
#3450=CARTESIAN_POINT('Origin',(54.125,-41.125,0.));
#3451=CARTESIAN_POINT('',(54.125,-40.625,0.));
#3452=CARTESIAN_POINT('Origin',(41.125,-40.625,0.));
#3453=CARTESIAN_POINT('',(41.125,-40.625,0.));
#3454=CARTESIAN_POINT('Origin',(41.125,-41.125,0.));
#3455=CARTESIAN_POINT('Origin',(65.441,41.786,0.));
#3456=CARTESIAN_POINT('',(63.741,41.786,0.));
#3457=CARTESIAN_POINT('Origin',(21.575,-35.075,0.));
#3458=CARTESIAN_POINT('',(21.575,-22.075,0.));
#3459=CARTESIAN_POINT('',(21.575,-35.075,0.));
#3460=CARTESIAN_POINT('Origin',(22.075,-35.075,0.));
#3461=CARTESIAN_POINT('',(22.075,-35.575,0.));
#3462=CARTESIAN_POINT('Origin',(35.075,-35.575,0.));
#3463=CARTESIAN_POINT('',(35.075,-35.575,0.));
#3464=CARTESIAN_POINT('Origin',(35.075,-35.075,0.));
#3465=CARTESIAN_POINT('',(35.575,-35.075,0.));
#3466=CARTESIAN_POINT('Origin',(35.575,-22.075,0.));
#3467=CARTESIAN_POINT('',(35.575,-22.075,0.));
#3468=CARTESIAN_POINT('Origin',(35.075,-22.075,0.));
#3469=CARTESIAN_POINT('',(35.075,-21.575,0.));
#3470=CARTESIAN_POINT('Origin',(22.075,-21.575,0.));
#3471=CARTESIAN_POINT('',(22.075,-21.575,0.));
#3472=CARTESIAN_POINT('Origin',(22.075,-22.075,0.));
#3473=CARTESIAN_POINT('Origin',(21.575,-16.025,0.));
#3474=CARTESIAN_POINT('',(21.575,-3.025,0.));
#3475=CARTESIAN_POINT('',(21.575,-16.025,0.));
#3476=CARTESIAN_POINT('Origin',(22.075,-16.025,0.));
#3477=CARTESIAN_POINT('',(22.075,-16.525,0.));
#3478=CARTESIAN_POINT('Origin',(35.075,-16.525,0.));
#3479=CARTESIAN_POINT('',(35.075,-16.525,0.));
#3480=CARTESIAN_POINT('Origin',(35.075,-16.025,0.));
#3481=CARTESIAN_POINT('',(35.575,-16.025,0.));
#3482=CARTESIAN_POINT('Origin',(35.575,-3.025,0.));
#3483=CARTESIAN_POINT('',(35.575,-3.025,0.));
#3484=CARTESIAN_POINT('Origin',(35.075,-3.025,0.));
#3485=CARTESIAN_POINT('',(35.075,-2.525,0.));
#3486=CARTESIAN_POINT('Origin',(22.075,-2.525,0.));
#3487=CARTESIAN_POINT('',(22.075,-2.525,0.));
#3488=CARTESIAN_POINT('Origin',(22.075,-3.025,0.));
#3489=CARTESIAN_POINT('Origin',(-6.472,41.786,0.));
#3490=CARTESIAN_POINT('',(-8.172,41.786,0.));
#3491=CARTESIAN_POINT('Origin',(65.441,-63.464,0.));
#3492=CARTESIAN_POINT('',(63.741,-63.464,0.));
#3493=CARTESIAN_POINT('Origin',(2.525,-16.025,0.));
#3494=CARTESIAN_POINT('',(2.525,-3.025,0.));
#3495=CARTESIAN_POINT('',(2.525,-16.025,0.));
#3496=CARTESIAN_POINT('Origin',(3.025,-16.025,0.));
#3497=CARTESIAN_POINT('',(3.025,-16.525,0.));
#3498=CARTESIAN_POINT('Origin',(16.025,-16.525,0.));
#3499=CARTESIAN_POINT('',(16.025,-16.525,0.));
#3500=CARTESIAN_POINT('Origin',(16.025,-16.025,0.));
#3501=CARTESIAN_POINT('',(16.525,-16.025,0.));
#3502=CARTESIAN_POINT('Origin',(16.525,-3.025,0.));
#3503=CARTESIAN_POINT('',(16.525,-3.025,0.));
#3504=CARTESIAN_POINT('Origin',(16.025,-3.025,0.));
#3505=CARTESIAN_POINT('',(16.025,-2.525,0.));
#3506=CARTESIAN_POINT('Origin',(3.025,-2.525,0.));
#3507=CARTESIAN_POINT('',(3.025,-2.525,0.));
#3508=CARTESIAN_POINT('Origin',(3.025,-3.025,0.));
#3509=CARTESIAN_POINT('Origin',(2.525,-54.125,0.));
#3510=CARTESIAN_POINT('',(2.525,-41.125,0.));
#3511=CARTESIAN_POINT('',(2.525,-54.125,0.));
#3512=CARTESIAN_POINT('Origin',(3.025,-54.125,0.));
#3513=CARTESIAN_POINT('',(3.025,-54.625,0.));
#3514=CARTESIAN_POINT('Origin',(16.025,-54.625,0.));
#3515=CARTESIAN_POINT('',(16.025,-54.625,0.));
#3516=CARTESIAN_POINT('Origin',(16.025,-54.125,0.));
#3517=CARTESIAN_POINT('',(16.525,-54.125,0.));
#3518=CARTESIAN_POINT('Origin',(16.525,-41.125,0.));
#3519=CARTESIAN_POINT('',(16.525,-41.125,0.));
#3520=CARTESIAN_POINT('Origin',(16.025,-41.125,0.));
#3521=CARTESIAN_POINT('',(16.025,-40.625,0.));
#3522=CARTESIAN_POINT('Origin',(3.025,-40.625,0.));
#3523=CARTESIAN_POINT('',(3.025,-40.625,0.));
#3524=CARTESIAN_POINT('Origin',(3.025,-41.125,0.));
#3525=CARTESIAN_POINT('Origin',(-11.472,-68.464,0.));
#3526=CARTESIAN_POINT('',(0.,0.,0.));
#3527=CARTESIAN_POINT('Origin',(0.,0.,0.));
#3528=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#3534,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#3529=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#3534,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#3530=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#3534,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#3531=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3528))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3534,#3536,#3537))
REPRESENTATION_CONTEXT('','3D')
);
#3532=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3529))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3534,#3536,#3537))
REPRESENTATION_CONTEXT('','3D')
);
#3533=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3530))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3534,#3536,#3537))
REPRESENTATION_CONTEXT('','3D')
);
#3534=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#3535=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#3536=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#3537=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#3538=SHAPE_DEFINITION_REPRESENTATION(#3540,#3543);
#3539=SHAPE_DEFINITION_REPRESENTATION(#3541,#3544);
#3540=PRODUCT_DEFINITION_SHAPE('',$,#3546);
#3541=PRODUCT_DEFINITION_SHAPE('',$,#3547);
#3542=PRODUCT_DEFINITION_SHAPE($,$,#17);
#3543=SHAPE_REPRESENTATION('',(#2096,#2304),#3531);
#3544=SHAPE_REPRESENTATION('',(#2097),#3532);
#3545=PRODUCT_DEFINITION_CONTEXT('part definition',#3553,'design');
#3546=PRODUCT_DEFINITION('2025-05-31-11-33-27-300','Top Plate',#3548,#3545);
#3547=PRODUCT_DEFINITION('2025-05-31-11-45-31-633','Top Plate (1)',#3549,
#3545);
#3548=PRODUCT_DEFINITION_FORMATION('',$,#3555);
#3549=PRODUCT_DEFINITION_FORMATION('',$,#3556);
#3550=PRODUCT_RELATED_PRODUCT_CATEGORY('Top Plate','Top Plate',(#3555));
#3551=PRODUCT_RELATED_PRODUCT_CATEGORY('Top Plate (1)','Top Plate (1)',
(#3556));
#3552=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#3553);
#3553=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#3554=PRODUCT_CONTEXT('part definition',#3553,'mechanical');
#3555=PRODUCT('2025-05-31-11-33-27-300','Top Plate',$,(#3554));
#3556=PRODUCT('2025-05-31-11-45-31-633','Top Plate (1)',$,(#3554));
#3557=PRESENTATION_STYLE_ASSIGNMENT((#3560));
#3558=PRESENTATION_STYLE_ASSIGNMENT((#3561));
#3559=PRESENTATION_STYLE_ASSIGNMENT((NULL_STYLE(.NULL.)));
#3560=SURFACE_STYLE_USAGE(.BOTH.,#3562);
#3561=SURFACE_STYLE_USAGE(.BOTH.,#3563);
#3562=SURFACE_SIDE_STYLE('',(#3564));
#3563=SURFACE_SIDE_STYLE('',(#3565));
#3564=SURFACE_STYLE_FILL_AREA(#3566);
#3565=SURFACE_STYLE_FILL_AREA(#3567);
#3566=FILL_AREA_STYLE('Steel - Satin',(#3568));
#3567=FILL_AREA_STYLE('ABS (White)',(#3569));
#3568=FILL_AREA_STYLE_COLOUR('Steel - Satin',#3570);
#3569=FILL_AREA_STYLE_COLOUR('ABS (White)',#3571);
#3570=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
#3571=COLOUR_RGB('ABS (White)',0.964705882352941,0.964705882352941,0.952941176470588);
ENDSEC;
END-ISO-10303-21;
