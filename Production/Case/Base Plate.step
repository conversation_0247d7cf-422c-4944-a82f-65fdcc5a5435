ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'Top Plate.step',
/* time_stamp */ '2025-06-01T14:20:32-04:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v20.1',
/* originating_system */ 'Autodesk Translation Framework v14.10.0.0',
/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#20,#21),
#7226);
#11=CONTEXT_DEPENDENT_OVER_RIDING_STYLED_ITEM('',(#7252),#7237,#21,(#15));
#12=MAPPED_ITEM('',#13,#3713);
#13=REPRESENTATION_MAP(#3713,#7236);
#14=ITEM_DEFINED_TRANSFORMATION($,$,#3596,#3712);
#15=(
REPRESENTATION_RELATIONSHIP($,$,#7237,#7236)
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#14)
SHAPE_REPRESENTATION_RELATIONSHIP()
);
#16=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#15,#7235);
#17=NEXT_ASSEMBLY_USAGE_OCCURRENCE('Base Plate:1','Base Plate:1',
'Base Plate:1',#7239,#7240,'Base Plate:1');
#18=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#7237,#19);
#19=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#22),#7225);
#20=STYLED_ITEM('',(#7250),#22);
#21=STYLED_ITEM('',(#7253),#12);
#22=MANIFOLD_SOLID_BREP('Body1',#3582);
#23=CYLINDRICAL_SURFACE('',#3667,2.);
#24=CYLINDRICAL_SURFACE('',#3672,2.);
#25=CYLINDRICAL_SURFACE('',#3674,2.);
#26=CYLINDRICAL_SURFACE('',#3675,5.);
#27=CYLINDRICAL_SURFACE('',#3678,5.);
#28=CYLINDRICAL_SURFACE('',#3680,5.);
#29=CYLINDRICAL_SURFACE('',#3683,5.);
#30=CYLINDRICAL_SURFACE('',#3685,3.00000000000001);
#31=CYLINDRICAL_SURFACE('',#3688,3.);
#32=CYLINDRICAL_SURFACE('',#3691,3.);
#33=CYLINDRICAL_SURFACE('',#3694,3.);
#34=CYLINDRICAL_SURFACE('',#3700,1.7);
#35=CYLINDRICAL_SURFACE('',#3703,1.7);
#36=CYLINDRICAL_SURFACE('',#3706,1.7);
#37=CYLINDRICAL_SURFACE('',#3709,1.7);
#38=TOROIDAL_SURFACE('',#3663,4.,2.);
#39=TOROIDAL_SURFACE('',#3669,4.,2.);
#40=CIRCLE('',#3646,2.);
#41=CIRCLE('',#3647,2.);
#42=CIRCLE('',#3648,5.);
#43=CIRCLE('',#3649,5.);
#44=CIRCLE('',#3650,5.);
#45=CIRCLE('',#3651,5.);
#46=CIRCLE('',#3652,1.7);
#47=CIRCLE('',#3653,1.7);
#48=CIRCLE('',#3654,1.7);
#49=CIRCLE('',#3655,1.7);
#50=CIRCLE('',#3657,2.);
#51=CIRCLE('',#3662,2.);
#52=CIRCLE('',#3664,2.);
#53=CIRCLE('',#3665,4.);
#54=CIRCLE('',#3666,2.);
#55=CIRCLE('',#3668,2.);
#56=CIRCLE('',#3670,4.);
#57=CIRCLE('',#3671,2.);
#58=CIRCLE('',#3676,5.);
#59=CIRCLE('',#3679,5.);
#60=CIRCLE('',#3681,5.);
#61=CIRCLE('',#3684,5.);
#62=CIRCLE('',#3686,3.00000000000001);
#63=CIRCLE('',#3687,3.00000000000001);
#64=CIRCLE('',#3689,3.);
#65=CIRCLE('',#3690,3.);
#66=CIRCLE('',#3692,3.);
#67=CIRCLE('',#3693,3.);
#68=CIRCLE('',#3695,3.);
#69=CIRCLE('',#3696,3.);
#70=CIRCLE('',#3701,1.7);
#71=CIRCLE('',#3704,1.7);
#72=CIRCLE('',#3707,1.7);
#73=CIRCLE('',#3710,1.7);
#74=FACE_BOUND('',#667,.T.);
#75=FACE_BOUND('',#669,.T.);
#76=FACE_BOUND('',#670,.T.);
#77=FACE_BOUND('',#671,.T.);
#78=FACE_BOUND('',#672,.T.);
#79=FACE_BOUND('',#673,.T.);
#80=FACE_BOUND('',#674,.T.);
#81=FACE_BOUND('',#695,.T.);
#82=FACE_BOUND('',#735,.T.);
#83=FACE_BOUND('',#760,.T.);
#84=FACE_BOUND('',#800,.T.);
#85=FACE_BOUND('',#803,.T.);
#86=FACE_BOUND('',#804,.T.);
#87=FACE_BOUND('',#805,.T.);
#88=FACE_BOUND('',#806,.T.);
#89=FACE_BOUND('',#831,.T.);
#90=FACE_BOUND('',#832,.T.);
#91=FACE_BOUND('',#833,.T.);
#92=FACE_BOUND('',#834,.T.);
#93=FACE_BOUND('',#837,.T.);
#94=FACE_BOUND('',#840,.T.);
#95=FACE_BOUND('',#843,.T.);
#96=FACE_BOUND('',#846,.T.);
#97=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4249,#4250,#4251,#4252,#4253),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0332232278204757,0.0630580503980056),
 .UNSPECIFIED.);
#98=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4255,#4256,#4257,#4258,#4259),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0630580503980056,-0.0332232278204757,
0.),.UNSPECIFIED.);
#99=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4325,#4326,#4327,#4328,#4329),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0489641781566588,0.0870048187550354),
 .UNSPECIFIED.);
#100=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4333,#4334,#4335,#4336,#4337),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0870048187550354,-0.0489641781566588,
0.),.UNSPECIFIED.);
#101=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4350,#4351,#4352,#4353,#4354),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0320763741077342,0.0626257349906224),
 .UNSPECIFIED.);
#102=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4356,#4357,#4358,#4359,#4360),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0626257349906224,-0.0320763741077342,
0.),.UNSPECIFIED.);
#103=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4373,#4374,#4375,#4376,#4377),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0311267092576747,0.0640130497206912),
 .UNSPECIFIED.);
#104=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4379,#4380,#4381,#4382,#4383),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0640130497206912,-0.0311267092576747,
0.),.UNSPECIFIED.);
#105=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4396,#4397,#4398,#4399,#4400),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0395354965168579,0.090420498095387),
 .UNSPECIFIED.);
#106=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4402,#4403,#4404,#4405,#4406),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.090420498095387,-0.0395354965168579,
0.),.UNSPECIFIED.);
#107=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4419,#4420,#4421,#4422,#4423),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0487688684125448,0.0873281375942793),
 .UNSPECIFIED.);
#108=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4425,#4426,#4427,#4428,#4429),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0873281375942793,-0.0487688684125448,
0.),.UNSPECIFIED.);
#109=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4442,#4443,#4444,#4445,#4446),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0323586061774373,0.0625548072252209),
 .UNSPECIFIED.);
#110=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4448,#4449,#4450,#4451,#4452),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0625548072252209,-0.0323586061774373,
0.),.UNSPECIFIED.);
#111=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4465,#4466,#4467,#4468,#4469),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0305072370070659,0.0646749887620658),
 .UNSPECIFIED.);
#112=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4471,#4472,#4473,#4474,#4475),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0646749887620658,-0.0305072370070659,
0.),.UNSPECIFIED.);
#113=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4487,#4488,#4489,#4490,#4491),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0401455396885352,0.0897596569214949),
 .UNSPECIFIED.);
#114=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4492,#4493,#4494,#4495,#4496),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0897596569214949,-0.0401455396885352,
0.),.UNSPECIFIED.);
#115=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4529,#4530,#4531,#4532,#4533),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0256227914361976,0.0489681431843219),
 .UNSPECIFIED.);
#116=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4535,#4536,#4537,#4538,#4539),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0489681431843219,-0.0256227914361976,
0.),.UNSPECIFIED.);
#117=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4552,#4553,#4554,#4555,#4556),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.023576185871271,0.0499866082252915),
 .UNSPECIFIED.);
#118=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4558,#4559,#4560,#4561,#4562),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0499866082252915,-0.023576185871271,
0.),.UNSPECIFIED.);
#119=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4575,#4576,#4577,#4578,#4579),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0352498816958074,0.0689999599739871),
 .UNSPECIFIED.);
#120=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4581,#4582,#4583,#4584,#4585),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0689999599739871,-0.0352498816958074,
0.),.UNSPECIFIED.);
#121=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4598,#4599,#4600,#4601,#4602),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0339333406109823,0.0697229709721148),
 .UNSPECIFIED.);
#122=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4604,#4605,#4606,#4607,#4608),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0697229709721148,-0.0339333406109823,
0.),.UNSPECIFIED.);
#123=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4621,#4622,#4623,#4624,#4625),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.037940707919432,0.0778727689218518),
 .UNSPECIFIED.);
#124=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4627,#4628,#4629,#4630,#4631),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0778727689218518,-0.037940707919432,
0.),.UNSPECIFIED.);
#125=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4644,#4645,#4646,#4647,#4648),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0424829995581773,0.0822847351075214),
 .UNSPECIFIED.);
#126=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4650,#4651,#4652,#4653,#4654),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0822847351075214,-0.0424829995581773,
0.),.UNSPECIFIED.);
#127=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4667,#4668,#4669,#4670,#4671),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0369482130230788,0.0724474583793122),
 .UNSPECIFIED.);
#128=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4673,#4674,#4675,#4676,#4677),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0724474583793122,-0.0369482130230788,
0.),.UNSPECIFIED.);
#129=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4690,#4691,#4692,#4693,#4694),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0341599063551578,0.0672386117563061),
 .UNSPECIFIED.);
#130=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4696,#4697,#4698,#4699,#4700),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0672386117563061,-0.0341599063551578,
0.),.UNSPECIFIED.);
#131=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4713,#4714,#4715,#4716,#4717),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0234723442129683,0.0457469430879444),
 .UNSPECIFIED.);
#132=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4719,#4720,#4721,#4722,#4723),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0457469430879444,-0.0234723442129683,
0.),.UNSPECIFIED.);
#133=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4736,#4737,#4738,#4739,#4740),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0212080417625968,0.0415149509992502),
 .UNSPECIFIED.);
#134=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4742,#4743,#4744,#4745,#4746),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0415149509992502,-0.0212080417625968,
0.),.UNSPECIFIED.);
#135=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4773,#4774,#4775,#4776,#4777),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0351425252127339,0.0610179249057739),
 .UNSPECIFIED.);
#136=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4779,#4780,#4781,#4782,#4783),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0280116838365093,0.0531830593723236),
 .UNSPECIFIED.);
#137=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4785,#4786,#4787,#4788,#4789),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0243329228413605,0.0504285700811963),
 .UNSPECIFIED.);
#138=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4791,#4792,#4793,#4794,#4795),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0276366208085698,0.0563143346515221),
 .UNSPECIFIED.);
#139=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4797,#4798,#4799,#4800,#4801),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0478289062178764,0.0941120824854342),
 .UNSPECIFIED.);
#140=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4803,#4804,#4805,#4806,#4807),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0526606385410694,0.124958075778914),
 .UNSPECIFIED.);
#141=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4817,#4818,#4819,#4820,#4821),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0469469312268579,0.0951654628153913),
 .UNSPECIFIED.);
#142=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4823,#4824,#4825,#4826,#4827),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.033628714622706,0.0673787929008857),
 .UNSPECIFIED.);
#143=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4829,#4830,#4831,#4832,#4833),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0344020868273981,0.0703751792317449),
 .UNSPECIFIED.);
#144=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4835,#4836,#4837,#4838,#4839),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0387648554762092,0.0809018808622737),
 .UNSPECIFIED.);
#145=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4841,#4842,#4843,#4844,#4845),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0414947744405871,0.0802860291013455),
 .UNSPECIFIED.);
#146=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4847,#4848,#4849,#4850,#4851),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0361532003492885,0.0703420182699884),
 .UNSPECIFIED.);
#147=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4853,#4854,#4855,#4856,#4857),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.033725650843392,0.0680878258389567),
 .UNSPECIFIED.);
#148=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4859,#4860,#4861,#4862,#4863),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0249186414278097,0.0483674301054939),
 .UNSPECIFIED.);
#149=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4865,#4866,#4867,#4868,#4869),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0224386745972272,0.0442114320715124),
 .UNSPECIFIED.);
#150=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4879,#4880,#4881,#4882,#4883),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0333161184869677,0.0622355569718287),
 .UNSPECIFIED.);
#151=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4885,#4886,#4887,#4888,#4889),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0270237311350868,0.0550926951907323),
 .UNSPECIFIED.);
#152=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4891,#4892,#4893,#4894,#4895),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0426199560996444,0.0783177667280387),
 .UNSPECIFIED.);
#153=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4897,#4898,#4899,#4900,#4901),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0328843084613603,0.0665831787891137),
 .UNSPECIFIED.);
#154=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4903,#4904,#4905,#4906,#4907),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0204671135702732,0.0402282493870738),
 .UNSPECIFIED.);
#155=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4909,#4910,#4911,#4912,#4913),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0195309127474034,0.0383325562867562),
 .UNSPECIFIED.);
#156=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4915,#4916,#4917,#4918,#4919),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0192070900219338,0.0390799903304408),
 .UNSPECIFIED.);
#157=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4921,#4922,#4923,#4924,#4925),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0185284397878466,0.0442948897470726),
 .UNSPECIFIED.);
#158=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4927,#4928,#4929,#4930,#4931),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0522193611043987,0.0893114045672862),
 .UNSPECIFIED.);
#159=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4933,#4934,#4935,#4936,#4937),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0082562907233126,0.0128270451155165),
 .UNSPECIFIED.);
#160=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4939,#4940,#4941,#4942,#4943),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0258152924767853,0.0455185033807595),
 .UNSPECIFIED.);
#161=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4945,#4946,#4947,#4948,#4949),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0265187624123591,0.0593212346823061),
 .UNSPECIFIED.);
#162=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4951,#4952,#4953,#4954,#4955),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0303059101301422,0.0533700416403343),
 .UNSPECIFIED.);
#163=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4957,#4958,#4959,#4960,#4961),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0219668478893245,0.0499505815972015),
 .UNSPECIFIED.);
#164=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4965,#4966,#4967,#4968,#4969),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0291479332331411,0.0544989854539011),
 .UNSPECIFIED.);
#165=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4971,#4972,#4973,#4974,#4975),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0249112315986868,0.0528243665518279),
 .UNSPECIFIED.);
#166=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4977,#4978,#4979,#4980,#4981),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0315993190508233,0.0668244263253945),
 .UNSPECIFIED.);
#167=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4983,#4984,#4985,#4986,#4987),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0336997078729892,0.0611630752258989),
 .UNSPECIFIED.);
#168=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4989,#4990,#4991,#4992,#4993),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0224221928309154,0.0409863474474436),
 .UNSPECIFIED.);
#169=B_SPLINE_CURVE_WITH_KNOTS('',3,(#4995,#4996,#4997,#4998,#4999),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0175268455900678,0.0368978496094305),
 .UNSPECIFIED.);
#170=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5001,#5002,#5003,#5004,#5005),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0176950326161023,0.0453962406627712),
 .UNSPECIFIED.);
#171=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5009,#5010,#5011,#5012,#5013),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0559395514169812,0.0868538118276596),
 .UNSPECIFIED.);
#172=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5015,#5016,#5017,#5018,#5019),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0183009921077505,0.0369814993166081),
 .UNSPECIFIED.);
#173=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5022,#5023,#5024,#5025,#5026),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0177239024305477,0.037217178999239),
 .UNSPECIFIED.);
#174=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5029,#5030,#5031,#5032,#5033),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0333160705264821,0.0622355090113432),
 .UNSPECIFIED.);
#175=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5035,#5036,#5037,#5038,#5039),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0270237886081428,0.0550927229694672),
 .UNSPECIFIED.);
#176=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5041,#5042,#5043,#5044,#5045),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0426200446754038,0.0783178032537605),
 .UNSPECIFIED.);
#177=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5047,#5048,#5049,#5050,#5051),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0328842735950209,0.0665831495933769),
 .UNSPECIFIED.);
#178=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5053,#5054,#5055,#5056,#5057),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0204671205231686,0.0402282131334243),
 .UNSPECIFIED.);
#179=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5059,#5060,#5061,#5062,#5063),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0195309299216581,0.0383325958426253),
 .UNSPECIFIED.);
#180=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5065,#5066,#5067,#5068,#5069),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0192071160588506,0.03907993228078),
 .UNSPECIFIED.);
#181=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5071,#5072,#5073,#5074,#5075),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0185285273428258,0.04429491828703),
 .UNSPECIFIED.);
#182=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5077,#5078,#5079,#5080,#5081),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0522194200444501,0.0893114635073375),
 .UNSPECIFIED.);
#183=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5083,#5084,#5085,#5086,#5087),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.00825628776884585,0.0128270421610497),
 .UNSPECIFIED.);
#184=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5089,#5090,#5091,#5092,#5093),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0258152924767853,0.0455185033807595),
 .UNSPECIFIED.);
#185=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5095,#5096,#5097,#5098,#5099),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0265187098242649,0.0593211525326363),
 .UNSPECIFIED.);
#186=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5101,#5102,#5103,#5104,#5105),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0303059101301422,0.0533700962879618),
 .UNSPECIFIED.);
#187=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5107,#5108,#5109,#5110,#5111),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0219668478893245,0.0499505815972015),
 .UNSPECIFIED.);
#188=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5115,#5116,#5117,#5118,#5119),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0291479420906738,0.054499009467346),
 .UNSPECIFIED.);
#189=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5121,#5122,#5123,#5124,#5125),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0249111657428554,0.0528243808813189),
 .UNSPECIFIED.);
#190=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5127,#5128,#5129,#5130,#5131),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.031599261279197,0.0668244279968161),
 .UNSPECIFIED.);
#191=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5133,#5134,#5135,#5136,#5137),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0336997078729892,0.0611630173404577),
 .UNSPECIFIED.);
#192=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5139,#5140,#5141,#5142,#5143),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0224221928309154,0.0409863474474436),
 .UNSPECIFIED.);
#193=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5145,#5146,#5147,#5148,#5149),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0175268771387675,0.0368978442172899),
 .UNSPECIFIED.);
#194=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5151,#5152,#5153,#5154,#5155),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.017695038819937,0.0453962468666059),
 .UNSPECIFIED.);
#195=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5159,#5160,#5161,#5162,#5163),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0559395522165387,0.0868538082872632),
 .UNSPECIFIED.);
#196=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5165,#5166,#5167,#5168,#5169),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0183010076591476,0.0369814900558673),
 .UNSPECIFIED.);
#197=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5172,#5173,#5174,#5175,#5176),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0177238813261091,0.0372171578948003),
 .UNSPECIFIED.);
#198=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5197,#5198,#5199,#5200,#5201),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0441199368341506,0.0764563928333372),
 .UNSPECIFIED.);
#199=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5203,#5204,#5205,#5206,#5207),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0764563928333372,-0.0441199368341506,
0.),.UNSPECIFIED.);
#200=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5220,#5221,#5222,#5223,#5224),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0281031935010044,0.0593355585944129),
 .UNSPECIFIED.);
#201=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5226,#5227,#5228,#5229,#5230),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0593355585944129,-0.0281031935010044,
0.),.UNSPECIFIED.);
#202=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5243,#5244,#5245,#5246,#5247),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0234804674011256,0.0452926252606328),
 .UNSPECIFIED.);
#203=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5249,#5250,#5251,#5252,#5253),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0452926252606328,-0.0234804674011256,
0.),.UNSPECIFIED.);
#204=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5266,#5267,#5268,#5269,#5270),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0208865994199229,0.0415447549985437),
 .UNSPECIFIED.);
#205=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5272,#5273,#5274,#5275,#5276),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0415447549985437,-0.0208865994199229,
0.),.UNSPECIFIED.);
#206=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5289,#5290,#5291,#5292,#5293),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0192163231380338,0.0495840114104723),
 .UNSPECIFIED.);
#207=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5295,#5296,#5297,#5298,#5299),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0495840114104723,-0.0192163231380338,
0.),.UNSPECIFIED.);
#208=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5331,#5332,#5333,#5334,#5335),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0610179249057739,-0.0351425252127339,
0.),.UNSPECIFIED.);
#209=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5348,#5349,#5350,#5351,#5352),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0531830593723236,-0.0280116838365093,
0.),.UNSPECIFIED.);
#210=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5365,#5366,#5367,#5368,#5369),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0504285700811963,-0.0243329228413605,
0.),.UNSPECIFIED.);
#211=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5382,#5383,#5384,#5385,#5386),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0563143346515221,-0.0276366208085698,
0.),.UNSPECIFIED.);
#212=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5399,#5400,#5401,#5402,#5403),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0941120824854342,-0.0478289062178764,
0.),.UNSPECIFIED.);
#213=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5416,#5417,#5418,#5419,#5420),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.124958075778914,-0.0526606385410694,
0.),.UNSPECIFIED.);
#214=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5446,#5447,#5448,#5449,#5450),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0349157223225691,0.0824679802230752),
 .UNSPECIFIED.);
#215=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5454,#5455,#5456,#5457,#5458),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0824679802230752,-0.0349157223225691,
0.),.UNSPECIFIED.);
#216=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5471,#5472,#5473,#5474,#5475),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0262654985723071,0.044023984659863),
 .UNSPECIFIED.);
#217=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5477,#5478,#5479,#5480,#5481),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.044023984659863,-0.0262654985723071,
0.),.UNSPECIFIED.);
#218=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5494,#5495,#5496,#5497,#5498),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0130088901546045,0.0254770566087581),
 .UNSPECIFIED.);
#219=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5500,#5501,#5502,#5503,#5504),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0254770566087581,-0.0130088901546045,
0.),.UNSPECIFIED.);
#220=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5517,#5518,#5519,#5520,#5521),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0121154313089734,0.024444039256053),
 .UNSPECIFIED.);
#221=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5523,#5524,#5525,#5526,#5527),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.024444039256053,-0.0121154313089734,
0.),.UNSPECIFIED.);
#222=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5540,#5541,#5542,#5543,#5544),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0183810245959774,0.0367984783618504),
 .UNSPECIFIED.);
#223=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5546,#5547,#5548,#5549,#5550),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0367984783618504,-0.0183810245959774,
0.),.UNSPECIFIED.);
#224=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5563,#5564,#5565,#5566,#5567),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0208731468986089,0.0463233929083288),
 .UNSPECIFIED.);
#225=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5569,#5570,#5571,#5572,#5573),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0463233929083288,-0.0208731468986089,
0.),.UNSPECIFIED.);
#226=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5586,#5587,#5588,#5589,#5590),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0269828327999049,0.0523809064308374),
 .UNSPECIFIED.);
#227=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5592,#5593,#5594,#5595,#5596),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0523809064308374,-0.0269828327999049,
0.),.UNSPECIFIED.);
#228=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5609,#5610,#5611,#5612,#5613),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0242179392985011,0.0476611138758454),
 .UNSPECIFIED.);
#229=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5615,#5616,#5617,#5618,#5619),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0476611138758454,-0.0242179392985011,
0.),.UNSPECIFIED.);
#230=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5632,#5633,#5634,#5635,#5636),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0214380485767212,0.0500195490776112),
 .UNSPECIFIED.);
#231=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5638,#5639,#5640,#5641,#5642),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0500195490776112,-0.0214380485767212,
0.),.UNSPECIFIED.);
#232=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5660,#5661,#5662,#5663,#5664),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0622355090113432,-0.0333160705264821,
0.),.UNSPECIFIED.);
#233=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5677,#5678,#5679,#5680,#5681),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0550927229694672,-0.0270237886081428,
0.),.UNSPECIFIED.);
#234=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5694,#5695,#5696,#5697,#5698),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0783178032537605,-0.0426200446754038,
0.),.UNSPECIFIED.);
#235=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5711,#5712,#5713,#5714,#5715),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0665831495933769,-0.0328842735950209,
0.),.UNSPECIFIED.);
#236=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5728,#5729,#5730,#5731,#5732),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0402282131334243,-0.0204671205231686,
0.),.UNSPECIFIED.);
#237=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5745,#5746,#5747,#5748,#5749),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0383325958426253,-0.0195309299216581,
0.),.UNSPECIFIED.);
#238=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5762,#5763,#5764,#5765,#5766),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.03907993228078,-0.0192071160588506,0.),
 .UNSPECIFIED.);
#239=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5779,#5780,#5781,#5782,#5783),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.04429491828703,-0.0185285273428258,0.),
 .UNSPECIFIED.);
#240=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5796,#5797,#5798,#5799,#5800),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0893114635073375,-0.0522194200444501,
0.),.UNSPECIFIED.);
#241=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5813,#5814,#5815,#5816,#5817),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0128270421610497,-0.00825628776884585,
0.),.UNSPECIFIED.);
#242=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5830,#5831,#5832,#5833,#5834),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0455185033807595,-0.0258152924767853,
0.),.UNSPECIFIED.);
#243=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5847,#5848,#5849,#5850,#5851),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0593211525326363,-0.0265187098242649,
0.),.UNSPECIFIED.);
#244=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5864,#5865,#5866,#5867,#5868),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0533700962879618,-0.0303059101301422,
0.),.UNSPECIFIED.);
#245=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5881,#5882,#5883,#5884,#5885),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0499505815972015,-0.0219668478893245,
0.),.UNSPECIFIED.);
#246=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5902,#5903,#5904,#5905,#5906),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.054499009467346,-0.0291479420906738,
0.),.UNSPECIFIED.);
#247=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5919,#5920,#5921,#5922,#5923),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0528243808813189,-0.0249111657428554,
0.),.UNSPECIFIED.);
#248=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5936,#5937,#5938,#5939,#5940),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0668244279968161,-0.031599261279197,
0.),.UNSPECIFIED.);
#249=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5953,#5954,#5955,#5956,#5957),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0611630173404577,-0.0336997078729892,
0.),.UNSPECIFIED.);
#250=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5970,#5971,#5972,#5973,#5974),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0409863474474436,-0.0224221928309154,
0.),.UNSPECIFIED.);
#251=B_SPLINE_CURVE_WITH_KNOTS('',3,(#5987,#5988,#5989,#5990,#5991),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0368978442172899,-0.0175268771387675,
0.),.UNSPECIFIED.);
#252=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6004,#6005,#6006,#6007,#6008),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0453962468666059,-0.017695038819937,
0.),.UNSPECIFIED.);
#253=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6025,#6026,#6027,#6028,#6029),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0868538082872632,-0.0559395522165387,
0.),.UNSPECIFIED.);
#254=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6042,#6043,#6044,#6045,#6046),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0369814900558673,-0.0183010076591476,
0.),.UNSPECIFIED.);
#255=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6062,#6063,#6064,#6065,#6066),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0372171578948003,-0.0177238813261091,
0.),.UNSPECIFIED.);
#256=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6081,#6082,#6083,#6084,#6085),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.048988244755236,0.0877924724347864),
 .UNSPECIFIED.);
#257=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6089,#6090,#6091,#6092,#6093),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0877924724347864,-0.048988244755236,
0.),.UNSPECIFIED.);
#258=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6106,#6107,#6108,#6109,#6110),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0328863779234484,0.0634811660635357),
 .UNSPECIFIED.);
#259=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6112,#6113,#6114,#6115,#6116),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0634811660635357,-0.0328863779234484,
0.),.UNSPECIFIED.);
#260=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6129,#6130,#6131,#6132,#6133),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0304532066826528,0.0621144648696724),
 .UNSPECIFIED.);
#261=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6135,#6136,#6137,#6138,#6139),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0621144648696724,-0.0304532066826528,
0.),.UNSPECIFIED.);
#262=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6152,#6153,#6154,#6155,#6156),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0374315329948845,0.0849054207321809),
 .UNSPECIFIED.);
#263=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6158,#6159,#6160,#6161,#6162),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0849054207321809,-0.0374315329948845,
0.),.UNSPECIFIED.);
#264=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6175,#6176,#6177,#6178,#6179),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0518862200258535,0.0915482833461465),
 .UNSPECIFIED.);
#265=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6181,#6182,#6183,#6184,#6185),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0915482833461465,-0.0518862200258535,
0.),.UNSPECIFIED.);
#266=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6198,#6199,#6200,#6201,#6202),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0330153633409254,0.0646894740399855),
 .UNSPECIFIED.);
#267=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6204,#6205,#6206,#6207,#6208),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0646894740399855,-0.0330153633409254,
0.),.UNSPECIFIED.);
#268=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6221,#6222,#6223,#6224,#6225),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0308156757974671,0.062679497807866),
 .UNSPECIFIED.);
#269=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6227,#6228,#6229,#6230,#6231),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.062679497807866,-0.0308156757974671,
0.),.UNSPECIFIED.);
#270=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6243,#6244,#6245,#6246,#6247),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0381398411034351,0.0883011242259416),
 .UNSPECIFIED.);
#271=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6248,#6249,#6250,#6251,#6252),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0883011242259416,-0.0381398411034351,
0.),.UNSPECIFIED.);
#272=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6270,#6271,#6272,#6273,#6274),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0951654628153913,-0.0469469312268579,
0.),.UNSPECIFIED.);
#273=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6287,#6288,#6289,#6290,#6291),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0673787929008857,-0.033628714622706,
0.),.UNSPECIFIED.);
#274=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6304,#6305,#6306,#6307,#6308),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0703751792317449,-0.0344020868273981,
0.),.UNSPECIFIED.);
#275=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6321,#6322,#6323,#6324,#6325),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0809018808622737,-0.0387648554762092,
0.),.UNSPECIFIED.);
#276=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6338,#6339,#6340,#6341,#6342),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0802860291013455,-0.0414947744405871,
0.),.UNSPECIFIED.);
#277=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6355,#6356,#6357,#6358,#6359),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0703420182699884,-0.0361532003492885,
0.),.UNSPECIFIED.);
#278=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6372,#6373,#6374,#6375,#6376),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0680878258389567,-0.033725650843392,
0.),.UNSPECIFIED.);
#279=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6389,#6390,#6391,#6392,#6393),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0483674301054939,-0.0249186414278097,
0.),.UNSPECIFIED.);
#280=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6406,#6407,#6408,#6409,#6410),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0442114320715124,-0.0224386745972272,
0.),.UNSPECIFIED.);
#281=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6440,#6441,#6442,#6443,#6444),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0349156082050476,0.0824679540867423),
 .UNSPECIFIED.);
#282=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6448,#6449,#6450,#6451,#6452),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0824679540867423,-0.0349156082050476,
0.),.UNSPECIFIED.);
#283=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6465,#6466,#6467,#6468,#6469),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0262654692383923,0.0440240123342674),
 .UNSPECIFIED.);
#284=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6471,#6472,#6473,#6474,#6475),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0440240123342674,-0.0262654692383923,
0.),.UNSPECIFIED.);
#285=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6488,#6489,#6490,#6491,#6492),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0130088137380629,0.0254770196494594),
 .UNSPECIFIED.);
#286=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6494,#6495,#6496,#6497,#6498),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0254770196494594,-0.0130088137380629,
0.),.UNSPECIFIED.);
#287=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6511,#6512,#6513,#6514,#6515),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0121154187243702,0.0244440307937324),
 .UNSPECIFIED.);
#288=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6517,#6518,#6519,#6520,#6521),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0244440307937324,-0.0121154187243702,
0.),.UNSPECIFIED.);
#289=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6534,#6535,#6536,#6537,#6538),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0183810365404859,0.0367984903063589),
 .UNSPECIFIED.);
#290=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6540,#6541,#6542,#6543,#6544),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0367984903063589,-0.0183810365404859,
0.),.UNSPECIFIED.);
#291=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6557,#6558,#6559,#6560,#6561),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0208731206024539,0.0463233666121738),
 .UNSPECIFIED.);
#292=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6563,#6564,#6565,#6566,#6567),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0463233666121738,-0.0208731206024539,
0.),.UNSPECIFIED.);
#293=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6580,#6581,#6582,#6583,#6584),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.026982773582202,0.0523809027619369),
 .UNSPECIFIED.);
#294=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6586,#6587,#6588,#6589,#6590),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0523809027619369,-0.026982773582202,
0.),.UNSPECIFIED.);
#295=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6603,#6604,#6605,#6606,#6607),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0242179392985011,0.0476611138758454),
 .UNSPECIFIED.);
#296=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6609,#6610,#6611,#6612,#6613),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0476611138758454,-0.0242179392985011,
0.),.UNSPECIFIED.);
#297=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6626,#6627,#6628,#6629,#6630),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,0.0214380650765451,0.0500195573265714),
 .UNSPECIFIED.);
#298=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6632,#6633,#6634,#6635,#6636),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0500195573265714,-0.0214380650765451,
0.),.UNSPECIFIED.);
#299=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6654,#6655,#6656,#6657,#6658),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0622355569718287,-0.0333161184869677,
0.),.UNSPECIFIED.);
#300=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6671,#6672,#6673,#6674,#6675),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0550926951907323,-0.0270237311350868,
0.),.UNSPECIFIED.);
#301=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6688,#6689,#6690,#6691,#6692),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0783177667280387,-0.0426199560996444,
0.),.UNSPECIFIED.);
#302=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6705,#6706,#6707,#6708,#6709),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0665831787891137,-0.0328843084613603,
0.),.UNSPECIFIED.);
#303=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6722,#6723,#6724,#6725,#6726),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0402282493870738,-0.0204671135702732,
0.),.UNSPECIFIED.);
#304=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6739,#6740,#6741,#6742,#6743),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0383325562867562,-0.0195309127474034,
0.),.UNSPECIFIED.);
#305=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6756,#6757,#6758,#6759,#6760),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0390799903304408,-0.0192070900219338,
0.),.UNSPECIFIED.);
#306=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6773,#6774,#6775,#6776,#6777),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0442948897470726,-0.0185284397878466,
0.),.UNSPECIFIED.);
#307=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6790,#6791,#6792,#6793,#6794),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0893114045672862,-0.0522193611043987,
0.),.UNSPECIFIED.);
#308=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6807,#6808,#6809,#6810,#6811),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0128270451155165,-0.0082562907233126,
0.),.UNSPECIFIED.);
#309=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6824,#6825,#6826,#6827,#6828),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0455185033807595,-0.0258152924767853,
0.),.UNSPECIFIED.);
#310=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6841,#6842,#6843,#6844,#6845),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0593212346823061,-0.0265187624123591,
0.),.UNSPECIFIED.);
#311=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6858,#6859,#6860,#6861,#6862),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0533700416403343,-0.0303059101301422,
0.),.UNSPECIFIED.);
#312=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6875,#6876,#6877,#6878,#6879),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0499505815972015,-0.0219668478893245,
0.),.UNSPECIFIED.);
#313=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6896,#6897,#6898,#6899,#6900),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0544989854539011,-0.0291479332331411,
0.),.UNSPECIFIED.);
#314=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6913,#6914,#6915,#6916,#6917),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0528243665518279,-0.0249112315986868,
0.),.UNSPECIFIED.);
#315=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6930,#6931,#6932,#6933,#6934),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0668244263253945,-0.0315993190508233,
0.),.UNSPECIFIED.);
#316=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6947,#6948,#6949,#6950,#6951),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0611630752258989,-0.0336997078729892,
0.),.UNSPECIFIED.);
#317=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6964,#6965,#6966,#6967,#6968),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0409863474474436,-0.0224221928309154,
0.),.UNSPECIFIED.);
#318=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6981,#6982,#6983,#6984,#6985),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0368978496094305,-0.0175268455900678,
0.),.UNSPECIFIED.);
#319=B_SPLINE_CURVE_WITH_KNOTS('',3,(#6998,#6999,#7000,#7001,#7002),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0453962406627712,-0.0176950326161023,
0.),.UNSPECIFIED.);
#320=B_SPLINE_CURVE_WITH_KNOTS('',3,(#7019,#7020,#7021,#7022,#7023),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0868538118276596,-0.0559395514169812,
0.),.UNSPECIFIED.);
#321=B_SPLINE_CURVE_WITH_KNOTS('',3,(#7036,#7037,#7038,#7039,#7040),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.0369814993166081,-0.0183009921077505,
0.),.UNSPECIFIED.);
#322=B_SPLINE_CURVE_WITH_KNOTS('',3,(#7056,#7057,#7058,#7059,#7060),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-0.037217178999239,-0.0177239024305477,
0.),.UNSPECIFIED.);
#323=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4238,#4239),(#4240,#4241),(#4242,
#4243),(#4244,#4245),(#4246,#4247)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0630580503980056,-0.0332232278204757,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#324=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4313,#4314),(#4315,#4316),(#4317,
#4318),(#4319,#4320),(#4321,#4322)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0870048187550354,-0.0489641781566588,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#325=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4339,#4340),(#4341,#4342),(#4343,
#4344),(#4345,#4346),(#4347,#4348)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0626257349906224,-0.0320763741077342,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#326=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4362,#4363),(#4364,#4365),(#4366,
#4367),(#4368,#4369),(#4370,#4371)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0640130497206912,-0.0311267092576747,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#327=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4385,#4386),(#4387,#4388),(#4389,
#4390),(#4391,#4392),(#4393,#4394)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.090420498095387,-0.0395354965168579,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#328=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4408,#4409),(#4410,#4411),(#4412,
#4413),(#4414,#4415),(#4416,#4417)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0873281375942793,-0.0487688684125448,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#329=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4431,#4432),(#4433,#4434),(#4435,
#4436),(#4437,#4438),(#4439,#4440)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0625548072252209,-0.0323586061774373,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#330=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4454,#4455),(#4456,#4457),(#4458,
#4459),(#4460,#4461),(#4462,#4463)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0646749887620658,-0.0305072370070659,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#331=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4477,#4478),(#4479,#4480),(#4481,
#4482),(#4483,#4484),(#4485,#4486)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0897596569214949,-0.0401455396885352,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#332=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4518,#4519),(#4520,#4521),(#4522,
#4523),(#4524,#4525),(#4526,#4527)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0489681431843219,-0.0256227914361976,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#333=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4541,#4542),(#4543,#4544),(#4545,
#4546),(#4547,#4548),(#4549,#4550)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0499866082252915,-0.023576185871271,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#334=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4564,#4565),(#4566,#4567),(#4568,
#4569),(#4570,#4571),(#4572,#4573)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0689999599739871,-0.0352498816958074,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#335=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4587,#4588),(#4589,#4590),(#4591,
#4592),(#4593,#4594),(#4595,#4596)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0697229709721148,-0.0339333406109823,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#336=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4610,#4611),(#4612,#4613),(#4614,
#4615),(#4616,#4617),(#4618,#4619)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0778727689218518,-0.037940707919432,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#337=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4633,#4634),(#4635,#4636),(#4637,
#4638),(#4639,#4640),(#4641,#4642)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0822847351075214,-0.0424829995581773,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#338=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4656,#4657),(#4658,#4659),(#4660,
#4661),(#4662,#4663),(#4664,#4665)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0724474583793122,-0.0369482130230788,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#339=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4679,#4680),(#4681,#4682),(#4683,
#4684),(#4685,#4686),(#4687,#4688)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0672386117563061,-0.0341599063551578,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#340=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4702,#4703),(#4704,#4705),(#4706,
#4707),(#4708,#4709),(#4710,#4711)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0457469430879444,-0.0234723442129683,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#341=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#4725,#4726),(#4727,#4728),(#4729,
#4730),(#4731,#4732),(#4733,#4734)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0415149509992502,-0.0212080417625968,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#342=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5186,#5187),(#5188,#5189),(#5190,
#5191),(#5192,#5193),(#5194,#5195)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0764563928333372,-0.0441199368341506,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#343=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5209,#5210),(#5211,#5212),(#5213,
#5214),(#5215,#5216),(#5217,#5218)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0593355585944129,-0.0281031935010044,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#344=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5232,#5233),(#5234,#5235),(#5236,
#5237),(#5238,#5239),(#5240,#5241)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0452926252606328,-0.0234804674011256,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#345=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5255,#5256),(#5257,#5258),(#5259,
#5260),(#5261,#5262),(#5263,#5264)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0415447549985437,-0.0208865994199229,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#346=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5278,#5279),(#5280,#5281),(#5282,
#5283),(#5284,#5285),(#5286,#5287)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0495840114104723,-0.0192163231380338,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#347=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5320,#5321),(#5322,#5323),(#5324,
#5325),(#5326,#5327),(#5328,#5329)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0610179249057739,-0.0351425252127339,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#348=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5337,#5338),(#5339,#5340),(#5341,
#5342),(#5343,#5344),(#5345,#5346)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0531830593723236,-0.0280116838365093,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#349=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5354,#5355),(#5356,#5357),(#5358,
#5359),(#5360,#5361),(#5362,#5363)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0504285700811963,-0.0243329228413605,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#350=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5371,#5372),(#5373,#5374),(#5375,
#5376),(#5377,#5378),(#5379,#5380)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0563143346515221,-0.0276366208085698,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#351=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5388,#5389),(#5390,#5391),(#5392,
#5393),(#5394,#5395),(#5396,#5397)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0941120824854342,-0.0478289062178764,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#352=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5405,#5406),(#5407,#5408),(#5409,
#5410),(#5411,#5412),(#5413,#5414)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.124958075778914,-0.0526606385410694,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#353=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5434,#5435),(#5436,#5437),(#5438,
#5439),(#5440,#5441),(#5442,#5443)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0824679802230752,-0.0349157223225691,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#354=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5460,#5461),(#5462,#5463),(#5464,
#5465),(#5466,#5467),(#5468,#5469)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.044023984659863,-0.0262654985723071,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#355=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5483,#5484),(#5485,#5486),(#5487,
#5488),(#5489,#5490),(#5491,#5492)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0254770566087581,-0.0130088901546045,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#356=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5506,#5507),(#5508,#5509),(#5510,
#5511),(#5512,#5513),(#5514,#5515)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.024444039256053,-0.0121154313089734,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#357=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5529,#5530),(#5531,#5532),(#5533,
#5534),(#5535,#5536),(#5537,#5538)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0367984783618504,-0.0183810245959774,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#358=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5552,#5553),(#5554,#5555),(#5556,
#5557),(#5558,#5559),(#5560,#5561)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0463233929083288,-0.0208731468986089,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#359=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5575,#5576),(#5577,#5578),(#5579,
#5580),(#5581,#5582),(#5583,#5584)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0523809064308374,-0.0269828327999049,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#360=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5598,#5599),(#5600,#5601),(#5602,
#5603),(#5604,#5605),(#5606,#5607)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0476611138758454,-0.0242179392985011,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#361=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5621,#5622),(#5623,#5624),(#5625,
#5626),(#5627,#5628),(#5629,#5630)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0500195490776112,-0.0214380485767212,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#362=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5647,#5648),(#5649,#5650),(#5651,
#5652),(#5653,#5654),(#5655,#5656)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0622355090113432,-0.0333160705264821,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#363=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5666,#5667),(#5668,#5669),(#5670,
#5671),(#5672,#5673),(#5674,#5675)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0550927229694672,-0.0270237886081428,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#364=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5683,#5684),(#5685,#5686),(#5687,
#5688),(#5689,#5690),(#5691,#5692)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0783178032537605,-0.0426200446754038,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#365=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5700,#5701),(#5702,#5703),(#5704,
#5705),(#5706,#5707),(#5708,#5709)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0665831495933769,-0.0328842735950209,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#366=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5717,#5718),(#5719,#5720),(#5721,
#5722),(#5723,#5724),(#5725,#5726)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0402282131334243,-0.0204671205231686,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#367=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5734,#5735),(#5736,#5737),(#5738,
#5739),(#5740,#5741),(#5742,#5743)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0383325958426253,-0.0195309299216581,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#368=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5751,#5752),(#5753,#5754),(#5755,
#5756),(#5757,#5758),(#5759,#5760)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.03907993228078,-0.0192071160588506,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#369=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5768,#5769),(#5770,#5771),(#5772,
#5773),(#5774,#5775),(#5776,#5777)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.04429491828703,-0.0185285273428258,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#370=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5785,#5786),(#5787,#5788),(#5789,
#5790),(#5791,#5792),(#5793,#5794)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0893114635073375,-0.0522194200444501,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#371=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5802,#5803),(#5804,#5805),(#5806,
#5807),(#5808,#5809),(#5810,#5811)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0128270421610497,-0.00825628776884585,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#372=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5819,#5820),(#5821,#5822),(#5823,
#5824),(#5825,#5826),(#5827,#5828)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0455185033807595,-0.0258152924767853,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#373=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5836,#5837),(#5838,#5839),(#5840,
#5841),(#5842,#5843),(#5844,#5845)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0593211525326363,-0.0265187098242649,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#374=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5853,#5854),(#5855,#5856),(#5857,
#5858),(#5859,#5860),(#5861,#5862)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0533700962879618,-0.0303059101301422,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#375=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5870,#5871),(#5872,#5873),(#5874,
#5875),(#5876,#5877),(#5878,#5879)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0499505815972015,-0.0219668478893245,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#376=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5891,#5892),(#5893,#5894),(#5895,
#5896),(#5897,#5898),(#5899,#5900)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.054499009467346,-0.0291479420906738,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#377=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5908,#5909),(#5910,#5911),(#5912,
#5913),(#5914,#5915),(#5916,#5917)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0528243808813189,-0.0249111657428554,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#378=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5925,#5926),(#5927,#5928),(#5929,
#5930),(#5931,#5932),(#5933,#5934)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0668244279968161,-0.031599261279197,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#379=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5942,#5943),(#5944,#5945),(#5946,
#5947),(#5948,#5949),(#5950,#5951)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0611630173404577,-0.0336997078729892,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#380=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5959,#5960),(#5961,#5962),(#5963,
#5964),(#5965,#5966),(#5967,#5968)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0409863474474436,-0.0224221928309154,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#381=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5976,#5977),(#5978,#5979),(#5980,
#5981),(#5982,#5983),(#5984,#5985)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0368978442172899,-0.0175268771387675,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#382=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#5993,#5994),(#5995,#5996),(#5997,
#5998),(#5999,#6000),(#6001,#6002)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0453962468666059,-0.017695038819937,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#383=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6014,#6015),(#6016,#6017),(#6018,
#6019),(#6020,#6021),(#6022,#6023)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0868538082872632,-0.0559395522165387,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#384=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6031,#6032),(#6033,#6034),(#6035,
#6036),(#6037,#6038),(#6039,#6040)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0369814900558673,-0.0183010076591476,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#385=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6052,#6053),(#6054,#6055),(#6056,
#6057),(#6058,#6059),(#6060,#6061)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0372171578948003,-0.0177238813261091,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#386=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6069,#6070),(#6071,#6072),(#6073,
#6074),(#6075,#6076),(#6077,#6078)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0877924724347864,-0.048988244755236,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#387=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6095,#6096),(#6097,#6098),(#6099,
#6100),(#6101,#6102),(#6103,#6104)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0634811660635357,-0.0328863779234484,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#388=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6118,#6119),(#6120,#6121),(#6122,
#6123),(#6124,#6125),(#6126,#6127)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0621144648696724,-0.0304532066826528,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#389=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6141,#6142),(#6143,#6144),(#6145,
#6146),(#6147,#6148),(#6149,#6150)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0849054207321809,-0.0374315329948845,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#390=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6164,#6165),(#6166,#6167),(#6168,
#6169),(#6170,#6171),(#6172,#6173)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0915482833461465,-0.0518862200258535,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#391=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6187,#6188),(#6189,#6190),(#6191,
#6192),(#6193,#6194),(#6195,#6196)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0646894740399855,-0.0330153633409254,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#392=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6210,#6211),(#6212,#6213),(#6214,
#6215),(#6216,#6217),(#6218,#6219)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.062679497807866,-0.0308156757974671,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#393=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6233,#6234),(#6235,#6236),(#6237,
#6238),(#6239,#6240),(#6241,#6242)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0883011242259416,-0.0381398411034351,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#394=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6259,#6260),(#6261,#6262),(#6263,
#6264),(#6265,#6266),(#6267,#6268)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0951654628153913,-0.0469469312268579,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#395=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6276,#6277),(#6278,#6279),(#6280,
#6281),(#6282,#6283),(#6284,#6285)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0673787929008857,-0.033628714622706,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#396=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6293,#6294),(#6295,#6296),(#6297,
#6298),(#6299,#6300),(#6301,#6302)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0703751792317449,-0.0344020868273981,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#397=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6310,#6311),(#6312,#6313),(#6314,
#6315),(#6316,#6317),(#6318,#6319)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0809018808622737,-0.0387648554762092,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#398=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6327,#6328),(#6329,#6330),(#6331,
#6332),(#6333,#6334),(#6335,#6336)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0802860291013455,-0.0414947744405871,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#399=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6344,#6345),(#6346,#6347),(#6348,
#6349),(#6350,#6351),(#6352,#6353)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0703420182699884,-0.0361532003492885,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#400=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6361,#6362),(#6363,#6364),(#6365,
#6366),(#6367,#6368),(#6369,#6370)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0680878258389567,-0.033725650843392,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#401=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6378,#6379),(#6380,#6381),(#6382,
#6383),(#6384,#6385),(#6386,#6387)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0483674301054939,-0.0249186414278097,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#402=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6395,#6396),(#6397,#6398),(#6399,
#6400),(#6401,#6402),(#6403,#6404)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0442114320715124,-0.0224386745972272,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#403=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6428,#6429),(#6430,#6431),(#6432,
#6433),(#6434,#6435),(#6436,#6437)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0824679540867423,-0.0349156082050476,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#404=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6454,#6455),(#6456,#6457),(#6458,
#6459),(#6460,#6461),(#6462,#6463)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0440240123342674,-0.0262654692383923,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#405=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6477,#6478),(#6479,#6480),(#6481,
#6482),(#6483,#6484),(#6485,#6486)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0254770196494594,-0.0130088137380629,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#406=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6500,#6501),(#6502,#6503),(#6504,
#6505),(#6506,#6507),(#6508,#6509)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0244440307937324,-0.0121154187243702,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#407=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6523,#6524),(#6525,#6526),(#6527,
#6528),(#6529,#6530),(#6531,#6532)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0367984903063589,-0.0183810365404859,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#408=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6546,#6547),(#6548,#6549),(#6550,
#6551),(#6552,#6553),(#6554,#6555)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0463233666121738,-0.0208731206024539,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#409=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6569,#6570),(#6571,#6572),(#6573,
#6574),(#6575,#6576),(#6577,#6578)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0523809027619369,-0.026982773582202,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#410=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6592,#6593),(#6594,#6595),(#6596,
#6597),(#6598,#6599),(#6600,#6601)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0476611138758454,-0.0242179392985011,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#411=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6615,#6616),(#6617,#6618),(#6619,
#6620),(#6621,#6622),(#6623,#6624)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0500195573265714,-0.0214380650765451,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#412=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6641,#6642),(#6643,#6644),(#6645,
#6646),(#6647,#6648),(#6649,#6650)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0622355569718287,-0.0333161184869677,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#413=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6660,#6661),(#6662,#6663),(#6664,
#6665),(#6666,#6667),(#6668,#6669)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0550926951907323,-0.0270237311350868,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#414=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6677,#6678),(#6679,#6680),(#6681,
#6682),(#6683,#6684),(#6685,#6686)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0783177667280387,-0.0426199560996444,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#415=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6694,#6695),(#6696,#6697),(#6698,
#6699),(#6700,#6701),(#6702,#6703)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0665831787891137,-0.0328843084613603,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#416=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6711,#6712),(#6713,#6714),(#6715,
#6716),(#6717,#6718),(#6719,#6720)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0402282493870738,-0.0204671135702732,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#417=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6728,#6729),(#6730,#6731),(#6732,
#6733),(#6734,#6735),(#6736,#6737)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0383325562867562,-0.0195309127474034,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#418=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6745,#6746),(#6747,#6748),(#6749,
#6750),(#6751,#6752),(#6753,#6754)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0390799903304408,-0.0192070900219338,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#419=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6762,#6763),(#6764,#6765),(#6766,
#6767),(#6768,#6769),(#6770,#6771)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0442948897470726,-0.0185284397878466,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#420=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6779,#6780),(#6781,#6782),(#6783,
#6784),(#6785,#6786),(#6787,#6788)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0893114045672862,-0.0522193611043987,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#421=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6796,#6797),(#6798,#6799),(#6800,
#6801),(#6802,#6803),(#6804,#6805)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0128270451155165,-0.0082562907233126,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#422=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6813,#6814),(#6815,#6816),(#6817,
#6818),(#6819,#6820),(#6821,#6822)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0455185033807595,-0.0258152924767853,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#423=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6830,#6831),(#6832,#6833),(#6834,
#6835),(#6836,#6837),(#6838,#6839)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0593212346823061,-0.0265187624123591,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#424=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6847,#6848),(#6849,#6850),(#6851,
#6852),(#6853,#6854),(#6855,#6856)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0533700416403343,-0.0303059101301422,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#425=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6864,#6865),(#6866,#6867),(#6868,
#6869),(#6870,#6871),(#6872,#6873)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0499505815972015,-0.0219668478893245,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#426=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6885,#6886),(#6887,#6888),(#6889,
#6890),(#6891,#6892),(#6893,#6894)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0544989854539011,-0.0291479332331411,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#427=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6902,#6903),(#6904,#6905),(#6906,
#6907),(#6908,#6909),(#6910,#6911)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0528243665518279,-0.0249112315986868,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#428=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6919,#6920),(#6921,#6922),(#6923,
#6924),(#6925,#6926),(#6927,#6928)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0668244263253945,-0.0315993190508233,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#429=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6936,#6937),(#6938,#6939),(#6940,
#6941),(#6942,#6943),(#6944,#6945)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0611630752258989,-0.0336997078729892,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#430=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6953,#6954),(#6955,#6956),(#6957,
#6958),(#6959,#6960),(#6961,#6962)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0409863474474436,-0.0224221928309154,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#431=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6970,#6971),(#6972,#6973),(#6974,
#6975),(#6976,#6977),(#6978,#6979)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0368978496094305,-0.0175268455900678,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#432=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#6987,#6988),(#6989,#6990),(#6991,
#6992),(#6993,#6994),(#6995,#6996)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0453962406627712,-0.0176950326161023,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#433=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#7008,#7009),(#7010,#7011),(#7012,
#7013),(#7014,#7015),(#7016,#7017)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0868538118276596,-0.0559395514169812,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#434=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#7025,#7026),(#7027,#7028),(#7029,
#7030),(#7031,#7032),(#7033,#7034)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.0369814993166081,-0.0183009921077505,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#435=B_SPLINE_SURFACE_WITH_KNOTS('',3,1,((#7046,#7047),(#7048,#7049),(#7050,
#7051),(#7052,#7053),(#7054,#7055)),.UNSPECIFIED.,.F.,.F.,.F.,(4,1,4),(2,
2),(-0.037217178999239,-0.0177239024305477,0.),(0.,0.0499999999999998),
 .UNSPECIFIED.);
#436=FACE_OUTER_BOUND('',#630,.T.);
#437=FACE_OUTER_BOUND('',#631,.T.);
#438=FACE_OUTER_BOUND('',#632,.T.);
#439=FACE_OUTER_BOUND('',#633,.T.);
#440=FACE_OUTER_BOUND('',#634,.T.);
#441=FACE_OUTER_BOUND('',#635,.T.);
#442=FACE_OUTER_BOUND('',#636,.T.);
#443=FACE_OUTER_BOUND('',#637,.T.);
#444=FACE_OUTER_BOUND('',#638,.T.);
#445=FACE_OUTER_BOUND('',#639,.T.);
#446=FACE_OUTER_BOUND('',#640,.T.);
#447=FACE_OUTER_BOUND('',#641,.T.);
#448=FACE_OUTER_BOUND('',#642,.T.);
#449=FACE_OUTER_BOUND('',#643,.T.);
#450=FACE_OUTER_BOUND('',#644,.T.);
#451=FACE_OUTER_BOUND('',#645,.T.);
#452=FACE_OUTER_BOUND('',#646,.T.);
#453=FACE_OUTER_BOUND('',#647,.T.);
#454=FACE_OUTER_BOUND('',#648,.T.);
#455=FACE_OUTER_BOUND('',#649,.T.);
#456=FACE_OUTER_BOUND('',#650,.T.);
#457=FACE_OUTER_BOUND('',#651,.T.);
#458=FACE_OUTER_BOUND('',#652,.T.);
#459=FACE_OUTER_BOUND('',#653,.T.);
#460=FACE_OUTER_BOUND('',#654,.T.);
#461=FACE_OUTER_BOUND('',#655,.T.);
#462=FACE_OUTER_BOUND('',#656,.T.);
#463=FACE_OUTER_BOUND('',#657,.T.);
#464=FACE_OUTER_BOUND('',#658,.T.);
#465=FACE_OUTER_BOUND('',#659,.T.);
#466=FACE_OUTER_BOUND('',#660,.T.);
#467=FACE_OUTER_BOUND('',#661,.T.);
#468=FACE_OUTER_BOUND('',#662,.T.);
#469=FACE_OUTER_BOUND('',#663,.T.);
#470=FACE_OUTER_BOUND('',#664,.T.);
#471=FACE_OUTER_BOUND('',#665,.T.);
#472=FACE_OUTER_BOUND('',#666,.T.);
#473=FACE_OUTER_BOUND('',#668,.T.);
#474=FACE_OUTER_BOUND('',#675,.T.);
#475=FACE_OUTER_BOUND('',#676,.T.);
#476=FACE_OUTER_BOUND('',#677,.T.);
#477=FACE_OUTER_BOUND('',#678,.T.);
#478=FACE_OUTER_BOUND('',#679,.T.);
#479=FACE_OUTER_BOUND('',#680,.T.);
#480=FACE_OUTER_BOUND('',#681,.T.);
#481=FACE_OUTER_BOUND('',#682,.T.);
#482=FACE_OUTER_BOUND('',#683,.T.);
#483=FACE_OUTER_BOUND('',#684,.T.);
#484=FACE_OUTER_BOUND('',#685,.T.);
#485=FACE_OUTER_BOUND('',#686,.T.);
#486=FACE_OUTER_BOUND('',#687,.T.);
#487=FACE_OUTER_BOUND('',#688,.T.);
#488=FACE_OUTER_BOUND('',#689,.T.);
#489=FACE_OUTER_BOUND('',#690,.T.);
#490=FACE_OUTER_BOUND('',#691,.T.);
#491=FACE_OUTER_BOUND('',#692,.T.);
#492=FACE_OUTER_BOUND('',#693,.T.);
#493=FACE_OUTER_BOUND('',#694,.T.);
#494=FACE_OUTER_BOUND('',#696,.T.);
#495=FACE_OUTER_BOUND('',#697,.T.);
#496=FACE_OUTER_BOUND('',#698,.T.);
#497=FACE_OUTER_BOUND('',#699,.T.);
#498=FACE_OUTER_BOUND('',#700,.T.);
#499=FACE_OUTER_BOUND('',#701,.T.);
#500=FACE_OUTER_BOUND('',#702,.T.);
#501=FACE_OUTER_BOUND('',#703,.T.);
#502=FACE_OUTER_BOUND('',#704,.T.);
#503=FACE_OUTER_BOUND('',#705,.T.);
#504=FACE_OUTER_BOUND('',#706,.T.);
#505=FACE_OUTER_BOUND('',#707,.T.);
#506=FACE_OUTER_BOUND('',#708,.T.);
#507=FACE_OUTER_BOUND('',#709,.T.);
#508=FACE_OUTER_BOUND('',#710,.T.);
#509=FACE_OUTER_BOUND('',#711,.T.);
#510=FACE_OUTER_BOUND('',#712,.T.);
#511=FACE_OUTER_BOUND('',#713,.T.);
#512=FACE_OUTER_BOUND('',#714,.T.);
#513=FACE_OUTER_BOUND('',#715,.T.);
#514=FACE_OUTER_BOUND('',#716,.T.);
#515=FACE_OUTER_BOUND('',#717,.T.);
#516=FACE_OUTER_BOUND('',#718,.T.);
#517=FACE_OUTER_BOUND('',#719,.T.);
#518=FACE_OUTER_BOUND('',#720,.T.);
#519=FACE_OUTER_BOUND('',#721,.T.);
#520=FACE_OUTER_BOUND('',#722,.T.);
#521=FACE_OUTER_BOUND('',#723,.T.);
#522=FACE_OUTER_BOUND('',#724,.T.);
#523=FACE_OUTER_BOUND('',#725,.T.);
#524=FACE_OUTER_BOUND('',#726,.T.);
#525=FACE_OUTER_BOUND('',#727,.T.);
#526=FACE_OUTER_BOUND('',#728,.T.);
#527=FACE_OUTER_BOUND('',#729,.T.);
#528=FACE_OUTER_BOUND('',#730,.T.);
#529=FACE_OUTER_BOUND('',#731,.T.);
#530=FACE_OUTER_BOUND('',#732,.T.);
#531=FACE_OUTER_BOUND('',#733,.T.);
#532=FACE_OUTER_BOUND('',#734,.T.);
#533=FACE_OUTER_BOUND('',#736,.T.);
#534=FACE_OUTER_BOUND('',#737,.T.);
#535=FACE_OUTER_BOUND('',#738,.T.);
#536=FACE_OUTER_BOUND('',#739,.T.);
#537=FACE_OUTER_BOUND('',#740,.T.);
#538=FACE_OUTER_BOUND('',#741,.T.);
#539=FACE_OUTER_BOUND('',#742,.T.);
#540=FACE_OUTER_BOUND('',#743,.T.);
#541=FACE_OUTER_BOUND('',#744,.T.);
#542=FACE_OUTER_BOUND('',#745,.T.);
#543=FACE_OUTER_BOUND('',#746,.T.);
#544=FACE_OUTER_BOUND('',#747,.T.);
#545=FACE_OUTER_BOUND('',#748,.T.);
#546=FACE_OUTER_BOUND('',#749,.T.);
#547=FACE_OUTER_BOUND('',#750,.T.);
#548=FACE_OUTER_BOUND('',#751,.T.);
#549=FACE_OUTER_BOUND('',#752,.T.);
#550=FACE_OUTER_BOUND('',#753,.T.);
#551=FACE_OUTER_BOUND('',#754,.T.);
#552=FACE_OUTER_BOUND('',#755,.T.);
#553=FACE_OUTER_BOUND('',#756,.T.);
#554=FACE_OUTER_BOUND('',#757,.T.);
#555=FACE_OUTER_BOUND('',#758,.T.);
#556=FACE_OUTER_BOUND('',#759,.T.);
#557=FACE_OUTER_BOUND('',#761,.T.);
#558=FACE_OUTER_BOUND('',#762,.T.);
#559=FACE_OUTER_BOUND('',#763,.T.);
#560=FACE_OUTER_BOUND('',#764,.T.);
#561=FACE_OUTER_BOUND('',#765,.T.);
#562=FACE_OUTER_BOUND('',#766,.T.);
#563=FACE_OUTER_BOUND('',#767,.T.);
#564=FACE_OUTER_BOUND('',#768,.T.);
#565=FACE_OUTER_BOUND('',#769,.T.);
#566=FACE_OUTER_BOUND('',#770,.T.);
#567=FACE_OUTER_BOUND('',#771,.T.);
#568=FACE_OUTER_BOUND('',#772,.T.);
#569=FACE_OUTER_BOUND('',#773,.T.);
#570=FACE_OUTER_BOUND('',#774,.T.);
#571=FACE_OUTER_BOUND('',#775,.T.);
#572=FACE_OUTER_BOUND('',#776,.T.);
#573=FACE_OUTER_BOUND('',#777,.T.);
#574=FACE_OUTER_BOUND('',#778,.T.);
#575=FACE_OUTER_BOUND('',#779,.T.);
#576=FACE_OUTER_BOUND('',#780,.T.);
#577=FACE_OUTER_BOUND('',#781,.T.);
#578=FACE_OUTER_BOUND('',#782,.T.);
#579=FACE_OUTER_BOUND('',#783,.T.);
#580=FACE_OUTER_BOUND('',#784,.T.);
#581=FACE_OUTER_BOUND('',#785,.T.);
#582=FACE_OUTER_BOUND('',#786,.T.);
#583=FACE_OUTER_BOUND('',#787,.T.);
#584=FACE_OUTER_BOUND('',#788,.T.);
#585=FACE_OUTER_BOUND('',#789,.T.);
#586=FACE_OUTER_BOUND('',#790,.T.);
#587=FACE_OUTER_BOUND('',#791,.T.);
#588=FACE_OUTER_BOUND('',#792,.T.);
#589=FACE_OUTER_BOUND('',#793,.T.);
#590=FACE_OUTER_BOUND('',#794,.T.);
#591=FACE_OUTER_BOUND('',#795,.T.);
#592=FACE_OUTER_BOUND('',#796,.T.);
#593=FACE_OUTER_BOUND('',#797,.T.);
#594=FACE_OUTER_BOUND('',#798,.T.);
#595=FACE_OUTER_BOUND('',#799,.T.);
#596=FACE_OUTER_BOUND('',#801,.T.);
#597=FACE_OUTER_BOUND('',#802,.T.);
#598=FACE_OUTER_BOUND('',#807,.T.);
#599=FACE_OUTER_BOUND('',#808,.T.);
#600=FACE_OUTER_BOUND('',#809,.T.);
#601=FACE_OUTER_BOUND('',#810,.T.);
#602=FACE_OUTER_BOUND('',#811,.T.);
#603=FACE_OUTER_BOUND('',#812,.T.);
#604=FACE_OUTER_BOUND('',#813,.T.);
#605=FACE_OUTER_BOUND('',#814,.T.);
#606=FACE_OUTER_BOUND('',#815,.T.);
#607=FACE_OUTER_BOUND('',#816,.T.);
#608=FACE_OUTER_BOUND('',#817,.T.);
#609=FACE_OUTER_BOUND('',#818,.T.);
#610=FACE_OUTER_BOUND('',#819,.T.);
#611=FACE_OUTER_BOUND('',#820,.T.);
#612=FACE_OUTER_BOUND('',#821,.T.);
#613=FACE_OUTER_BOUND('',#822,.T.);
#614=FACE_OUTER_BOUND('',#823,.T.);
#615=FACE_OUTER_BOUND('',#824,.T.);
#616=FACE_OUTER_BOUND('',#825,.T.);
#617=FACE_OUTER_BOUND('',#826,.T.);
#618=FACE_OUTER_BOUND('',#827,.T.);
#619=FACE_OUTER_BOUND('',#828,.T.);
#620=FACE_OUTER_BOUND('',#829,.T.);
#621=FACE_OUTER_BOUND('',#830,.T.);
#622=FACE_OUTER_BOUND('',#835,.T.);
#623=FACE_OUTER_BOUND('',#836,.T.);
#624=FACE_OUTER_BOUND('',#838,.T.);
#625=FACE_OUTER_BOUND('',#839,.T.);
#626=FACE_OUTER_BOUND('',#841,.T.);
#627=FACE_OUTER_BOUND('',#842,.T.);
#628=FACE_OUTER_BOUND('',#844,.T.);
#629=FACE_OUTER_BOUND('',#845,.T.);
#630=EDGE_LOOP('',(#2266,#2267,#2268,#2269));
#631=EDGE_LOOP('',(#2270,#2271,#2272,#2273));
#632=EDGE_LOOP('',(#2274,#2275,#2276,#2277));
#633=EDGE_LOOP('',(#2278,#2279,#2280,#2281));
#634=EDGE_LOOP('',(#2282,#2283,#2284,#2285));
#635=EDGE_LOOP('',(#2286,#2287,#2288,#2289));
#636=EDGE_LOOP('',(#2290,#2291,#2292,#2293));
#637=EDGE_LOOP('',(#2294,#2295,#2296,#2297));
#638=EDGE_LOOP('',(#2298,#2299,#2300,#2301));
#639=EDGE_LOOP('',(#2302,#2303,#2304,#2305));
#640=EDGE_LOOP('',(#2306,#2307,#2308,#2309));
#641=EDGE_LOOP('',(#2310,#2311,#2312,#2313));
#642=EDGE_LOOP('',(#2314,#2315,#2316,#2317,#2318,#2319,#2320,#2321,#2322,
#2323,#2324,#2325));
#643=EDGE_LOOP('',(#2326,#2327,#2328,#2329));
#644=EDGE_LOOP('',(#2330,#2331,#2332,#2333));
#645=EDGE_LOOP('',(#2334,#2335,#2336,#2337));
#646=EDGE_LOOP('',(#2338,#2339,#2340,#2341));
#647=EDGE_LOOP('',(#2342,#2343,#2344,#2345));
#648=EDGE_LOOP('',(#2346,#2347,#2348,#2349));
#649=EDGE_LOOP('',(#2350,#2351,#2352,#2353));
#650=EDGE_LOOP('',(#2354,#2355,#2356,#2357));
#651=EDGE_LOOP('',(#2358,#2359,#2360,#2361));
#652=EDGE_LOOP('',(#2362,#2363,#2364,#2365));
#653=EDGE_LOOP('',(#2366,#2367,#2368,#2369));
#654=EDGE_LOOP('',(#2370,#2371,#2372,#2373));
#655=EDGE_LOOP('',(#2374,#2375,#2376,#2377));
#656=EDGE_LOOP('',(#2378,#2379,#2380,#2381));
#657=EDGE_LOOP('',(#2382,#2383,#2384,#2385));
#658=EDGE_LOOP('',(#2386,#2387,#2388,#2389));
#659=EDGE_LOOP('',(#2390,#2391,#2392,#2393));
#660=EDGE_LOOP('',(#2394,#2395,#2396,#2397));
#661=EDGE_LOOP('',(#2398,#2399,#2400,#2401));
#662=EDGE_LOOP('',(#2402,#2403,#2404,#2405));
#663=EDGE_LOOP('',(#2406,#2407,#2408,#2409));
#664=EDGE_LOOP('',(#2410,#2411,#2412,#2413));
#665=EDGE_LOOP('',(#2414,#2415,#2416,#2417));
#666=EDGE_LOOP('',(#2418,#2419,#2420,#2421,#2422,#2423,#2424,#2425,#2426,
#2427,#2428,#2429,#2430,#2431,#2432));
#667=EDGE_LOOP('',(#2433,#2434,#2435,#2436,#2437,#2438,#2439,#2440));
#668=EDGE_LOOP('',(#2441,#2442,#2443,#2444));
#669=EDGE_LOOP('',(#2445,#2446,#2447,#2448,#2449,#2450,#2451,#2452,#2453,
#2454,#2455,#2456));
#670=EDGE_LOOP('',(#2457,#2458,#2459,#2460,#2461,#2462,#2463,#2464,#2465,
#2466,#2467));
#671=EDGE_LOOP('',(#2468,#2469,#2470,#2471,#2472,#2473,#2474,#2475,#2476,
#2477,#2478,#2479,#2480,#2481));
#672=EDGE_LOOP('',(#2482,#2483,#2484,#2485,#2486,#2487,#2488,#2489,#2490,
#2491,#2492,#2493,#2494,#2495,#2496,#2497,#2498,#2499,#2500,#2501,#2502,
#2503,#2504,#2505,#2506,#2507,#2508));
#673=EDGE_LOOP('',(#2509,#2510,#2511,#2512,#2513,#2514,#2515,#2516,#2517,
#2518,#2519,#2520,#2521,#2522,#2523,#2524,#2525,#2526,#2527,#2528,#2529,
#2530,#2531,#2532,#2533,#2534,#2535));
#674=EDGE_LOOP('',(#2536,#2537,#2538,#2539,#2540,#2541,#2542,#2543,#2544,
#2545,#2546,#2547,#2548,#2549,#2550));
#675=EDGE_LOOP('',(#2551,#2552,#2553,#2554));
#676=EDGE_LOOP('',(#2555,#2556,#2557,#2558));
#677=EDGE_LOOP('',(#2559,#2560,#2561,#2562));
#678=EDGE_LOOP('',(#2563,#2564,#2565,#2566));
#679=EDGE_LOOP('',(#2567,#2568,#2569,#2570));
#680=EDGE_LOOP('',(#2571,#2572,#2573,#2574));
#681=EDGE_LOOP('',(#2575,#2576,#2577,#2578));
#682=EDGE_LOOP('',(#2579,#2580,#2581,#2582));
#683=EDGE_LOOP('',(#2583,#2584,#2585,#2586));
#684=EDGE_LOOP('',(#2587,#2588,#2589,#2590));
#685=EDGE_LOOP('',(#2591,#2592,#2593,#2594));
#686=EDGE_LOOP('',(#2595,#2596,#2597,#2598));
#687=EDGE_LOOP('',(#2599,#2600,#2601,#2602));
#688=EDGE_LOOP('',(#2603,#2604,#2605,#2606));
#689=EDGE_LOOP('',(#2607,#2608,#2609,#2610));
#690=EDGE_LOOP('',(#2611,#2612,#2613,#2614));
#691=EDGE_LOOP('',(#2615,#2616,#2617,#2618));
#692=EDGE_LOOP('',(#2619,#2620,#2621,#2622));
#693=EDGE_LOOP('',(#2623,#2624,#2625,#2626));
#694=EDGE_LOOP('',(#2627,#2628,#2629,#2630,#2631,#2632,#2633,#2634,#2635,
#2636,#2637));
#695=EDGE_LOOP('',(#2638,#2639,#2640,#2641,#2642,#2643,#2644,#2645));
#696=EDGE_LOOP('',(#2646,#2647,#2648,#2649,#2650,#2651,#2652,#2653));
#697=EDGE_LOOP('',(#2654,#2655,#2656,#2657));
#698=EDGE_LOOP('',(#2658,#2659,#2660,#2661));
#699=EDGE_LOOP('',(#2662,#2663,#2664,#2665));
#700=EDGE_LOOP('',(#2666,#2667,#2668,#2669));
#701=EDGE_LOOP('',(#2670,#2671,#2672,#2673));
#702=EDGE_LOOP('',(#2674,#2675,#2676,#2677));
#703=EDGE_LOOP('',(#2678,#2679,#2680,#2681));
#704=EDGE_LOOP('',(#2682,#2683,#2684,#2685));
#705=EDGE_LOOP('',(#2686,#2687,#2688,#2689));
#706=EDGE_LOOP('',(#2690,#2691,#2692,#2693));
#707=EDGE_LOOP('',(#2694,#2695,#2696,#2697));
#708=EDGE_LOOP('',(#2698,#2699,#2700,#2701));
#709=EDGE_LOOP('',(#2702,#2703,#2704,#2705));
#710=EDGE_LOOP('',(#2706,#2707,#2708,#2709));
#711=EDGE_LOOP('',(#2710,#2711,#2712,#2713));
#712=EDGE_LOOP('',(#2714,#2715,#2716,#2717));
#713=EDGE_LOOP('',(#2718,#2719,#2720,#2721));
#714=EDGE_LOOP('',(#2722,#2723,#2724,#2725));
#715=EDGE_LOOP('',(#2726,#2727,#2728,#2729));
#716=EDGE_LOOP('',(#2730,#2731,#2732,#2733));
#717=EDGE_LOOP('',(#2734,#2735,#2736,#2737));
#718=EDGE_LOOP('',(#2738,#2739,#2740,#2741));
#719=EDGE_LOOP('',(#2742,#2743,#2744,#2745));
#720=EDGE_LOOP('',(#2746,#2747,#2748,#2749));
#721=EDGE_LOOP('',(#2750,#2751,#2752,#2753));
#722=EDGE_LOOP('',(#2754,#2755,#2756,#2757));
#723=EDGE_LOOP('',(#2758,#2759,#2760,#2761));
#724=EDGE_LOOP('',(#2762,#2763,#2764,#2765));
#725=EDGE_LOOP('',(#2766,#2767,#2768,#2769));
#726=EDGE_LOOP('',(#2770,#2771,#2772,#2773));
#727=EDGE_LOOP('',(#2774,#2775,#2776,#2777));
#728=EDGE_LOOP('',(#2778,#2779,#2780,#2781));
#729=EDGE_LOOP('',(#2782,#2783,#2784,#2785));
#730=EDGE_LOOP('',(#2786,#2787,#2788,#2789));
#731=EDGE_LOOP('',(#2790,#2791,#2792,#2793));
#732=EDGE_LOOP('',(#2794,#2795,#2796,#2797));
#733=EDGE_LOOP('',(#2798,#2799,#2800,#2801));
#734=EDGE_LOOP('',(#2802,#2803,#2804,#2805,#2806,#2807,#2808,#2809,#2810,
#2811,#2812,#2813,#2814,#2815,#2816,#2817,#2818,#2819,#2820,#2821,#2822,
#2823,#2824,#2825,#2826,#2827,#2828));
#735=EDGE_LOOP('',(#2829,#2830,#2831,#2832,#2833,#2834,#2835,#2836,#2837,
#2838));
#736=EDGE_LOOP('',(#2839,#2840,#2841,#2842,#2843,#2844,#2845,#2846));
#737=EDGE_LOOP('',(#2847,#2848,#2849,#2850));
#738=EDGE_LOOP('',(#2851,#2852,#2853,#2854));
#739=EDGE_LOOP('',(#2855,#2856,#2857,#2858));
#740=EDGE_LOOP('',(#2859,#2860,#2861,#2862));
#741=EDGE_LOOP('',(#2863,#2864,#2865,#2866));
#742=EDGE_LOOP('',(#2867,#2868,#2869,#2870));
#743=EDGE_LOOP('',(#2871,#2872,#2873,#2874));
#744=EDGE_LOOP('',(#2875,#2876,#2877,#2878));
#745=EDGE_LOOP('',(#2879,#2880,#2881,#2882));
#746=EDGE_LOOP('',(#2883,#2884,#2885,#2886));
#747=EDGE_LOOP('',(#2887,#2888,#2889,#2890));
#748=EDGE_LOOP('',(#2891,#2892,#2893,#2894));
#749=EDGE_LOOP('',(#2895,#2896,#2897,#2898));
#750=EDGE_LOOP('',(#2899,#2900,#2901,#2902));
#751=EDGE_LOOP('',(#2903,#2904,#2905,#2906));
#752=EDGE_LOOP('',(#2907,#2908,#2909,#2910));
#753=EDGE_LOOP('',(#2911,#2912,#2913,#2914));
#754=EDGE_LOOP('',(#2915,#2916,#2917,#2918));
#755=EDGE_LOOP('',(#2919,#2920,#2921,#2922));
#756=EDGE_LOOP('',(#2923,#2924,#2925,#2926));
#757=EDGE_LOOP('',(#2927,#2928,#2929,#2930));
#758=EDGE_LOOP('',(#2931,#2932,#2933,#2934));
#759=EDGE_LOOP('',(#2935,#2936,#2937,#2938,#2939,#2940,#2941,#2942,#2943,
#2944,#2945,#2946,#2947,#2948));
#760=EDGE_LOOP('',(#2949,#2950,#2951,#2952,#2953,#2954,#2955,#2956));
#761=EDGE_LOOP('',(#2957,#2958,#2959,#2960,#2961,#2962,#2963,#2964,#2965,
#2966));
#762=EDGE_LOOP('',(#2967,#2968,#2969,#2970));
#763=EDGE_LOOP('',(#2971,#2972,#2973,#2974));
#764=EDGE_LOOP('',(#2975,#2976,#2977,#2978));
#765=EDGE_LOOP('',(#2979,#2980,#2981,#2982));
#766=EDGE_LOOP('',(#2983,#2984,#2985,#2986));
#767=EDGE_LOOP('',(#2987,#2988,#2989,#2990));
#768=EDGE_LOOP('',(#2991,#2992,#2993,#2994));
#769=EDGE_LOOP('',(#2995,#2996,#2997,#2998));
#770=EDGE_LOOP('',(#2999,#3000,#3001,#3002));
#771=EDGE_LOOP('',(#3003,#3004,#3005,#3006));
#772=EDGE_LOOP('',(#3007,#3008,#3009,#3010));
#773=EDGE_LOOP('',(#3011,#3012,#3013,#3014));
#774=EDGE_LOOP('',(#3015,#3016,#3017,#3018));
#775=EDGE_LOOP('',(#3019,#3020,#3021,#3022));
#776=EDGE_LOOP('',(#3023,#3024,#3025,#3026));
#777=EDGE_LOOP('',(#3027,#3028,#3029,#3030));
#778=EDGE_LOOP('',(#3031,#3032,#3033,#3034));
#779=EDGE_LOOP('',(#3035,#3036,#3037,#3038));
#780=EDGE_LOOP('',(#3039,#3040,#3041,#3042));
#781=EDGE_LOOP('',(#3043,#3044,#3045,#3046));
#782=EDGE_LOOP('',(#3047,#3048,#3049,#3050));
#783=EDGE_LOOP('',(#3051,#3052,#3053,#3054));
#784=EDGE_LOOP('',(#3055,#3056,#3057,#3058));
#785=EDGE_LOOP('',(#3059,#3060,#3061,#3062));
#786=EDGE_LOOP('',(#3063,#3064,#3065,#3066));
#787=EDGE_LOOP('',(#3067,#3068,#3069,#3070));
#788=EDGE_LOOP('',(#3071,#3072,#3073,#3074));
#789=EDGE_LOOP('',(#3075,#3076,#3077,#3078));
#790=EDGE_LOOP('',(#3079,#3080,#3081,#3082));
#791=EDGE_LOOP('',(#3083,#3084,#3085,#3086));
#792=EDGE_LOOP('',(#3087,#3088,#3089,#3090));
#793=EDGE_LOOP('',(#3091,#3092,#3093,#3094));
#794=EDGE_LOOP('',(#3095,#3096,#3097,#3098));
#795=EDGE_LOOP('',(#3099,#3100,#3101,#3102));
#796=EDGE_LOOP('',(#3103,#3104,#3105,#3106));
#797=EDGE_LOOP('',(#3107,#3108,#3109,#3110));
#798=EDGE_LOOP('',(#3111,#3112,#3113,#3114));
#799=EDGE_LOOP('',(#3115,#3116,#3117,#3118,#3119,#3120,#3121,#3122,#3123,
#3124,#3125,#3126,#3127,#3128,#3129,#3130,#3131,#3132,#3133,#3134,#3135,
#3136,#3137,#3138,#3139,#3140,#3141));
#800=EDGE_LOOP('',(#3142,#3143,#3144,#3145,#3146,#3147,#3148,#3149,#3150,
#3151));
#801=EDGE_LOOP('',(#3152,#3153,#3154,#3155,#3156,#3157,#3158,#3159));
#802=EDGE_LOOP('',(#3160,#3161,#3162,#3163,#3164,#3165,#3166,#3167,#3168,
#3169,#3170,#3171,#3172,#3173,#3174,#3175,#3176,#3177));
#803=EDGE_LOOP('',(#3178));
#804=EDGE_LOOP('',(#3179));
#805=EDGE_LOOP('',(#3180));
#806=EDGE_LOOP('',(#3181));
#807=EDGE_LOOP('',(#3182,#3183,#3184,#3185));
#808=EDGE_LOOP('',(#3186,#3187,#3188,#3189));
#809=EDGE_LOOP('',(#3190,#3191,#3192,#3193));
#810=EDGE_LOOP('',(#3194,#3195,#3196,#3197));
#811=EDGE_LOOP('',(#3198,#3199,#3200,#3201));
#812=EDGE_LOOP('',(#3202,#3203,#3204,#3205));
#813=EDGE_LOOP('',(#3206,#3207,#3208,#3209));
#814=EDGE_LOOP('',(#3210,#3211,#3212,#3213));
#815=EDGE_LOOP('',(#3214,#3215,#3216,#3217));
#816=EDGE_LOOP('',(#3218,#3219,#3220,#3221,#3222,#3223,#3224,#3225));
#817=EDGE_LOOP('',(#3226,#3227,#3228,#3229));
#818=EDGE_LOOP('',(#3230,#3231,#3232,#3233));
#819=EDGE_LOOP('',(#3234,#3235,#3236,#3237,#3238,#3239,#3240,#3241,#3242,
#3243));
#820=EDGE_LOOP('',(#3244,#3245,#3246,#3247));
#821=EDGE_LOOP('',(#3248,#3249,#3250,#3251));
#822=EDGE_LOOP('',(#3252,#3253,#3254,#3255,#3256,#3257,#3258,#3259));
#823=EDGE_LOOP('',(#3260,#3261,#3262,#3263));
#824=EDGE_LOOP('',(#3264,#3265,#3266,#3267));
#825=EDGE_LOOP('',(#3268,#3269,#3270,#3271));
#826=EDGE_LOOP('',(#3272,#3273,#3274,#3275));
#827=EDGE_LOOP('',(#3276,#3277,#3278,#3279));
#828=EDGE_LOOP('',(#3280,#3281,#3282,#3283));
#829=EDGE_LOOP('',(#3284,#3285,#3286,#3287));
#830=EDGE_LOOP('',(#3288,#3289,#3290,#3291,#3292,#3293,#3294,#3295));
#831=EDGE_LOOP('',(#3296));
#832=EDGE_LOOP('',(#3297));
#833=EDGE_LOOP('',(#3298));
#834=EDGE_LOOP('',(#3299));
#835=EDGE_LOOP('',(#3300,#3301,#3302,#3303));
#836=EDGE_LOOP('',(#3304));
#837=EDGE_LOOP('',(#3305));
#838=EDGE_LOOP('',(#3306,#3307,#3308,#3309));
#839=EDGE_LOOP('',(#3310));
#840=EDGE_LOOP('',(#3311));
#841=EDGE_LOOP('',(#3312,#3313,#3314,#3315));
#842=EDGE_LOOP('',(#3316));
#843=EDGE_LOOP('',(#3317));
#844=EDGE_LOOP('',(#3318,#3319,#3320,#3321));
#845=EDGE_LOOP('',(#3322));
#846=EDGE_LOOP('',(#3323));
#847=LINE('',#4226,#1116);
#848=LINE('',#4228,#1117);
#849=LINE('',#4230,#1118);
#850=LINE('',#4231,#1119);
#851=LINE('',#4234,#1120);
#852=LINE('',#4236,#1121);
#853=LINE('',#4237,#1122);
#854=LINE('',#4260,#1123);
#855=LINE('',#4263,#1124);
#856=LINE('',#4265,#1125);
#857=LINE('',#4266,#1126);
#858=LINE('',#4269,#1127);
#859=LINE('',#4271,#1128);
#860=LINE('',#4272,#1129);
#861=LINE('',#4275,#1130);
#862=LINE('',#4277,#1131);
#863=LINE('',#4278,#1132);
#864=LINE('',#4281,#1133);
#865=LINE('',#4283,#1134);
#866=LINE('',#4284,#1135);
#867=LINE('',#4287,#1136);
#868=LINE('',#4289,#1137);
#869=LINE('',#4290,#1138);
#870=LINE('',#4293,#1139);
#871=LINE('',#4295,#1140);
#872=LINE('',#4296,#1141);
#873=LINE('',#4299,#1142);
#874=LINE('',#4301,#1143);
#875=LINE('',#4302,#1144);
#876=LINE('',#4305,#1145);
#877=LINE('',#4307,#1146);
#878=LINE('',#4308,#1147);
#879=LINE('',#4310,#1148);
#880=LINE('',#4311,#1149);
#881=LINE('',#4331,#1150);
#882=LINE('',#4338,#1151);
#883=LINE('',#4361,#1152);
#884=LINE('',#4384,#1153);
#885=LINE('',#4407,#1154);
#886=LINE('',#4430,#1155);
#887=LINE('',#4453,#1156);
#888=LINE('',#4476,#1157);
#889=LINE('',#4500,#1158);
#890=LINE('',#4502,#1159);
#891=LINE('',#4504,#1160);
#892=LINE('',#4505,#1161);
#893=LINE('',#4508,#1162);
#894=LINE('',#4510,#1163);
#895=LINE('',#4511,#1164);
#896=LINE('',#4514,#1165);
#897=LINE('',#4516,#1166);
#898=LINE('',#4517,#1167);
#899=LINE('',#4540,#1168);
#900=LINE('',#4563,#1169);
#901=LINE('',#4586,#1170);
#902=LINE('',#4609,#1171);
#903=LINE('',#4632,#1172);
#904=LINE('',#4655,#1173);
#905=LINE('',#4678,#1174);
#906=LINE('',#4701,#1175);
#907=LINE('',#4724,#1176);
#908=LINE('',#4747,#1177);
#909=LINE('',#4750,#1178);
#910=LINE('',#4752,#1179);
#911=LINE('',#4753,#1180);
#912=LINE('',#4755,#1181);
#913=LINE('',#4756,#1182);
#914=LINE('',#4761,#1183);
#915=LINE('',#4763,#1184);
#916=LINE('',#4765,#1185);
#917=LINE('',#4766,#1186);
#918=LINE('',#4769,#1187);
#919=LINE('',#4771,#1188);
#920=LINE('',#4809,#1189);
#921=LINE('',#4811,#1190);
#922=LINE('',#4812,#1191);
#923=LINE('',#4815,#1192);
#924=LINE('',#4871,#1193);
#925=LINE('',#4873,#1194);
#926=LINE('',#4875,#1195);
#927=LINE('',#4876,#1196);
#928=LINE('',#4963,#1197);
#929=LINE('',#5007,#1198);
#930=LINE('',#5021,#1199);
#931=LINE('',#5113,#1200);
#932=LINE('',#5157,#1201);
#933=LINE('',#5171,#1202);
#934=LINE('',#5180,#1203);
#935=LINE('',#5182,#1204);
#936=LINE('',#5184,#1205);
#937=LINE('',#5185,#1206);
#938=LINE('',#5208,#1207);
#939=LINE('',#5231,#1208);
#940=LINE('',#5254,#1209);
#941=LINE('',#5277,#1210);
#942=LINE('',#5300,#1211);
#943=LINE('',#5303,#1212);
#944=LINE('',#5305,#1213);
#945=LINE('',#5306,#1214);
#946=LINE('',#5308,#1215);
#947=LINE('',#5309,#1216);
#948=LINE('',#5312,#1217);
#949=LINE('',#5314,#1218);
#950=LINE('',#5315,#1219);
#951=LINE('',#5318,#1220);
#952=LINE('',#5319,#1221);
#953=LINE('',#5336,#1222);
#954=LINE('',#5353,#1223);
#955=LINE('',#5370,#1224);
#956=LINE('',#5387,#1225);
#957=LINE('',#5404,#1226);
#958=LINE('',#5421,#1227);
#959=LINE('',#5424,#1228);
#960=LINE('',#5425,#1229);
#961=LINE('',#5428,#1230);
#962=LINE('',#5429,#1231);
#963=LINE('',#5431,#1232);
#964=LINE('',#5452,#1233);
#965=LINE('',#5459,#1234);
#966=LINE('',#5482,#1235);
#967=LINE('',#5505,#1236);
#968=LINE('',#5528,#1237);
#969=LINE('',#5551,#1238);
#970=LINE('',#5574,#1239);
#971=LINE('',#5597,#1240);
#972=LINE('',#5620,#1241);
#973=LINE('',#5643,#1242);
#974=LINE('',#5645,#1243);
#975=LINE('',#5646,#1244);
#976=LINE('',#5658,#1245);
#977=LINE('',#5665,#1246);
#978=LINE('',#5682,#1247);
#979=LINE('',#5699,#1248);
#980=LINE('',#5716,#1249);
#981=LINE('',#5733,#1250);
#982=LINE('',#5750,#1251);
#983=LINE('',#5767,#1252);
#984=LINE('',#5784,#1253);
#985=LINE('',#5801,#1254);
#986=LINE('',#5818,#1255);
#987=LINE('',#5835,#1256);
#988=LINE('',#5852,#1257);
#989=LINE('',#5869,#1258);
#990=LINE('',#5886,#1259);
#991=LINE('',#5889,#1260);
#992=LINE('',#5890,#1261);
#993=LINE('',#5907,#1262);
#994=LINE('',#5924,#1263);
#995=LINE('',#5941,#1264);
#996=LINE('',#5958,#1265);
#997=LINE('',#5975,#1266);
#998=LINE('',#5992,#1267);
#999=LINE('',#6009,#1268);
#1000=LINE('',#6012,#1269);
#1001=LINE('',#6013,#1270);
#1002=LINE('',#6030,#1271);
#1003=LINE('',#6047,#1272);
#1004=LINE('',#6050,#1273);
#1005=LINE('',#6051,#1274);
#1006=LINE('',#6087,#1275);
#1007=LINE('',#6094,#1276);
#1008=LINE('',#6117,#1277);
#1009=LINE('',#6140,#1278);
#1010=LINE('',#6163,#1279);
#1011=LINE('',#6186,#1280);
#1012=LINE('',#6209,#1281);
#1013=LINE('',#6232,#1282);
#1014=LINE('',#6255,#1283);
#1015=LINE('',#6257,#1284);
#1016=LINE('',#6258,#1285);
#1017=LINE('',#6275,#1286);
#1018=LINE('',#6292,#1287);
#1019=LINE('',#6309,#1288);
#1020=LINE('',#6326,#1289);
#1021=LINE('',#6343,#1290);
#1022=LINE('',#6360,#1291);
#1023=LINE('',#6377,#1292);
#1024=LINE('',#6394,#1293);
#1025=LINE('',#6411,#1294);
#1026=LINE('',#6414,#1295);
#1027=LINE('',#6415,#1296);
#1028=LINE('',#6418,#1297);
#1029=LINE('',#6419,#1298);
#1030=LINE('',#6422,#1299);
#1031=LINE('',#6423,#1300);
#1032=LINE('',#6425,#1301);
#1033=LINE('',#6446,#1302);
#1034=LINE('',#6453,#1303);
#1035=LINE('',#6476,#1304);
#1036=LINE('',#6499,#1305);
#1037=LINE('',#6522,#1306);
#1038=LINE('',#6545,#1307);
#1039=LINE('',#6568,#1308);
#1040=LINE('',#6591,#1309);
#1041=LINE('',#6614,#1310);
#1042=LINE('',#6637,#1311);
#1043=LINE('',#6639,#1312);
#1044=LINE('',#6640,#1313);
#1045=LINE('',#6652,#1314);
#1046=LINE('',#6659,#1315);
#1047=LINE('',#6676,#1316);
#1048=LINE('',#6693,#1317);
#1049=LINE('',#6710,#1318);
#1050=LINE('',#6727,#1319);
#1051=LINE('',#6744,#1320);
#1052=LINE('',#6761,#1321);
#1053=LINE('',#6778,#1322);
#1054=LINE('',#6795,#1323);
#1055=LINE('',#6812,#1324);
#1056=LINE('',#6829,#1325);
#1057=LINE('',#6846,#1326);
#1058=LINE('',#6863,#1327);
#1059=LINE('',#6880,#1328);
#1060=LINE('',#6883,#1329);
#1061=LINE('',#6884,#1330);
#1062=LINE('',#6901,#1331);
#1063=LINE('',#6918,#1332);
#1064=LINE('',#6935,#1333);
#1065=LINE('',#6952,#1334);
#1066=LINE('',#6969,#1335);
#1067=LINE('',#6986,#1336);
#1068=LINE('',#7003,#1337);
#1069=LINE('',#7006,#1338);
#1070=LINE('',#7007,#1339);
#1071=LINE('',#7024,#1340);
#1072=LINE('',#7041,#1341);
#1073=LINE('',#7044,#1342);
#1074=LINE('',#7045,#1343);
#1075=LINE('',#7068,#1344);
#1076=LINE('',#7070,#1345);
#1077=LINE('',#7072,#1346);
#1078=LINE('',#7074,#1347);
#1079=LINE('',#7076,#1348);
#1080=LINE('',#7078,#1349);
#1081=LINE('',#7080,#1350);
#1082=LINE('',#7084,#1351);
#1083=LINE('',#7088,#1352);
#1084=LINE('',#7093,#1353);
#1085=LINE('',#7096,#1354);
#1086=LINE('',#7107,#1355);
#1087=LINE('',#7109,#1356);
#1088=LINE('',#7113,#1357);
#1089=LINE('',#7114,#1358);
#1090=LINE('',#7117,#1359);
#1091=LINE('',#7118,#1360);
#1092=LINE('',#7121,#1361);
#1093=LINE('',#7122,#1362);
#1094=LINE('',#7126,#1363);
#1095=LINE('',#7137,#1364);
#1096=LINE('',#7138,#1365);
#1097=LINE('',#7143,#1366);
#1098=LINE('',#7145,#1367);
#1099=LINE('',#7149,#1368);
#1100=LINE('',#7155,#1369);
#1101=LINE('',#7158,#1370);
#1102=LINE('',#7161,#1371);
#1103=LINE('',#7164,#1372);
#1104=LINE('',#7165,#1373);
#1105=LINE('',#7169,#1374);
#1106=LINE('',#7174,#1375);
#1107=LINE('',#7180,#1376);
#1108=LINE('',#7186,#1377);
#1109=LINE('',#7192,#1378);
#1110=LINE('',#7195,#1379);
#1111=LINE('',#7197,#1380);
#1112=LINE('',#7201,#1381);
#1113=LINE('',#7206,#1382);
#1114=LINE('',#7211,#1383);
#1115=LINE('',#7216,#1384);
#1116=VECTOR('',#3720,10.);
#1117=VECTOR('',#3721,10.);
#1118=VECTOR('',#3722,10.);
#1119=VECTOR('',#3723,10.);
#1120=VECTOR('',#3726,10.);
#1121=VECTOR('',#3727,10.);
#1122=VECTOR('',#3728,10.);
#1123=VECTOR('',#3729,10.);
#1124=VECTOR('',#3732,10.);
#1125=VECTOR('',#3733,10.);
#1126=VECTOR('',#3734,10.);
#1127=VECTOR('',#3737,10.);
#1128=VECTOR('',#3738,10.);
#1129=VECTOR('',#3739,10.);
#1130=VECTOR('',#3742,10.);
#1131=VECTOR('',#3743,10.);
#1132=VECTOR('',#3744,10.);
#1133=VECTOR('',#3747,10.);
#1134=VECTOR('',#3748,10.);
#1135=VECTOR('',#3749,10.);
#1136=VECTOR('',#3752,10.);
#1137=VECTOR('',#3753,10.);
#1138=VECTOR('',#3754,10.);
#1139=VECTOR('',#3757,10.);
#1140=VECTOR('',#3758,10.);
#1141=VECTOR('',#3759,10.);
#1142=VECTOR('',#3762,10.);
#1143=VECTOR('',#3763,10.);
#1144=VECTOR('',#3764,10.);
#1145=VECTOR('',#3767,10.);
#1146=VECTOR('',#3768,10.);
#1147=VECTOR('',#3769,10.);
#1148=VECTOR('',#3772,10.);
#1149=VECTOR('',#3773,10.);
#1150=VECTOR('',#3776,10.);
#1151=VECTOR('',#3777,10.);
#1152=VECTOR('',#3778,10.);
#1153=VECTOR('',#3779,10.);
#1154=VECTOR('',#3780,10.);
#1155=VECTOR('',#3781,10.);
#1156=VECTOR('',#3782,10.);
#1157=VECTOR('',#3783,10.);
#1158=VECTOR('',#3786,10.);
#1159=VECTOR('',#3787,10.);
#1160=VECTOR('',#3788,10.);
#1161=VECTOR('',#3789,10.);
#1162=VECTOR('',#3792,10.);
#1163=VECTOR('',#3793,10.);
#1164=VECTOR('',#3794,10.);
#1165=VECTOR('',#3797,10.);
#1166=VECTOR('',#3798,10.);
#1167=VECTOR('',#3799,10.);
#1168=VECTOR('',#3800,10.);
#1169=VECTOR('',#3801,10.);
#1170=VECTOR('',#3802,10.);
#1171=VECTOR('',#3803,10.);
#1172=VECTOR('',#3804,10.);
#1173=VECTOR('',#3805,10.);
#1174=VECTOR('',#3806,10.);
#1175=VECTOR('',#3807,10.);
#1176=VECTOR('',#3808,10.);
#1177=VECTOR('',#3809,10.);
#1178=VECTOR('',#3812,10.);
#1179=VECTOR('',#3813,10.);
#1180=VECTOR('',#3814,10.);
#1181=VECTOR('',#3817,10.);
#1182=VECTOR('',#3818,10.);
#1183=VECTOR('',#3823,10.);
#1184=VECTOR('',#3824,10.);
#1185=VECTOR('',#3825,10.);
#1186=VECTOR('',#3826,10.);
#1187=VECTOR('',#3827,10.);
#1188=VECTOR('',#3828,10.);
#1189=VECTOR('',#3829,10.);
#1190=VECTOR('',#3830,10.);
#1191=VECTOR('',#3831,10.);
#1192=VECTOR('',#3832,10.);
#1193=VECTOR('',#3833,10.);
#1194=VECTOR('',#3834,10.);
#1195=VECTOR('',#3835,10.);
#1196=VECTOR('',#3836,10.);
#1197=VECTOR('',#3837,10.);
#1198=VECTOR('',#3838,10.);
#1199=VECTOR('',#3839,10.);
#1200=VECTOR('',#3840,10.);
#1201=VECTOR('',#3841,10.);
#1202=VECTOR('',#3842,10.);
#1203=VECTOR('',#3845,10.);
#1204=VECTOR('',#3846,10.);
#1205=VECTOR('',#3847,10.);
#1206=VECTOR('',#3848,10.);
#1207=VECTOR('',#3849,10.);
#1208=VECTOR('',#3850,10.);
#1209=VECTOR('',#3851,10.);
#1210=VECTOR('',#3852,10.);
#1211=VECTOR('',#3853,10.);
#1212=VECTOR('',#3856,10.);
#1213=VECTOR('',#3857,10.);
#1214=VECTOR('',#3858,10.);
#1215=VECTOR('',#3861,10.);
#1216=VECTOR('',#3862,10.);
#1217=VECTOR('',#3865,10.);
#1218=VECTOR('',#3866,10.);
#1219=VECTOR('',#3867,10.);
#1220=VECTOR('',#3870,10.);
#1221=VECTOR('',#3871,10.);
#1222=VECTOR('',#3872,10.);
#1223=VECTOR('',#3873,10.);
#1224=VECTOR('',#3874,10.);
#1225=VECTOR('',#3875,10.);
#1226=VECTOR('',#3876,10.);
#1227=VECTOR('',#3877,10.);
#1228=VECTOR('',#3880,10.);
#1229=VECTOR('',#3881,10.);
#1230=VECTOR('',#3884,10.);
#1231=VECTOR('',#3885,10.);
#1232=VECTOR('',#3888,10.);
#1233=VECTOR('',#3893,10.);
#1234=VECTOR('',#3894,10.);
#1235=VECTOR('',#3895,10.);
#1236=VECTOR('',#3896,10.);
#1237=VECTOR('',#3897,10.);
#1238=VECTOR('',#3898,10.);
#1239=VECTOR('',#3899,10.);
#1240=VECTOR('',#3900,10.);
#1241=VECTOR('',#3901,10.);
#1242=VECTOR('',#3902,10.);
#1243=VECTOR('',#3905,10.);
#1244=VECTOR('',#3906,10.);
#1245=VECTOR('',#3907,10.);
#1246=VECTOR('',#3908,10.);
#1247=VECTOR('',#3909,10.);
#1248=VECTOR('',#3910,10.);
#1249=VECTOR('',#3911,10.);
#1250=VECTOR('',#3912,10.);
#1251=VECTOR('',#3913,10.);
#1252=VECTOR('',#3914,10.);
#1253=VECTOR('',#3915,10.);
#1254=VECTOR('',#3916,10.);
#1255=VECTOR('',#3917,10.);
#1256=VECTOR('',#3918,10.);
#1257=VECTOR('',#3919,10.);
#1258=VECTOR('',#3920,10.);
#1259=VECTOR('',#3921,10.);
#1260=VECTOR('',#3924,10.);
#1261=VECTOR('',#3925,10.);
#1262=VECTOR('',#3926,10.);
#1263=VECTOR('',#3927,10.);
#1264=VECTOR('',#3928,10.);
#1265=VECTOR('',#3929,10.);
#1266=VECTOR('',#3930,10.);
#1267=VECTOR('',#3931,10.);
#1268=VECTOR('',#3932,10.);
#1269=VECTOR('',#3935,10.);
#1270=VECTOR('',#3936,10.);
#1271=VECTOR('',#3937,10.);
#1272=VECTOR('',#3938,10.);
#1273=VECTOR('',#3941,10.);
#1274=VECTOR('',#3942,10.);
#1275=VECTOR('',#3947,10.);
#1276=VECTOR('',#3948,10.);
#1277=VECTOR('',#3949,10.);
#1278=VECTOR('',#3950,10.);
#1279=VECTOR('',#3951,10.);
#1280=VECTOR('',#3952,10.);
#1281=VECTOR('',#3953,10.);
#1282=VECTOR('',#3954,10.);
#1283=VECTOR('',#3957,10.);
#1284=VECTOR('',#3958,10.);
#1285=VECTOR('',#3959,10.);
#1286=VECTOR('',#3960,10.);
#1287=VECTOR('',#3961,10.);
#1288=VECTOR('',#3962,10.);
#1289=VECTOR('',#3963,10.);
#1290=VECTOR('',#3964,10.);
#1291=VECTOR('',#3965,10.);
#1292=VECTOR('',#3966,10.);
#1293=VECTOR('',#3967,10.);
#1294=VECTOR('',#3968,10.);
#1295=VECTOR('',#3971,10.);
#1296=VECTOR('',#3972,10.);
#1297=VECTOR('',#3975,10.);
#1298=VECTOR('',#3976,10.);
#1299=VECTOR('',#3979,10.);
#1300=VECTOR('',#3980,10.);
#1301=VECTOR('',#3983,10.);
#1302=VECTOR('',#3988,10.);
#1303=VECTOR('',#3989,10.);
#1304=VECTOR('',#3990,10.);
#1305=VECTOR('',#3991,10.);
#1306=VECTOR('',#3992,10.);
#1307=VECTOR('',#3993,10.);
#1308=VECTOR('',#3994,10.);
#1309=VECTOR('',#3995,10.);
#1310=VECTOR('',#3996,10.);
#1311=VECTOR('',#3997,10.);
#1312=VECTOR('',#4000,10.);
#1313=VECTOR('',#4001,10.);
#1314=VECTOR('',#4002,10.);
#1315=VECTOR('',#4003,10.);
#1316=VECTOR('',#4004,10.);
#1317=VECTOR('',#4005,10.);
#1318=VECTOR('',#4006,10.);
#1319=VECTOR('',#4007,10.);
#1320=VECTOR('',#4008,10.);
#1321=VECTOR('',#4009,10.);
#1322=VECTOR('',#4010,10.);
#1323=VECTOR('',#4011,10.);
#1324=VECTOR('',#4012,10.);
#1325=VECTOR('',#4013,10.);
#1326=VECTOR('',#4014,10.);
#1327=VECTOR('',#4015,10.);
#1328=VECTOR('',#4016,10.);
#1329=VECTOR('',#4019,10.);
#1330=VECTOR('',#4020,10.);
#1331=VECTOR('',#4021,10.);
#1332=VECTOR('',#4022,10.);
#1333=VECTOR('',#4023,10.);
#1334=VECTOR('',#4024,10.);
#1335=VECTOR('',#4025,10.);
#1336=VECTOR('',#4026,10.);
#1337=VECTOR('',#4027,10.);
#1338=VECTOR('',#4030,10.);
#1339=VECTOR('',#4031,10.);
#1340=VECTOR('',#4032,10.);
#1341=VECTOR('',#4033,10.);
#1342=VECTOR('',#4036,10.);
#1343=VECTOR('',#4037,10.);
#1344=VECTOR('',#4046,10.);
#1345=VECTOR('',#4047,10.);
#1346=VECTOR('',#4048,10.);
#1347=VECTOR('',#4049,10.);
#1348=VECTOR('',#4050,10.);
#1349=VECTOR('',#4051,10.);
#1350=VECTOR('',#4052,10.);
#1351=VECTOR('',#4055,10.);
#1352=VECTOR('',#4058,10.);
#1353=VECTOR('',#4063,10.);
#1354=VECTOR('',#4066,10.);
#1355=VECTOR('',#4077,10.);
#1356=VECTOR('',#4078,10.);
#1357=VECTOR('',#4083,10.);
#1358=VECTOR('',#4084,10.);
#1359=VECTOR('',#4087,10.);
#1360=VECTOR('',#4088,10.);
#1361=VECTOR('',#4091,10.);
#1362=VECTOR('',#4092,10.);
#1363=VECTOR('',#4097,10.);
#1364=VECTOR('',#4110,10.);
#1365=VECTOR('',#4111,10.);
#1366=VECTOR('',#4120,10.);
#1367=VECTOR('',#4123,10.);
#1368=VECTOR('',#4128,10.);
#1369=VECTOR('',#4137,10.);
#1370=VECTOR('',#4140,10.);
#1371=VECTOR('',#4143,10.);
#1372=VECTOR('',#4146,10.);
#1373=VECTOR('',#4147,10.);
#1374=VECTOR('',#4152,10.);
#1375=VECTOR('',#4157,3.00000000000001);
#1376=VECTOR('',#4164,3.);
#1377=VECTOR('',#4171,3.);
#1378=VECTOR('',#4178,3.);
#1379=VECTOR('',#4183,10.);
#1380=VECTOR('',#4186,10.);
#1381=VECTOR('',#4191,1.7);
#1382=VECTOR('',#4198,1.7);
#1383=VECTOR('',#4205,1.7);
#1384=VECTOR('',#4212,1.7);
#1385=VERTEX_POINT('',#4224);
#1386=VERTEX_POINT('',#4225);
#1387=VERTEX_POINT('',#4227);
#1388=VERTEX_POINT('',#4229);
#1389=VERTEX_POINT('',#4233);
#1390=VERTEX_POINT('',#4235);
#1391=VERTEX_POINT('',#4248);
#1392=VERTEX_POINT('',#4254);
#1393=VERTEX_POINT('',#4262);
#1394=VERTEX_POINT('',#4264);
#1395=VERTEX_POINT('',#4268);
#1396=VERTEX_POINT('',#4270);
#1397=VERTEX_POINT('',#4274);
#1398=VERTEX_POINT('',#4276);
#1399=VERTEX_POINT('',#4280);
#1400=VERTEX_POINT('',#4282);
#1401=VERTEX_POINT('',#4286);
#1402=VERTEX_POINT('',#4288);
#1403=VERTEX_POINT('',#4292);
#1404=VERTEX_POINT('',#4294);
#1405=VERTEX_POINT('',#4298);
#1406=VERTEX_POINT('',#4300);
#1407=VERTEX_POINT('',#4304);
#1408=VERTEX_POINT('',#4306);
#1409=VERTEX_POINT('',#4323);
#1410=VERTEX_POINT('',#4324);
#1411=VERTEX_POINT('',#4330);
#1412=VERTEX_POINT('',#4332);
#1413=VERTEX_POINT('',#4349);
#1414=VERTEX_POINT('',#4355);
#1415=VERTEX_POINT('',#4372);
#1416=VERTEX_POINT('',#4378);
#1417=VERTEX_POINT('',#4395);
#1418=VERTEX_POINT('',#4401);
#1419=VERTEX_POINT('',#4418);
#1420=VERTEX_POINT('',#4424);
#1421=VERTEX_POINT('',#4441);
#1422=VERTEX_POINT('',#4447);
#1423=VERTEX_POINT('',#4464);
#1424=VERTEX_POINT('',#4470);
#1425=VERTEX_POINT('',#4498);
#1426=VERTEX_POINT('',#4499);
#1427=VERTEX_POINT('',#4501);
#1428=VERTEX_POINT('',#4503);
#1429=VERTEX_POINT('',#4507);
#1430=VERTEX_POINT('',#4509);
#1431=VERTEX_POINT('',#4513);
#1432=VERTEX_POINT('',#4515);
#1433=VERTEX_POINT('',#4528);
#1434=VERTEX_POINT('',#4534);
#1435=VERTEX_POINT('',#4551);
#1436=VERTEX_POINT('',#4557);
#1437=VERTEX_POINT('',#4574);
#1438=VERTEX_POINT('',#4580);
#1439=VERTEX_POINT('',#4597);
#1440=VERTEX_POINT('',#4603);
#1441=VERTEX_POINT('',#4620);
#1442=VERTEX_POINT('',#4626);
#1443=VERTEX_POINT('',#4643);
#1444=VERTEX_POINT('',#4649);
#1445=VERTEX_POINT('',#4666);
#1446=VERTEX_POINT('',#4672);
#1447=VERTEX_POINT('',#4689);
#1448=VERTEX_POINT('',#4695);
#1449=VERTEX_POINT('',#4712);
#1450=VERTEX_POINT('',#4718);
#1451=VERTEX_POINT('',#4735);
#1452=VERTEX_POINT('',#4741);
#1453=VERTEX_POINT('',#4749);
#1454=VERTEX_POINT('',#4751);
#1455=VERTEX_POINT('',#4759);
#1456=VERTEX_POINT('',#4760);
#1457=VERTEX_POINT('',#4762);
#1458=VERTEX_POINT('',#4764);
#1459=VERTEX_POINT('',#4767);
#1460=VERTEX_POINT('',#4768);
#1461=VERTEX_POINT('',#4770);
#1462=VERTEX_POINT('',#4772);
#1463=VERTEX_POINT('',#4778);
#1464=VERTEX_POINT('',#4784);
#1465=VERTEX_POINT('',#4790);
#1466=VERTEX_POINT('',#4796);
#1467=VERTEX_POINT('',#4802);
#1468=VERTEX_POINT('',#4808);
#1469=VERTEX_POINT('',#4810);
#1470=VERTEX_POINT('',#4813);
#1471=VERTEX_POINT('',#4814);
#1472=VERTEX_POINT('',#4816);
#1473=VERTEX_POINT('',#4822);
#1474=VERTEX_POINT('',#4828);
#1475=VERTEX_POINT('',#4834);
#1476=VERTEX_POINT('',#4840);
#1477=VERTEX_POINT('',#4846);
#1478=VERTEX_POINT('',#4852);
#1479=VERTEX_POINT('',#4858);
#1480=VERTEX_POINT('',#4864);
#1481=VERTEX_POINT('',#4870);
#1482=VERTEX_POINT('',#4872);
#1483=VERTEX_POINT('',#4874);
#1484=VERTEX_POINT('',#4877);
#1485=VERTEX_POINT('',#4878);
#1486=VERTEX_POINT('',#4884);
#1487=VERTEX_POINT('',#4890);
#1488=VERTEX_POINT('',#4896);
#1489=VERTEX_POINT('',#4902);
#1490=VERTEX_POINT('',#4908);
#1491=VERTEX_POINT('',#4914);
#1492=VERTEX_POINT('',#4920);
#1493=VERTEX_POINT('',#4926);
#1494=VERTEX_POINT('',#4932);
#1495=VERTEX_POINT('',#4938);
#1496=VERTEX_POINT('',#4944);
#1497=VERTEX_POINT('',#4950);
#1498=VERTEX_POINT('',#4956);
#1499=VERTEX_POINT('',#4962);
#1500=VERTEX_POINT('',#4964);
#1501=VERTEX_POINT('',#4970);
#1502=VERTEX_POINT('',#4976);
#1503=VERTEX_POINT('',#4982);
#1504=VERTEX_POINT('',#4988);
#1505=VERTEX_POINT('',#4994);
#1506=VERTEX_POINT('',#5000);
#1507=VERTEX_POINT('',#5006);
#1508=VERTEX_POINT('',#5008);
#1509=VERTEX_POINT('',#5014);
#1510=VERTEX_POINT('',#5020);
#1511=VERTEX_POINT('',#5027);
#1512=VERTEX_POINT('',#5028);
#1513=VERTEX_POINT('',#5034);
#1514=VERTEX_POINT('',#5040);
#1515=VERTEX_POINT('',#5046);
#1516=VERTEX_POINT('',#5052);
#1517=VERTEX_POINT('',#5058);
#1518=VERTEX_POINT('',#5064);
#1519=VERTEX_POINT('',#5070);
#1520=VERTEX_POINT('',#5076);
#1521=VERTEX_POINT('',#5082);
#1522=VERTEX_POINT('',#5088);
#1523=VERTEX_POINT('',#5094);
#1524=VERTEX_POINT('',#5100);
#1525=VERTEX_POINT('',#5106);
#1526=VERTEX_POINT('',#5112);
#1527=VERTEX_POINT('',#5114);
#1528=VERTEX_POINT('',#5120);
#1529=VERTEX_POINT('',#5126);
#1530=VERTEX_POINT('',#5132);
#1531=VERTEX_POINT('',#5138);
#1532=VERTEX_POINT('',#5144);
#1533=VERTEX_POINT('',#5150);
#1534=VERTEX_POINT('',#5156);
#1535=VERTEX_POINT('',#5158);
#1536=VERTEX_POINT('',#5164);
#1537=VERTEX_POINT('',#5170);
#1538=VERTEX_POINT('',#5178);
#1539=VERTEX_POINT('',#5179);
#1540=VERTEX_POINT('',#5181);
#1541=VERTEX_POINT('',#5183);
#1542=VERTEX_POINT('',#5196);
#1543=VERTEX_POINT('',#5202);
#1544=VERTEX_POINT('',#5219);
#1545=VERTEX_POINT('',#5225);
#1546=VERTEX_POINT('',#5242);
#1547=VERTEX_POINT('',#5248);
#1548=VERTEX_POINT('',#5265);
#1549=VERTEX_POINT('',#5271);
#1550=VERTEX_POINT('',#5288);
#1551=VERTEX_POINT('',#5294);
#1552=VERTEX_POINT('',#5302);
#1553=VERTEX_POINT('',#5304);
#1554=VERTEX_POINT('',#5311);
#1555=VERTEX_POINT('',#5313);
#1556=VERTEX_POINT('',#5317);
#1557=VERTEX_POINT('',#5330);
#1558=VERTEX_POINT('',#5347);
#1559=VERTEX_POINT('',#5364);
#1560=VERTEX_POINT('',#5381);
#1561=VERTEX_POINT('',#5398);
#1562=VERTEX_POINT('',#5415);
#1563=VERTEX_POINT('',#5423);
#1564=VERTEX_POINT('',#5427);
#1565=VERTEX_POINT('',#5444);
#1566=VERTEX_POINT('',#5445);
#1567=VERTEX_POINT('',#5451);
#1568=VERTEX_POINT('',#5453);
#1569=VERTEX_POINT('',#5470);
#1570=VERTEX_POINT('',#5476);
#1571=VERTEX_POINT('',#5493);
#1572=VERTEX_POINT('',#5499);
#1573=VERTEX_POINT('',#5516);
#1574=VERTEX_POINT('',#5522);
#1575=VERTEX_POINT('',#5539);
#1576=VERTEX_POINT('',#5545);
#1577=VERTEX_POINT('',#5562);
#1578=VERTEX_POINT('',#5568);
#1579=VERTEX_POINT('',#5585);
#1580=VERTEX_POINT('',#5591);
#1581=VERTEX_POINT('',#5608);
#1582=VERTEX_POINT('',#5614);
#1583=VERTEX_POINT('',#5631);
#1584=VERTEX_POINT('',#5637);
#1585=VERTEX_POINT('',#5657);
#1586=VERTEX_POINT('',#5659);
#1587=VERTEX_POINT('',#5676);
#1588=VERTEX_POINT('',#5693);
#1589=VERTEX_POINT('',#5710);
#1590=VERTEX_POINT('',#5727);
#1591=VERTEX_POINT('',#5744);
#1592=VERTEX_POINT('',#5761);
#1593=VERTEX_POINT('',#5778);
#1594=VERTEX_POINT('',#5795);
#1595=VERTEX_POINT('',#5812);
#1596=VERTEX_POINT('',#5829);
#1597=VERTEX_POINT('',#5846);
#1598=VERTEX_POINT('',#5863);
#1599=VERTEX_POINT('',#5880);
#1600=VERTEX_POINT('',#5888);
#1601=VERTEX_POINT('',#5901);
#1602=VERTEX_POINT('',#5918);
#1603=VERTEX_POINT('',#5935);
#1604=VERTEX_POINT('',#5952);
#1605=VERTEX_POINT('',#5969);
#1606=VERTEX_POINT('',#5986);
#1607=VERTEX_POINT('',#6003);
#1608=VERTEX_POINT('',#6011);
#1609=VERTEX_POINT('',#6024);
#1610=VERTEX_POINT('',#6041);
#1611=VERTEX_POINT('',#6049);
#1612=VERTEX_POINT('',#6079);
#1613=VERTEX_POINT('',#6080);
#1614=VERTEX_POINT('',#6086);
#1615=VERTEX_POINT('',#6088);
#1616=VERTEX_POINT('',#6105);
#1617=VERTEX_POINT('',#6111);
#1618=VERTEX_POINT('',#6128);
#1619=VERTEX_POINT('',#6134);
#1620=VERTEX_POINT('',#6151);
#1621=VERTEX_POINT('',#6157);
#1622=VERTEX_POINT('',#6174);
#1623=VERTEX_POINT('',#6180);
#1624=VERTEX_POINT('',#6197);
#1625=VERTEX_POINT('',#6203);
#1626=VERTEX_POINT('',#6220);
#1627=VERTEX_POINT('',#6226);
#1628=VERTEX_POINT('',#6254);
#1629=VERTEX_POINT('',#6256);
#1630=VERTEX_POINT('',#6269);
#1631=VERTEX_POINT('',#6286);
#1632=VERTEX_POINT('',#6303);
#1633=VERTEX_POINT('',#6320);
#1634=VERTEX_POINT('',#6337);
#1635=VERTEX_POINT('',#6354);
#1636=VERTEX_POINT('',#6371);
#1637=VERTEX_POINT('',#6388);
#1638=VERTEX_POINT('',#6405);
#1639=VERTEX_POINT('',#6413);
#1640=VERTEX_POINT('',#6417);
#1641=VERTEX_POINT('',#6421);
#1642=VERTEX_POINT('',#6438);
#1643=VERTEX_POINT('',#6439);
#1644=VERTEX_POINT('',#6445);
#1645=VERTEX_POINT('',#6447);
#1646=VERTEX_POINT('',#6464);
#1647=VERTEX_POINT('',#6470);
#1648=VERTEX_POINT('',#6487);
#1649=VERTEX_POINT('',#6493);
#1650=VERTEX_POINT('',#6510);
#1651=VERTEX_POINT('',#6516);
#1652=VERTEX_POINT('',#6533);
#1653=VERTEX_POINT('',#6539);
#1654=VERTEX_POINT('',#6556);
#1655=VERTEX_POINT('',#6562);
#1656=VERTEX_POINT('',#6579);
#1657=VERTEX_POINT('',#6585);
#1658=VERTEX_POINT('',#6602);
#1659=VERTEX_POINT('',#6608);
#1660=VERTEX_POINT('',#6625);
#1661=VERTEX_POINT('',#6631);
#1662=VERTEX_POINT('',#6651);
#1663=VERTEX_POINT('',#6653);
#1664=VERTEX_POINT('',#6670);
#1665=VERTEX_POINT('',#6687);
#1666=VERTEX_POINT('',#6704);
#1667=VERTEX_POINT('',#6721);
#1668=VERTEX_POINT('',#6738);
#1669=VERTEX_POINT('',#6755);
#1670=VERTEX_POINT('',#6772);
#1671=VERTEX_POINT('',#6789);
#1672=VERTEX_POINT('',#6806);
#1673=VERTEX_POINT('',#6823);
#1674=VERTEX_POINT('',#6840);
#1675=VERTEX_POINT('',#6857);
#1676=VERTEX_POINT('',#6874);
#1677=VERTEX_POINT('',#6882);
#1678=VERTEX_POINT('',#6895);
#1679=VERTEX_POINT('',#6912);
#1680=VERTEX_POINT('',#6929);
#1681=VERTEX_POINT('',#6946);
#1682=VERTEX_POINT('',#6963);
#1683=VERTEX_POINT('',#6980);
#1684=VERTEX_POINT('',#6997);
#1685=VERTEX_POINT('',#7005);
#1686=VERTEX_POINT('',#7018);
#1687=VERTEX_POINT('',#7035);
#1688=VERTEX_POINT('',#7043);
#1689=VERTEX_POINT('',#7064);
#1690=VERTEX_POINT('',#7065);
#1691=VERTEX_POINT('',#7067);
#1692=VERTEX_POINT('',#7069);
#1693=VERTEX_POINT('',#7071);
#1694=VERTEX_POINT('',#7073);
#1695=VERTEX_POINT('',#7075);
#1696=VERTEX_POINT('',#7077);
#1697=VERTEX_POINT('',#7079);
#1698=VERTEX_POINT('',#7081);
#1699=VERTEX_POINT('',#7083);
#1700=VERTEX_POINT('',#7085);
#1701=VERTEX_POINT('',#7087);
#1702=VERTEX_POINT('',#7090);
#1703=VERTEX_POINT('',#7092);
#1704=VERTEX_POINT('',#7094);
#1705=VERTEX_POINT('',#7097);
#1706=VERTEX_POINT('',#7099);
#1707=VERTEX_POINT('',#7101);
#1708=VERTEX_POINT('',#7103);
#1709=VERTEX_POINT('',#7106);
#1710=VERTEX_POINT('',#7108);
#1711=VERTEX_POINT('',#7112);
#1712=VERTEX_POINT('',#7116);
#1713=VERTEX_POINT('',#7120);
#1714=VERTEX_POINT('',#7124);
#1715=VERTEX_POINT('',#7128);
#1716=VERTEX_POINT('',#7129);
#1717=VERTEX_POINT('',#7134);
#1718=VERTEX_POINT('',#7135);
#1719=VERTEX_POINT('',#7148);
#1720=VERTEX_POINT('',#7153);
#1721=VERTEX_POINT('',#7157);
#1722=VERTEX_POINT('',#7159);
#1723=VERTEX_POINT('',#7163);
#1724=VERTEX_POINT('',#7167);
#1725=VERTEX_POINT('',#7171);
#1726=VERTEX_POINT('',#7173);
#1727=VERTEX_POINT('',#7177);
#1728=VERTEX_POINT('',#7179);
#1729=VERTEX_POINT('',#7183);
#1730=VERTEX_POINT('',#7185);
#1731=VERTEX_POINT('',#7189);
#1732=VERTEX_POINT('',#7191);
#1733=VERTEX_POINT('',#7200);
#1734=VERTEX_POINT('',#7205);
#1735=VERTEX_POINT('',#7210);
#1736=VERTEX_POINT('',#7215);
#1737=EDGE_CURVE('',#1385,#1386,#847,.T.);
#1738=EDGE_CURVE('',#1385,#1387,#848,.T.);
#1739=EDGE_CURVE('',#1388,#1387,#849,.T.);
#1740=EDGE_CURVE('',#1386,#1388,#850,.T.);
#1741=EDGE_CURVE('',#1386,#1389,#851,.T.);
#1742=EDGE_CURVE('',#1390,#1388,#852,.T.);
#1743=EDGE_CURVE('',#1389,#1390,#853,.T.);
#1744=EDGE_CURVE('',#1389,#1391,#97,.T.);
#1745=EDGE_CURVE('',#1392,#1390,#98,.T.);
#1746=EDGE_CURVE('',#1391,#1392,#854,.T.);
#1747=EDGE_CURVE('',#1391,#1393,#855,.T.);
#1748=EDGE_CURVE('',#1394,#1392,#856,.T.);
#1749=EDGE_CURVE('',#1393,#1394,#857,.T.);
#1750=EDGE_CURVE('',#1393,#1395,#858,.T.);
#1751=EDGE_CURVE('',#1396,#1394,#859,.T.);
#1752=EDGE_CURVE('',#1395,#1396,#860,.T.);
#1753=EDGE_CURVE('',#1395,#1397,#861,.T.);
#1754=EDGE_CURVE('',#1398,#1396,#862,.T.);
#1755=EDGE_CURVE('',#1397,#1398,#863,.T.);
#1756=EDGE_CURVE('',#1397,#1399,#864,.T.);
#1757=EDGE_CURVE('',#1400,#1398,#865,.T.);
#1758=EDGE_CURVE('',#1399,#1400,#866,.T.);
#1759=EDGE_CURVE('',#1399,#1401,#867,.T.);
#1760=EDGE_CURVE('',#1402,#1400,#868,.T.);
#1761=EDGE_CURVE('',#1401,#1402,#869,.T.);
#1762=EDGE_CURVE('',#1401,#1403,#870,.T.);
#1763=EDGE_CURVE('',#1404,#1402,#871,.T.);
#1764=EDGE_CURVE('',#1403,#1404,#872,.T.);
#1765=EDGE_CURVE('',#1403,#1405,#873,.T.);
#1766=EDGE_CURVE('',#1406,#1404,#874,.T.);
#1767=EDGE_CURVE('',#1405,#1406,#875,.T.);
#1768=EDGE_CURVE('',#1405,#1407,#876,.T.);
#1769=EDGE_CURVE('',#1408,#1406,#877,.T.);
#1770=EDGE_CURVE('',#1407,#1408,#878,.T.);
#1771=EDGE_CURVE('',#1407,#1385,#879,.T.);
#1772=EDGE_CURVE('',#1387,#1408,#880,.T.);
#1773=EDGE_CURVE('',#1409,#1410,#99,.T.);
#1774=EDGE_CURVE('',#1409,#1411,#881,.T.);
#1775=EDGE_CURVE('',#1412,#1411,#100,.T.);
#1776=EDGE_CURVE('',#1410,#1412,#882,.T.);
#1777=EDGE_CURVE('',#1410,#1413,#101,.T.);
#1778=EDGE_CURVE('',#1414,#1412,#102,.T.);
#1779=EDGE_CURVE('',#1413,#1414,#883,.T.);
#1780=EDGE_CURVE('',#1413,#1415,#103,.T.);
#1781=EDGE_CURVE('',#1416,#1414,#104,.T.);
#1782=EDGE_CURVE('',#1415,#1416,#884,.T.);
#1783=EDGE_CURVE('',#1415,#1417,#105,.T.);
#1784=EDGE_CURVE('',#1418,#1416,#106,.T.);
#1785=EDGE_CURVE('',#1417,#1418,#885,.T.);
#1786=EDGE_CURVE('',#1417,#1419,#107,.T.);
#1787=EDGE_CURVE('',#1420,#1418,#108,.T.);
#1788=EDGE_CURVE('',#1419,#1420,#886,.T.);
#1789=EDGE_CURVE('',#1419,#1421,#109,.T.);
#1790=EDGE_CURVE('',#1422,#1420,#110,.T.);
#1791=EDGE_CURVE('',#1421,#1422,#887,.T.);
#1792=EDGE_CURVE('',#1421,#1423,#111,.T.);
#1793=EDGE_CURVE('',#1424,#1422,#112,.T.);
#1794=EDGE_CURVE('',#1423,#1424,#888,.T.);
#1795=EDGE_CURVE('',#1423,#1409,#113,.T.);
#1796=EDGE_CURVE('',#1411,#1424,#114,.T.);
#1797=EDGE_CURVE('',#1425,#1426,#889,.T.);
#1798=EDGE_CURVE('',#1425,#1427,#890,.T.);
#1799=EDGE_CURVE('',#1428,#1427,#891,.T.);
#1800=EDGE_CURVE('',#1426,#1428,#892,.T.);
#1801=EDGE_CURVE('',#1426,#1429,#893,.T.);
#1802=EDGE_CURVE('',#1430,#1428,#894,.T.);
#1803=EDGE_CURVE('',#1429,#1430,#895,.T.);
#1804=EDGE_CURVE('',#1429,#1431,#896,.T.);
#1805=EDGE_CURVE('',#1432,#1430,#897,.T.);
#1806=EDGE_CURVE('',#1431,#1432,#898,.T.);
#1807=EDGE_CURVE('',#1431,#1433,#115,.T.);
#1808=EDGE_CURVE('',#1434,#1432,#116,.T.);
#1809=EDGE_CURVE('',#1433,#1434,#899,.T.);
#1810=EDGE_CURVE('',#1433,#1435,#117,.T.);
#1811=EDGE_CURVE('',#1436,#1434,#118,.T.);
#1812=EDGE_CURVE('',#1435,#1436,#900,.T.);
#1813=EDGE_CURVE('',#1435,#1437,#119,.T.);
#1814=EDGE_CURVE('',#1438,#1436,#120,.T.);
#1815=EDGE_CURVE('',#1437,#1438,#901,.T.);
#1816=EDGE_CURVE('',#1437,#1439,#121,.T.);
#1817=EDGE_CURVE('',#1440,#1438,#122,.T.);
#1818=EDGE_CURVE('',#1439,#1440,#902,.T.);
#1819=EDGE_CURVE('',#1439,#1441,#123,.T.);
#1820=EDGE_CURVE('',#1442,#1440,#124,.T.);
#1821=EDGE_CURVE('',#1441,#1442,#903,.T.);
#1822=EDGE_CURVE('',#1441,#1443,#125,.T.);
#1823=EDGE_CURVE('',#1444,#1442,#126,.T.);
#1824=EDGE_CURVE('',#1443,#1444,#904,.T.);
#1825=EDGE_CURVE('',#1443,#1445,#127,.T.);
#1826=EDGE_CURVE('',#1446,#1444,#128,.T.);
#1827=EDGE_CURVE('',#1445,#1446,#905,.T.);
#1828=EDGE_CURVE('',#1445,#1447,#129,.T.);
#1829=EDGE_CURVE('',#1448,#1446,#130,.T.);
#1830=EDGE_CURVE('',#1447,#1448,#906,.T.);
#1831=EDGE_CURVE('',#1447,#1449,#131,.T.);
#1832=EDGE_CURVE('',#1450,#1448,#132,.T.);
#1833=EDGE_CURVE('',#1449,#1450,#907,.T.);
#1834=EDGE_CURVE('',#1449,#1451,#133,.T.);
#1835=EDGE_CURVE('',#1452,#1450,#134,.T.);
#1836=EDGE_CURVE('',#1451,#1452,#908,.T.);
#1837=EDGE_CURVE('',#1451,#1453,#909,.T.);
#1838=EDGE_CURVE('',#1454,#1452,#910,.T.);
#1839=EDGE_CURVE('',#1453,#1454,#911,.T.);
#1840=EDGE_CURVE('',#1453,#1425,#912,.T.);
#1841=EDGE_CURVE('',#1427,#1454,#913,.T.);
#1842=EDGE_CURVE('',#1455,#1456,#914,.T.);
#1843=EDGE_CURVE('',#1457,#1456,#915,.T.);
#1844=EDGE_CURVE('',#1458,#1457,#916,.T.);
#1845=EDGE_CURVE('',#1455,#1458,#917,.T.);
#1846=EDGE_CURVE('',#1459,#1460,#918,.T.);
#1847=EDGE_CURVE('',#1460,#1461,#919,.T.);
#1848=EDGE_CURVE('',#1461,#1462,#135,.T.);
#1849=EDGE_CURVE('',#1462,#1463,#136,.T.);
#1850=EDGE_CURVE('',#1463,#1464,#137,.T.);
#1851=EDGE_CURVE('',#1464,#1465,#138,.T.);
#1852=EDGE_CURVE('',#1465,#1466,#139,.T.);
#1853=EDGE_CURVE('',#1466,#1467,#140,.T.);
#1854=EDGE_CURVE('',#1467,#1468,#920,.T.);
#1855=EDGE_CURVE('',#1468,#1469,#921,.T.);
#1856=EDGE_CURVE('',#1469,#1459,#922,.T.);
#1857=EDGE_CURVE('',#1470,#1471,#923,.T.);
#1858=EDGE_CURVE('',#1471,#1472,#141,.T.);
#1859=EDGE_CURVE('',#1472,#1473,#142,.T.);
#1860=EDGE_CURVE('',#1473,#1474,#143,.T.);
#1861=EDGE_CURVE('',#1474,#1475,#144,.T.);
#1862=EDGE_CURVE('',#1475,#1476,#145,.T.);
#1863=EDGE_CURVE('',#1476,#1477,#146,.T.);
#1864=EDGE_CURVE('',#1477,#1478,#147,.T.);
#1865=EDGE_CURVE('',#1478,#1479,#148,.T.);
#1866=EDGE_CURVE('',#1479,#1480,#149,.T.);
#1867=EDGE_CURVE('',#1480,#1481,#924,.T.);
#1868=EDGE_CURVE('',#1481,#1482,#925,.T.);
#1869=EDGE_CURVE('',#1482,#1483,#926,.T.);
#1870=EDGE_CURVE('',#1483,#1470,#927,.T.);
#1871=EDGE_CURVE('',#1484,#1485,#150,.T.);
#1872=EDGE_CURVE('',#1485,#1486,#151,.T.);
#1873=EDGE_CURVE('',#1486,#1487,#152,.T.);
#1874=EDGE_CURVE('',#1487,#1488,#153,.T.);
#1875=EDGE_CURVE('',#1488,#1489,#154,.T.);
#1876=EDGE_CURVE('',#1489,#1490,#155,.T.);
#1877=EDGE_CURVE('',#1490,#1491,#156,.T.);
#1878=EDGE_CURVE('',#1491,#1492,#157,.T.);
#1879=EDGE_CURVE('',#1492,#1493,#158,.T.);
#1880=EDGE_CURVE('',#1493,#1494,#159,.T.);
#1881=EDGE_CURVE('',#1494,#1495,#160,.T.);
#1882=EDGE_CURVE('',#1495,#1496,#161,.T.);
#1883=EDGE_CURVE('',#1496,#1497,#162,.T.);
#1884=EDGE_CURVE('',#1497,#1498,#163,.T.);
#1885=EDGE_CURVE('',#1498,#1499,#928,.T.);
#1886=EDGE_CURVE('',#1499,#1500,#164,.T.);
#1887=EDGE_CURVE('',#1500,#1501,#165,.T.);
#1888=EDGE_CURVE('',#1501,#1502,#166,.T.);
#1889=EDGE_CURVE('',#1502,#1503,#167,.T.);
#1890=EDGE_CURVE('',#1503,#1504,#168,.T.);
#1891=EDGE_CURVE('',#1504,#1505,#169,.T.);
#1892=EDGE_CURVE('',#1505,#1506,#170,.T.);
#1893=EDGE_CURVE('',#1506,#1507,#929,.T.);
#1894=EDGE_CURVE('',#1507,#1508,#171,.T.);
#1895=EDGE_CURVE('',#1508,#1509,#172,.T.);
#1896=EDGE_CURVE('',#1509,#1510,#930,.T.);
#1897=EDGE_CURVE('',#1510,#1484,#173,.T.);
#1898=EDGE_CURVE('',#1511,#1512,#174,.T.);
#1899=EDGE_CURVE('',#1512,#1513,#175,.T.);
#1900=EDGE_CURVE('',#1513,#1514,#176,.T.);
#1901=EDGE_CURVE('',#1514,#1515,#177,.T.);
#1902=EDGE_CURVE('',#1515,#1516,#178,.T.);
#1903=EDGE_CURVE('',#1516,#1517,#179,.T.);
#1904=EDGE_CURVE('',#1517,#1518,#180,.T.);
#1905=EDGE_CURVE('',#1518,#1519,#181,.T.);
#1906=EDGE_CURVE('',#1519,#1520,#182,.T.);
#1907=EDGE_CURVE('',#1520,#1521,#183,.T.);
#1908=EDGE_CURVE('',#1521,#1522,#184,.T.);
#1909=EDGE_CURVE('',#1522,#1523,#185,.T.);
#1910=EDGE_CURVE('',#1523,#1524,#186,.T.);
#1911=EDGE_CURVE('',#1524,#1525,#187,.T.);
#1912=EDGE_CURVE('',#1525,#1526,#931,.T.);
#1913=EDGE_CURVE('',#1526,#1527,#188,.T.);
#1914=EDGE_CURVE('',#1527,#1528,#189,.T.);
#1915=EDGE_CURVE('',#1528,#1529,#190,.T.);
#1916=EDGE_CURVE('',#1529,#1530,#191,.T.);
#1917=EDGE_CURVE('',#1530,#1531,#192,.T.);
#1918=EDGE_CURVE('',#1531,#1532,#193,.T.);
#1919=EDGE_CURVE('',#1532,#1533,#194,.T.);
#1920=EDGE_CURVE('',#1533,#1534,#932,.T.);
#1921=EDGE_CURVE('',#1534,#1535,#195,.T.);
#1922=EDGE_CURVE('',#1535,#1536,#196,.T.);
#1923=EDGE_CURVE('',#1536,#1537,#933,.T.);
#1924=EDGE_CURVE('',#1537,#1511,#197,.T.);
#1925=EDGE_CURVE('',#1538,#1539,#934,.T.);
#1926=EDGE_CURVE('',#1538,#1540,#935,.T.);
#1927=EDGE_CURVE('',#1541,#1540,#936,.T.);
#1928=EDGE_CURVE('',#1539,#1541,#937,.T.);
#1929=EDGE_CURVE('',#1539,#1542,#198,.T.);
#1930=EDGE_CURVE('',#1543,#1541,#199,.T.);
#1931=EDGE_CURVE('',#1542,#1543,#938,.T.);
#1932=EDGE_CURVE('',#1542,#1544,#200,.T.);
#1933=EDGE_CURVE('',#1545,#1543,#201,.T.);
#1934=EDGE_CURVE('',#1544,#1545,#939,.T.);
#1935=EDGE_CURVE('',#1544,#1546,#202,.T.);
#1936=EDGE_CURVE('',#1547,#1545,#203,.T.);
#1937=EDGE_CURVE('',#1546,#1547,#940,.T.);
#1938=EDGE_CURVE('',#1546,#1548,#204,.T.);
#1939=EDGE_CURVE('',#1549,#1547,#205,.T.);
#1940=EDGE_CURVE('',#1548,#1549,#941,.T.);
#1941=EDGE_CURVE('',#1548,#1550,#206,.T.);
#1942=EDGE_CURVE('',#1551,#1549,#207,.T.);
#1943=EDGE_CURVE('',#1550,#1551,#942,.T.);
#1944=EDGE_CURVE('',#1550,#1552,#943,.T.);
#1945=EDGE_CURVE('',#1553,#1551,#944,.T.);
#1946=EDGE_CURVE('',#1552,#1553,#945,.T.);
#1947=EDGE_CURVE('',#1552,#1538,#946,.T.);
#1948=EDGE_CURVE('',#1540,#1553,#947,.T.);
#1949=EDGE_CURVE('',#1459,#1554,#948,.T.);
#1950=EDGE_CURVE('',#1555,#1554,#949,.T.);
#1951=EDGE_CURVE('',#1460,#1555,#950,.T.);
#1952=EDGE_CURVE('',#1556,#1555,#951,.T.);
#1953=EDGE_CURVE('',#1461,#1556,#952,.T.);
#1954=EDGE_CURVE('',#1557,#1556,#208,.T.);
#1955=EDGE_CURVE('',#1462,#1557,#953,.T.);
#1956=EDGE_CURVE('',#1558,#1557,#209,.T.);
#1957=EDGE_CURVE('',#1463,#1558,#954,.T.);
#1958=EDGE_CURVE('',#1559,#1558,#210,.T.);
#1959=EDGE_CURVE('',#1464,#1559,#955,.T.);
#1960=EDGE_CURVE('',#1560,#1559,#211,.T.);
#1961=EDGE_CURVE('',#1465,#1560,#956,.T.);
#1962=EDGE_CURVE('',#1561,#1560,#212,.T.);
#1963=EDGE_CURVE('',#1466,#1561,#957,.T.);
#1964=EDGE_CURVE('',#1562,#1561,#213,.T.);
#1965=EDGE_CURVE('',#1467,#1562,#958,.T.);
#1966=EDGE_CURVE('',#1563,#1562,#959,.T.);
#1967=EDGE_CURVE('',#1468,#1563,#960,.T.);
#1968=EDGE_CURVE('',#1564,#1563,#961,.T.);
#1969=EDGE_CURVE('',#1469,#1564,#962,.T.);
#1970=EDGE_CURVE('',#1554,#1564,#963,.T.);
#1971=EDGE_CURVE('',#1565,#1566,#214,.T.);
#1972=EDGE_CURVE('',#1565,#1567,#964,.T.);
#1973=EDGE_CURVE('',#1568,#1567,#215,.T.);
#1974=EDGE_CURVE('',#1566,#1568,#965,.T.);
#1975=EDGE_CURVE('',#1566,#1569,#216,.T.);
#1976=EDGE_CURVE('',#1570,#1568,#217,.T.);
#1977=EDGE_CURVE('',#1569,#1570,#966,.T.);
#1978=EDGE_CURVE('',#1569,#1571,#218,.T.);
#1979=EDGE_CURVE('',#1572,#1570,#219,.T.);
#1980=EDGE_CURVE('',#1571,#1572,#967,.T.);
#1981=EDGE_CURVE('',#1571,#1573,#220,.T.);
#1982=EDGE_CURVE('',#1574,#1572,#221,.T.);
#1983=EDGE_CURVE('',#1573,#1574,#968,.T.);
#1984=EDGE_CURVE('',#1573,#1575,#222,.T.);
#1985=EDGE_CURVE('',#1576,#1574,#223,.T.);
#1986=EDGE_CURVE('',#1575,#1576,#969,.T.);
#1987=EDGE_CURVE('',#1575,#1577,#224,.T.);
#1988=EDGE_CURVE('',#1578,#1576,#225,.T.);
#1989=EDGE_CURVE('',#1577,#1578,#970,.T.);
#1990=EDGE_CURVE('',#1577,#1579,#226,.T.);
#1991=EDGE_CURVE('',#1580,#1578,#227,.T.);
#1992=EDGE_CURVE('',#1579,#1580,#971,.T.);
#1993=EDGE_CURVE('',#1579,#1581,#228,.T.);
#1994=EDGE_CURVE('',#1582,#1580,#229,.T.);
#1995=EDGE_CURVE('',#1581,#1582,#972,.T.);
#1996=EDGE_CURVE('',#1581,#1583,#230,.T.);
#1997=EDGE_CURVE('',#1584,#1582,#231,.T.);
#1998=EDGE_CURVE('',#1583,#1584,#973,.T.);
#1999=EDGE_CURVE('',#1583,#1565,#974,.T.);
#2000=EDGE_CURVE('',#1567,#1584,#975,.T.);
#2001=EDGE_CURVE('',#1511,#1585,#976,.T.);
#2002=EDGE_CURVE('',#1586,#1585,#232,.T.);
#2003=EDGE_CURVE('',#1512,#1586,#977,.T.);
#2004=EDGE_CURVE('',#1587,#1586,#233,.T.);
#2005=EDGE_CURVE('',#1513,#1587,#978,.T.);
#2006=EDGE_CURVE('',#1588,#1587,#234,.T.);
#2007=EDGE_CURVE('',#1514,#1588,#979,.T.);
#2008=EDGE_CURVE('',#1589,#1588,#235,.T.);
#2009=EDGE_CURVE('',#1515,#1589,#980,.T.);
#2010=EDGE_CURVE('',#1590,#1589,#236,.T.);
#2011=EDGE_CURVE('',#1516,#1590,#981,.T.);
#2012=EDGE_CURVE('',#1591,#1590,#237,.T.);
#2013=EDGE_CURVE('',#1517,#1591,#982,.T.);
#2014=EDGE_CURVE('',#1592,#1591,#238,.T.);
#2015=EDGE_CURVE('',#1518,#1592,#983,.T.);
#2016=EDGE_CURVE('',#1593,#1592,#239,.T.);
#2017=EDGE_CURVE('',#1519,#1593,#984,.T.);
#2018=EDGE_CURVE('',#1594,#1593,#240,.T.);
#2019=EDGE_CURVE('',#1520,#1594,#985,.T.);
#2020=EDGE_CURVE('',#1595,#1594,#241,.T.);
#2021=EDGE_CURVE('',#1521,#1595,#986,.T.);
#2022=EDGE_CURVE('',#1596,#1595,#242,.T.);
#2023=EDGE_CURVE('',#1522,#1596,#987,.T.);
#2024=EDGE_CURVE('',#1597,#1596,#243,.T.);
#2025=EDGE_CURVE('',#1523,#1597,#988,.T.);
#2026=EDGE_CURVE('',#1598,#1597,#244,.T.);
#2027=EDGE_CURVE('',#1524,#1598,#989,.T.);
#2028=EDGE_CURVE('',#1599,#1598,#245,.T.);
#2029=EDGE_CURVE('',#1525,#1599,#990,.T.);
#2030=EDGE_CURVE('',#1600,#1599,#991,.T.);
#2031=EDGE_CURVE('',#1526,#1600,#992,.T.);
#2032=EDGE_CURVE('',#1601,#1600,#246,.T.);
#2033=EDGE_CURVE('',#1527,#1601,#993,.T.);
#2034=EDGE_CURVE('',#1602,#1601,#247,.T.);
#2035=EDGE_CURVE('',#1528,#1602,#994,.T.);
#2036=EDGE_CURVE('',#1603,#1602,#248,.T.);
#2037=EDGE_CURVE('',#1529,#1603,#995,.T.);
#2038=EDGE_CURVE('',#1604,#1603,#249,.T.);
#2039=EDGE_CURVE('',#1530,#1604,#996,.T.);
#2040=EDGE_CURVE('',#1605,#1604,#250,.T.);
#2041=EDGE_CURVE('',#1531,#1605,#997,.T.);
#2042=EDGE_CURVE('',#1606,#1605,#251,.T.);
#2043=EDGE_CURVE('',#1532,#1606,#998,.T.);
#2044=EDGE_CURVE('',#1607,#1606,#252,.T.);
#2045=EDGE_CURVE('',#1533,#1607,#999,.T.);
#2046=EDGE_CURVE('',#1608,#1607,#1000,.T.);
#2047=EDGE_CURVE('',#1534,#1608,#1001,.T.);
#2048=EDGE_CURVE('',#1609,#1608,#253,.T.);
#2049=EDGE_CURVE('',#1535,#1609,#1002,.T.);
#2050=EDGE_CURVE('',#1610,#1609,#254,.T.);
#2051=EDGE_CURVE('',#1536,#1610,#1003,.T.);
#2052=EDGE_CURVE('',#1611,#1610,#1004,.T.);
#2053=EDGE_CURVE('',#1537,#1611,#1005,.T.);
#2054=EDGE_CURVE('',#1585,#1611,#255,.T.);
#2055=EDGE_CURVE('',#1612,#1613,#256,.T.);
#2056=EDGE_CURVE('',#1612,#1614,#1006,.T.);
#2057=EDGE_CURVE('',#1615,#1614,#257,.T.);
#2058=EDGE_CURVE('',#1613,#1615,#1007,.T.);
#2059=EDGE_CURVE('',#1613,#1616,#258,.T.);
#2060=EDGE_CURVE('',#1617,#1615,#259,.T.);
#2061=EDGE_CURVE('',#1616,#1617,#1008,.T.);
#2062=EDGE_CURVE('',#1616,#1618,#260,.T.);
#2063=EDGE_CURVE('',#1619,#1617,#261,.T.);
#2064=EDGE_CURVE('',#1618,#1619,#1009,.T.);
#2065=EDGE_CURVE('',#1618,#1620,#262,.T.);
#2066=EDGE_CURVE('',#1621,#1619,#263,.T.);
#2067=EDGE_CURVE('',#1620,#1621,#1010,.T.);
#2068=EDGE_CURVE('',#1620,#1622,#264,.T.);
#2069=EDGE_CURVE('',#1623,#1621,#265,.T.);
#2070=EDGE_CURVE('',#1622,#1623,#1011,.T.);
#2071=EDGE_CURVE('',#1622,#1624,#266,.T.);
#2072=EDGE_CURVE('',#1625,#1623,#267,.T.);
#2073=EDGE_CURVE('',#1624,#1625,#1012,.T.);
#2074=EDGE_CURVE('',#1624,#1626,#268,.T.);
#2075=EDGE_CURVE('',#1627,#1625,#269,.T.);
#2076=EDGE_CURVE('',#1626,#1627,#1013,.T.);
#2077=EDGE_CURVE('',#1626,#1612,#270,.T.);
#2078=EDGE_CURVE('',#1614,#1627,#271,.T.);
#2079=EDGE_CURVE('',#1470,#1628,#1014,.T.);
#2080=EDGE_CURVE('',#1629,#1628,#1015,.T.);
#2081=EDGE_CURVE('',#1471,#1629,#1016,.T.);
#2082=EDGE_CURVE('',#1630,#1629,#272,.T.);
#2083=EDGE_CURVE('',#1472,#1630,#1017,.T.);
#2084=EDGE_CURVE('',#1631,#1630,#273,.T.);
#2085=EDGE_CURVE('',#1473,#1631,#1018,.T.);
#2086=EDGE_CURVE('',#1632,#1631,#274,.T.);
#2087=EDGE_CURVE('',#1474,#1632,#1019,.T.);
#2088=EDGE_CURVE('',#1633,#1632,#275,.T.);
#2089=EDGE_CURVE('',#1475,#1633,#1020,.T.);
#2090=EDGE_CURVE('',#1634,#1633,#276,.T.);
#2091=EDGE_CURVE('',#1476,#1634,#1021,.T.);
#2092=EDGE_CURVE('',#1635,#1634,#277,.T.);
#2093=EDGE_CURVE('',#1477,#1635,#1022,.T.);
#2094=EDGE_CURVE('',#1636,#1635,#278,.T.);
#2095=EDGE_CURVE('',#1478,#1636,#1023,.T.);
#2096=EDGE_CURVE('',#1637,#1636,#279,.T.);
#2097=EDGE_CURVE('',#1479,#1637,#1024,.T.);
#2098=EDGE_CURVE('',#1638,#1637,#280,.T.);
#2099=EDGE_CURVE('',#1480,#1638,#1025,.T.);
#2100=EDGE_CURVE('',#1639,#1638,#1026,.T.);
#2101=EDGE_CURVE('',#1481,#1639,#1027,.T.);
#2102=EDGE_CURVE('',#1640,#1639,#1028,.T.);
#2103=EDGE_CURVE('',#1482,#1640,#1029,.T.);
#2104=EDGE_CURVE('',#1641,#1640,#1030,.T.);
#2105=EDGE_CURVE('',#1483,#1641,#1031,.T.);
#2106=EDGE_CURVE('',#1628,#1641,#1032,.T.);
#2107=EDGE_CURVE('',#1642,#1643,#281,.T.);
#2108=EDGE_CURVE('',#1642,#1644,#1033,.T.);
#2109=EDGE_CURVE('',#1645,#1644,#282,.T.);
#2110=EDGE_CURVE('',#1643,#1645,#1034,.T.);
#2111=EDGE_CURVE('',#1643,#1646,#283,.T.);
#2112=EDGE_CURVE('',#1647,#1645,#284,.T.);
#2113=EDGE_CURVE('',#1646,#1647,#1035,.T.);
#2114=EDGE_CURVE('',#1646,#1648,#285,.T.);
#2115=EDGE_CURVE('',#1649,#1647,#286,.T.);
#2116=EDGE_CURVE('',#1648,#1649,#1036,.T.);
#2117=EDGE_CURVE('',#1648,#1650,#287,.T.);
#2118=EDGE_CURVE('',#1651,#1649,#288,.T.);
#2119=EDGE_CURVE('',#1650,#1651,#1037,.T.);
#2120=EDGE_CURVE('',#1650,#1652,#289,.T.);
#2121=EDGE_CURVE('',#1653,#1651,#290,.T.);
#2122=EDGE_CURVE('',#1652,#1653,#1038,.T.);
#2123=EDGE_CURVE('',#1652,#1654,#291,.T.);
#2124=EDGE_CURVE('',#1655,#1653,#292,.T.);
#2125=EDGE_CURVE('',#1654,#1655,#1039,.T.);
#2126=EDGE_CURVE('',#1654,#1656,#293,.T.);
#2127=EDGE_CURVE('',#1657,#1655,#294,.T.);
#2128=EDGE_CURVE('',#1656,#1657,#1040,.T.);
#2129=EDGE_CURVE('',#1656,#1658,#295,.T.);
#2130=EDGE_CURVE('',#1659,#1657,#296,.T.);
#2131=EDGE_CURVE('',#1658,#1659,#1041,.T.);
#2132=EDGE_CURVE('',#1658,#1660,#297,.T.);
#2133=EDGE_CURVE('',#1661,#1659,#298,.T.);
#2134=EDGE_CURVE('',#1660,#1661,#1042,.T.);
#2135=EDGE_CURVE('',#1660,#1642,#1043,.T.);
#2136=EDGE_CURVE('',#1644,#1661,#1044,.T.);
#2137=EDGE_CURVE('',#1484,#1662,#1045,.T.);
#2138=EDGE_CURVE('',#1663,#1662,#299,.T.);
#2139=EDGE_CURVE('',#1485,#1663,#1046,.T.);
#2140=EDGE_CURVE('',#1664,#1663,#300,.T.);
#2141=EDGE_CURVE('',#1486,#1664,#1047,.T.);
#2142=EDGE_CURVE('',#1665,#1664,#301,.T.);
#2143=EDGE_CURVE('',#1487,#1665,#1048,.T.);
#2144=EDGE_CURVE('',#1666,#1665,#302,.T.);
#2145=EDGE_CURVE('',#1488,#1666,#1049,.T.);
#2146=EDGE_CURVE('',#1667,#1666,#303,.T.);
#2147=EDGE_CURVE('',#1489,#1667,#1050,.T.);
#2148=EDGE_CURVE('',#1668,#1667,#304,.T.);
#2149=EDGE_CURVE('',#1490,#1668,#1051,.T.);
#2150=EDGE_CURVE('',#1669,#1668,#305,.T.);
#2151=EDGE_CURVE('',#1491,#1669,#1052,.T.);
#2152=EDGE_CURVE('',#1670,#1669,#306,.T.);
#2153=EDGE_CURVE('',#1492,#1670,#1053,.T.);
#2154=EDGE_CURVE('',#1671,#1670,#307,.T.);
#2155=EDGE_CURVE('',#1493,#1671,#1054,.T.);
#2156=EDGE_CURVE('',#1672,#1671,#308,.T.);
#2157=EDGE_CURVE('',#1494,#1672,#1055,.T.);
#2158=EDGE_CURVE('',#1673,#1672,#309,.T.);
#2159=EDGE_CURVE('',#1495,#1673,#1056,.T.);
#2160=EDGE_CURVE('',#1674,#1673,#310,.T.);
#2161=EDGE_CURVE('',#1496,#1674,#1057,.T.);
#2162=EDGE_CURVE('',#1675,#1674,#311,.T.);
#2163=EDGE_CURVE('',#1497,#1675,#1058,.T.);
#2164=EDGE_CURVE('',#1676,#1675,#312,.T.);
#2165=EDGE_CURVE('',#1498,#1676,#1059,.T.);
#2166=EDGE_CURVE('',#1677,#1676,#1060,.T.);
#2167=EDGE_CURVE('',#1499,#1677,#1061,.T.);
#2168=EDGE_CURVE('',#1678,#1677,#313,.T.);
#2169=EDGE_CURVE('',#1500,#1678,#1062,.T.);
#2170=EDGE_CURVE('',#1679,#1678,#314,.T.);
#2171=EDGE_CURVE('',#1501,#1679,#1063,.T.);
#2172=EDGE_CURVE('',#1680,#1679,#315,.T.);
#2173=EDGE_CURVE('',#1502,#1680,#1064,.T.);
#2174=EDGE_CURVE('',#1681,#1680,#316,.T.);
#2175=EDGE_CURVE('',#1503,#1681,#1065,.T.);
#2176=EDGE_CURVE('',#1682,#1681,#317,.T.);
#2177=EDGE_CURVE('',#1504,#1682,#1066,.T.);
#2178=EDGE_CURVE('',#1683,#1682,#318,.T.);
#2179=EDGE_CURVE('',#1505,#1683,#1067,.T.);
#2180=EDGE_CURVE('',#1684,#1683,#319,.T.);
#2181=EDGE_CURVE('',#1506,#1684,#1068,.T.);
#2182=EDGE_CURVE('',#1685,#1684,#1069,.T.);
#2183=EDGE_CURVE('',#1507,#1685,#1070,.T.);
#2184=EDGE_CURVE('',#1686,#1685,#320,.T.);
#2185=EDGE_CURVE('',#1508,#1686,#1071,.T.);
#2186=EDGE_CURVE('',#1687,#1686,#321,.T.);
#2187=EDGE_CURVE('',#1509,#1687,#1072,.T.);
#2188=EDGE_CURVE('',#1688,#1687,#1073,.T.);
#2189=EDGE_CURVE('',#1510,#1688,#1074,.T.);
#2190=EDGE_CURVE('',#1662,#1688,#322,.T.);
#2191=EDGE_CURVE('',#1689,#1690,#40,.T.);
#2192=EDGE_CURVE('',#1691,#1689,#1075,.T.);
#2193=EDGE_CURVE('',#1692,#1691,#1076,.T.);
#2194=EDGE_CURVE('',#1693,#1692,#1077,.T.);
#2195=EDGE_CURVE('',#1694,#1693,#1078,.T.);
#2196=EDGE_CURVE('',#1695,#1694,#1079,.T.);
#2197=EDGE_CURVE('',#1696,#1695,#1080,.T.);
#2198=EDGE_CURVE('',#1697,#1696,#1081,.T.);
#2199=EDGE_CURVE('',#1697,#1698,#41,.T.);
#2200=EDGE_CURVE('',#1699,#1698,#1082,.T.);
#2201=EDGE_CURVE('',#1700,#1699,#42,.T.);
#2202=EDGE_CURVE('',#1701,#1700,#1083,.T.);
#2203=EDGE_CURVE('',#1456,#1701,#43,.T.);
#2204=EDGE_CURVE('',#1702,#1455,#44,.T.);
#2205=EDGE_CURVE('',#1703,#1702,#1084,.T.);
#2206=EDGE_CURVE('',#1704,#1703,#45,.T.);
#2207=EDGE_CURVE('',#1690,#1704,#1085,.T.);
#2208=EDGE_CURVE('',#1705,#1705,#46,.T.);
#2209=EDGE_CURVE('',#1706,#1706,#47,.T.);
#2210=EDGE_CURVE('',#1707,#1707,#48,.T.);
#2211=EDGE_CURVE('',#1708,#1708,#49,.T.);
#2212=EDGE_CURVE('',#1709,#1695,#1086,.T.);
#2213=EDGE_CURVE('',#1709,#1710,#1087,.T.);
#2214=EDGE_CURVE('',#1696,#1710,#50,.T.);
#2215=EDGE_CURVE('',#1711,#1694,#1088,.T.);
#2216=EDGE_CURVE('',#1711,#1709,#1089,.T.);
#2217=EDGE_CURVE('',#1712,#1693,#1090,.T.);
#2218=EDGE_CURVE('',#1712,#1711,#1091,.T.);
#2219=EDGE_CURVE('',#1713,#1692,#1092,.T.);
#2220=EDGE_CURVE('',#1713,#1712,#1093,.T.);
#2221=EDGE_CURVE('',#1714,#1691,#51,.T.);
#2222=EDGE_CURVE('',#1714,#1713,#1094,.T.);
#2223=EDGE_CURVE('',#1715,#1716,#52,.T.);
#2224=EDGE_CURVE('',#1716,#1698,#53,.T.);
#2225=EDGE_CURVE('',#1697,#1715,#54,.T.);
#2226=EDGE_CURVE('',#1717,#1718,#55,.T.);
#2227=EDGE_CURVE('',#1718,#1716,#1095,.T.);
#2228=EDGE_CURVE('',#1715,#1717,#1096,.T.);
#2229=EDGE_CURVE('',#1690,#1718,#56,.T.);
#2230=EDGE_CURVE('',#1717,#1689,#57,.T.);
#2231=EDGE_CURVE('',#1717,#1714,#1097,.T.);
#2232=EDGE_CURVE('',#1710,#1715,#1098,.T.);
#2233=EDGE_CURVE('',#1701,#1719,#1099,.T.);
#2234=EDGE_CURVE('',#1719,#1457,#58,.T.);
#2235=EDGE_CURVE('',#1458,#1720,#59,.T.);
#2236=EDGE_CURVE('',#1720,#1702,#1100,.T.);
#2237=EDGE_CURVE('',#1703,#1721,#1101,.T.);
#2238=EDGE_CURVE('',#1721,#1722,#60,.T.);
#2239=EDGE_CURVE('',#1722,#1704,#1102,.T.);
#2240=EDGE_CURVE('',#1723,#1722,#1103,.T.);
#2241=EDGE_CURVE('',#1699,#1723,#1104,.T.);
#2242=EDGE_CURVE('',#1723,#1724,#61,.T.);
#2243=EDGE_CURVE('',#1724,#1700,#1105,.T.);
#2244=EDGE_CURVE('',#1725,#1725,#62,.T.);
#2245=EDGE_CURVE('',#1725,#1726,#1106,.T.);
#2246=EDGE_CURVE('',#1726,#1726,#63,.T.);
#2247=EDGE_CURVE('',#1727,#1727,#64,.T.);
#2248=EDGE_CURVE('',#1727,#1728,#1107,.T.);
#2249=EDGE_CURVE('',#1728,#1728,#65,.T.);
#2250=EDGE_CURVE('',#1729,#1729,#66,.T.);
#2251=EDGE_CURVE('',#1729,#1730,#1108,.T.);
#2252=EDGE_CURVE('',#1730,#1730,#67,.T.);
#2253=EDGE_CURVE('',#1731,#1731,#68,.T.);
#2254=EDGE_CURVE('',#1731,#1732,#1109,.T.);
#2255=EDGE_CURVE('',#1732,#1732,#69,.T.);
#2256=EDGE_CURVE('',#1719,#1724,#1110,.T.);
#2257=EDGE_CURVE('',#1721,#1720,#1111,.T.);
#2258=EDGE_CURVE('',#1707,#1733,#1112,.T.);
#2259=EDGE_CURVE('',#1733,#1733,#70,.T.);
#2260=EDGE_CURVE('',#1706,#1734,#1113,.T.);
#2261=EDGE_CURVE('',#1734,#1734,#71,.T.);
#2262=EDGE_CURVE('',#1708,#1735,#1114,.T.);
#2263=EDGE_CURVE('',#1735,#1735,#72,.T.);
#2264=EDGE_CURVE('',#1705,#1736,#1115,.T.);
#2265=EDGE_CURVE('',#1736,#1736,#73,.T.);
#2266=ORIENTED_EDGE('',*,*,#1737,.F.);
#2267=ORIENTED_EDGE('',*,*,#1738,.T.);
#2268=ORIENTED_EDGE('',*,*,#1739,.F.);
#2269=ORIENTED_EDGE('',*,*,#1740,.F.);
#2270=ORIENTED_EDGE('',*,*,#1741,.F.);
#2271=ORIENTED_EDGE('',*,*,#1740,.T.);
#2272=ORIENTED_EDGE('',*,*,#1742,.F.);
#2273=ORIENTED_EDGE('',*,*,#1743,.F.);
#2274=ORIENTED_EDGE('',*,*,#1744,.F.);
#2275=ORIENTED_EDGE('',*,*,#1743,.T.);
#2276=ORIENTED_EDGE('',*,*,#1745,.F.);
#2277=ORIENTED_EDGE('',*,*,#1746,.F.);
#2278=ORIENTED_EDGE('',*,*,#1747,.F.);
#2279=ORIENTED_EDGE('',*,*,#1746,.T.);
#2280=ORIENTED_EDGE('',*,*,#1748,.F.);
#2281=ORIENTED_EDGE('',*,*,#1749,.F.);
#2282=ORIENTED_EDGE('',*,*,#1750,.F.);
#2283=ORIENTED_EDGE('',*,*,#1749,.T.);
#2284=ORIENTED_EDGE('',*,*,#1751,.F.);
#2285=ORIENTED_EDGE('',*,*,#1752,.F.);
#2286=ORIENTED_EDGE('',*,*,#1753,.F.);
#2287=ORIENTED_EDGE('',*,*,#1752,.T.);
#2288=ORIENTED_EDGE('',*,*,#1754,.F.);
#2289=ORIENTED_EDGE('',*,*,#1755,.F.);
#2290=ORIENTED_EDGE('',*,*,#1756,.F.);
#2291=ORIENTED_EDGE('',*,*,#1755,.T.);
#2292=ORIENTED_EDGE('',*,*,#1757,.F.);
#2293=ORIENTED_EDGE('',*,*,#1758,.F.);
#2294=ORIENTED_EDGE('',*,*,#1759,.F.);
#2295=ORIENTED_EDGE('',*,*,#1758,.T.);
#2296=ORIENTED_EDGE('',*,*,#1760,.F.);
#2297=ORIENTED_EDGE('',*,*,#1761,.F.);
#2298=ORIENTED_EDGE('',*,*,#1762,.F.);
#2299=ORIENTED_EDGE('',*,*,#1761,.T.);
#2300=ORIENTED_EDGE('',*,*,#1763,.F.);
#2301=ORIENTED_EDGE('',*,*,#1764,.F.);
#2302=ORIENTED_EDGE('',*,*,#1765,.F.);
#2303=ORIENTED_EDGE('',*,*,#1764,.T.);
#2304=ORIENTED_EDGE('',*,*,#1766,.F.);
#2305=ORIENTED_EDGE('',*,*,#1767,.F.);
#2306=ORIENTED_EDGE('',*,*,#1768,.F.);
#2307=ORIENTED_EDGE('',*,*,#1767,.T.);
#2308=ORIENTED_EDGE('',*,*,#1769,.F.);
#2309=ORIENTED_EDGE('',*,*,#1770,.F.);
#2310=ORIENTED_EDGE('',*,*,#1771,.F.);
#2311=ORIENTED_EDGE('',*,*,#1770,.T.);
#2312=ORIENTED_EDGE('',*,*,#1772,.F.);
#2313=ORIENTED_EDGE('',*,*,#1738,.F.);
#2314=ORIENTED_EDGE('',*,*,#1772,.T.);
#2315=ORIENTED_EDGE('',*,*,#1769,.T.);
#2316=ORIENTED_EDGE('',*,*,#1766,.T.);
#2317=ORIENTED_EDGE('',*,*,#1763,.T.);
#2318=ORIENTED_EDGE('',*,*,#1760,.T.);
#2319=ORIENTED_EDGE('',*,*,#1757,.T.);
#2320=ORIENTED_EDGE('',*,*,#1754,.T.);
#2321=ORIENTED_EDGE('',*,*,#1751,.T.);
#2322=ORIENTED_EDGE('',*,*,#1748,.T.);
#2323=ORIENTED_EDGE('',*,*,#1745,.T.);
#2324=ORIENTED_EDGE('',*,*,#1742,.T.);
#2325=ORIENTED_EDGE('',*,*,#1739,.T.);
#2326=ORIENTED_EDGE('',*,*,#1773,.F.);
#2327=ORIENTED_EDGE('',*,*,#1774,.T.);
#2328=ORIENTED_EDGE('',*,*,#1775,.F.);
#2329=ORIENTED_EDGE('',*,*,#1776,.F.);
#2330=ORIENTED_EDGE('',*,*,#1777,.F.);
#2331=ORIENTED_EDGE('',*,*,#1776,.T.);
#2332=ORIENTED_EDGE('',*,*,#1778,.F.);
#2333=ORIENTED_EDGE('',*,*,#1779,.F.);
#2334=ORIENTED_EDGE('',*,*,#1780,.F.);
#2335=ORIENTED_EDGE('',*,*,#1779,.T.);
#2336=ORIENTED_EDGE('',*,*,#1781,.F.);
#2337=ORIENTED_EDGE('',*,*,#1782,.F.);
#2338=ORIENTED_EDGE('',*,*,#1783,.F.);
#2339=ORIENTED_EDGE('',*,*,#1782,.T.);
#2340=ORIENTED_EDGE('',*,*,#1784,.F.);
#2341=ORIENTED_EDGE('',*,*,#1785,.F.);
#2342=ORIENTED_EDGE('',*,*,#1786,.F.);
#2343=ORIENTED_EDGE('',*,*,#1785,.T.);
#2344=ORIENTED_EDGE('',*,*,#1787,.F.);
#2345=ORIENTED_EDGE('',*,*,#1788,.F.);
#2346=ORIENTED_EDGE('',*,*,#1789,.F.);
#2347=ORIENTED_EDGE('',*,*,#1788,.T.);
#2348=ORIENTED_EDGE('',*,*,#1790,.F.);
#2349=ORIENTED_EDGE('',*,*,#1791,.F.);
#2350=ORIENTED_EDGE('',*,*,#1792,.F.);
#2351=ORIENTED_EDGE('',*,*,#1791,.T.);
#2352=ORIENTED_EDGE('',*,*,#1793,.F.);
#2353=ORIENTED_EDGE('',*,*,#1794,.F.);
#2354=ORIENTED_EDGE('',*,*,#1795,.F.);
#2355=ORIENTED_EDGE('',*,*,#1794,.T.);
#2356=ORIENTED_EDGE('',*,*,#1796,.F.);
#2357=ORIENTED_EDGE('',*,*,#1774,.F.);
#2358=ORIENTED_EDGE('',*,*,#1797,.F.);
#2359=ORIENTED_EDGE('',*,*,#1798,.T.);
#2360=ORIENTED_EDGE('',*,*,#1799,.F.);
#2361=ORIENTED_EDGE('',*,*,#1800,.F.);
#2362=ORIENTED_EDGE('',*,*,#1801,.F.);
#2363=ORIENTED_EDGE('',*,*,#1800,.T.);
#2364=ORIENTED_EDGE('',*,*,#1802,.F.);
#2365=ORIENTED_EDGE('',*,*,#1803,.F.);
#2366=ORIENTED_EDGE('',*,*,#1804,.F.);
#2367=ORIENTED_EDGE('',*,*,#1803,.T.);
#2368=ORIENTED_EDGE('',*,*,#1805,.F.);
#2369=ORIENTED_EDGE('',*,*,#1806,.F.);
#2370=ORIENTED_EDGE('',*,*,#1807,.F.);
#2371=ORIENTED_EDGE('',*,*,#1806,.T.);
#2372=ORIENTED_EDGE('',*,*,#1808,.F.);
#2373=ORIENTED_EDGE('',*,*,#1809,.F.);
#2374=ORIENTED_EDGE('',*,*,#1810,.F.);
#2375=ORIENTED_EDGE('',*,*,#1809,.T.);
#2376=ORIENTED_EDGE('',*,*,#1811,.F.);
#2377=ORIENTED_EDGE('',*,*,#1812,.F.);
#2378=ORIENTED_EDGE('',*,*,#1813,.F.);
#2379=ORIENTED_EDGE('',*,*,#1812,.T.);
#2380=ORIENTED_EDGE('',*,*,#1814,.F.);
#2381=ORIENTED_EDGE('',*,*,#1815,.F.);
#2382=ORIENTED_EDGE('',*,*,#1816,.F.);
#2383=ORIENTED_EDGE('',*,*,#1815,.T.);
#2384=ORIENTED_EDGE('',*,*,#1817,.F.);
#2385=ORIENTED_EDGE('',*,*,#1818,.F.);
#2386=ORIENTED_EDGE('',*,*,#1819,.F.);
#2387=ORIENTED_EDGE('',*,*,#1818,.T.);
#2388=ORIENTED_EDGE('',*,*,#1820,.F.);
#2389=ORIENTED_EDGE('',*,*,#1821,.F.);
#2390=ORIENTED_EDGE('',*,*,#1822,.F.);
#2391=ORIENTED_EDGE('',*,*,#1821,.T.);
#2392=ORIENTED_EDGE('',*,*,#1823,.F.);
#2393=ORIENTED_EDGE('',*,*,#1824,.F.);
#2394=ORIENTED_EDGE('',*,*,#1825,.F.);
#2395=ORIENTED_EDGE('',*,*,#1824,.T.);
#2396=ORIENTED_EDGE('',*,*,#1826,.F.);
#2397=ORIENTED_EDGE('',*,*,#1827,.F.);
#2398=ORIENTED_EDGE('',*,*,#1828,.F.);
#2399=ORIENTED_EDGE('',*,*,#1827,.T.);
#2400=ORIENTED_EDGE('',*,*,#1829,.F.);
#2401=ORIENTED_EDGE('',*,*,#1830,.F.);
#2402=ORIENTED_EDGE('',*,*,#1831,.F.);
#2403=ORIENTED_EDGE('',*,*,#1830,.T.);
#2404=ORIENTED_EDGE('',*,*,#1832,.F.);
#2405=ORIENTED_EDGE('',*,*,#1833,.F.);
#2406=ORIENTED_EDGE('',*,*,#1834,.F.);
#2407=ORIENTED_EDGE('',*,*,#1833,.T.);
#2408=ORIENTED_EDGE('',*,*,#1835,.F.);
#2409=ORIENTED_EDGE('',*,*,#1836,.F.);
#2410=ORIENTED_EDGE('',*,*,#1837,.F.);
#2411=ORIENTED_EDGE('',*,*,#1836,.T.);
#2412=ORIENTED_EDGE('',*,*,#1838,.F.);
#2413=ORIENTED_EDGE('',*,*,#1839,.F.);
#2414=ORIENTED_EDGE('',*,*,#1840,.F.);
#2415=ORIENTED_EDGE('',*,*,#1839,.T.);
#2416=ORIENTED_EDGE('',*,*,#1841,.F.);
#2417=ORIENTED_EDGE('',*,*,#1798,.F.);
#2418=ORIENTED_EDGE('',*,*,#1841,.T.);
#2419=ORIENTED_EDGE('',*,*,#1838,.T.);
#2420=ORIENTED_EDGE('',*,*,#1835,.T.);
#2421=ORIENTED_EDGE('',*,*,#1832,.T.);
#2422=ORIENTED_EDGE('',*,*,#1829,.T.);
#2423=ORIENTED_EDGE('',*,*,#1826,.T.);
#2424=ORIENTED_EDGE('',*,*,#1823,.T.);
#2425=ORIENTED_EDGE('',*,*,#1820,.T.);
#2426=ORIENTED_EDGE('',*,*,#1817,.T.);
#2427=ORIENTED_EDGE('',*,*,#1814,.T.);
#2428=ORIENTED_EDGE('',*,*,#1811,.T.);
#2429=ORIENTED_EDGE('',*,*,#1808,.T.);
#2430=ORIENTED_EDGE('',*,*,#1805,.T.);
#2431=ORIENTED_EDGE('',*,*,#1802,.T.);
#2432=ORIENTED_EDGE('',*,*,#1799,.T.);
#2433=ORIENTED_EDGE('',*,*,#1796,.T.);
#2434=ORIENTED_EDGE('',*,*,#1793,.T.);
#2435=ORIENTED_EDGE('',*,*,#1790,.T.);
#2436=ORIENTED_EDGE('',*,*,#1787,.T.);
#2437=ORIENTED_EDGE('',*,*,#1784,.T.);
#2438=ORIENTED_EDGE('',*,*,#1781,.T.);
#2439=ORIENTED_EDGE('',*,*,#1778,.T.);
#2440=ORIENTED_EDGE('',*,*,#1775,.T.);
#2441=ORIENTED_EDGE('',*,*,#1842,.T.);
#2442=ORIENTED_EDGE('',*,*,#1843,.F.);
#2443=ORIENTED_EDGE('',*,*,#1844,.F.);
#2444=ORIENTED_EDGE('',*,*,#1845,.F.);
#2445=ORIENTED_EDGE('',*,*,#1737,.T.);
#2446=ORIENTED_EDGE('',*,*,#1741,.T.);
#2447=ORIENTED_EDGE('',*,*,#1744,.T.);
#2448=ORIENTED_EDGE('',*,*,#1747,.T.);
#2449=ORIENTED_EDGE('',*,*,#1750,.T.);
#2450=ORIENTED_EDGE('',*,*,#1753,.T.);
#2451=ORIENTED_EDGE('',*,*,#1756,.T.);
#2452=ORIENTED_EDGE('',*,*,#1759,.T.);
#2453=ORIENTED_EDGE('',*,*,#1762,.T.);
#2454=ORIENTED_EDGE('',*,*,#1765,.T.);
#2455=ORIENTED_EDGE('',*,*,#1768,.T.);
#2456=ORIENTED_EDGE('',*,*,#1771,.T.);
#2457=ORIENTED_EDGE('',*,*,#1846,.T.);
#2458=ORIENTED_EDGE('',*,*,#1847,.T.);
#2459=ORIENTED_EDGE('',*,*,#1848,.T.);
#2460=ORIENTED_EDGE('',*,*,#1849,.T.);
#2461=ORIENTED_EDGE('',*,*,#1850,.T.);
#2462=ORIENTED_EDGE('',*,*,#1851,.T.);
#2463=ORIENTED_EDGE('',*,*,#1852,.T.);
#2464=ORIENTED_EDGE('',*,*,#1853,.T.);
#2465=ORIENTED_EDGE('',*,*,#1854,.T.);
#2466=ORIENTED_EDGE('',*,*,#1855,.T.);
#2467=ORIENTED_EDGE('',*,*,#1856,.T.);
#2468=ORIENTED_EDGE('',*,*,#1857,.T.);
#2469=ORIENTED_EDGE('',*,*,#1858,.T.);
#2470=ORIENTED_EDGE('',*,*,#1859,.T.);
#2471=ORIENTED_EDGE('',*,*,#1860,.T.);
#2472=ORIENTED_EDGE('',*,*,#1861,.T.);
#2473=ORIENTED_EDGE('',*,*,#1862,.T.);
#2474=ORIENTED_EDGE('',*,*,#1863,.T.);
#2475=ORIENTED_EDGE('',*,*,#1864,.T.);
#2476=ORIENTED_EDGE('',*,*,#1865,.T.);
#2477=ORIENTED_EDGE('',*,*,#1866,.T.);
#2478=ORIENTED_EDGE('',*,*,#1867,.T.);
#2479=ORIENTED_EDGE('',*,*,#1868,.T.);
#2480=ORIENTED_EDGE('',*,*,#1869,.T.);
#2481=ORIENTED_EDGE('',*,*,#1870,.T.);
#2482=ORIENTED_EDGE('',*,*,#1871,.T.);
#2483=ORIENTED_EDGE('',*,*,#1872,.T.);
#2484=ORIENTED_EDGE('',*,*,#1873,.T.);
#2485=ORIENTED_EDGE('',*,*,#1874,.T.);
#2486=ORIENTED_EDGE('',*,*,#1875,.T.);
#2487=ORIENTED_EDGE('',*,*,#1876,.T.);
#2488=ORIENTED_EDGE('',*,*,#1877,.T.);
#2489=ORIENTED_EDGE('',*,*,#1878,.T.);
#2490=ORIENTED_EDGE('',*,*,#1879,.T.);
#2491=ORIENTED_EDGE('',*,*,#1880,.T.);
#2492=ORIENTED_EDGE('',*,*,#1881,.T.);
#2493=ORIENTED_EDGE('',*,*,#1882,.T.);
#2494=ORIENTED_EDGE('',*,*,#1883,.T.);
#2495=ORIENTED_EDGE('',*,*,#1884,.T.);
#2496=ORIENTED_EDGE('',*,*,#1885,.T.);
#2497=ORIENTED_EDGE('',*,*,#1886,.T.);
#2498=ORIENTED_EDGE('',*,*,#1887,.T.);
#2499=ORIENTED_EDGE('',*,*,#1888,.T.);
#2500=ORIENTED_EDGE('',*,*,#1889,.T.);
#2501=ORIENTED_EDGE('',*,*,#1890,.T.);
#2502=ORIENTED_EDGE('',*,*,#1891,.T.);
#2503=ORIENTED_EDGE('',*,*,#1892,.T.);
#2504=ORIENTED_EDGE('',*,*,#1893,.T.);
#2505=ORIENTED_EDGE('',*,*,#1894,.T.);
#2506=ORIENTED_EDGE('',*,*,#1895,.T.);
#2507=ORIENTED_EDGE('',*,*,#1896,.T.);
#2508=ORIENTED_EDGE('',*,*,#1897,.T.);
#2509=ORIENTED_EDGE('',*,*,#1898,.T.);
#2510=ORIENTED_EDGE('',*,*,#1899,.T.);
#2511=ORIENTED_EDGE('',*,*,#1900,.T.);
#2512=ORIENTED_EDGE('',*,*,#1901,.T.);
#2513=ORIENTED_EDGE('',*,*,#1902,.T.);
#2514=ORIENTED_EDGE('',*,*,#1903,.T.);
#2515=ORIENTED_EDGE('',*,*,#1904,.T.);
#2516=ORIENTED_EDGE('',*,*,#1905,.T.);
#2517=ORIENTED_EDGE('',*,*,#1906,.T.);
#2518=ORIENTED_EDGE('',*,*,#1907,.T.);
#2519=ORIENTED_EDGE('',*,*,#1908,.T.);
#2520=ORIENTED_EDGE('',*,*,#1909,.T.);
#2521=ORIENTED_EDGE('',*,*,#1910,.T.);
#2522=ORIENTED_EDGE('',*,*,#1911,.T.);
#2523=ORIENTED_EDGE('',*,*,#1912,.T.);
#2524=ORIENTED_EDGE('',*,*,#1913,.T.);
#2525=ORIENTED_EDGE('',*,*,#1914,.T.);
#2526=ORIENTED_EDGE('',*,*,#1915,.T.);
#2527=ORIENTED_EDGE('',*,*,#1916,.T.);
#2528=ORIENTED_EDGE('',*,*,#1917,.T.);
#2529=ORIENTED_EDGE('',*,*,#1918,.T.);
#2530=ORIENTED_EDGE('',*,*,#1919,.T.);
#2531=ORIENTED_EDGE('',*,*,#1920,.T.);
#2532=ORIENTED_EDGE('',*,*,#1921,.T.);
#2533=ORIENTED_EDGE('',*,*,#1922,.T.);
#2534=ORIENTED_EDGE('',*,*,#1923,.T.);
#2535=ORIENTED_EDGE('',*,*,#1924,.T.);
#2536=ORIENTED_EDGE('',*,*,#1797,.T.);
#2537=ORIENTED_EDGE('',*,*,#1801,.T.);
#2538=ORIENTED_EDGE('',*,*,#1804,.T.);
#2539=ORIENTED_EDGE('',*,*,#1807,.T.);
#2540=ORIENTED_EDGE('',*,*,#1810,.T.);
#2541=ORIENTED_EDGE('',*,*,#1813,.T.);
#2542=ORIENTED_EDGE('',*,*,#1816,.T.);
#2543=ORIENTED_EDGE('',*,*,#1819,.T.);
#2544=ORIENTED_EDGE('',*,*,#1822,.T.);
#2545=ORIENTED_EDGE('',*,*,#1825,.T.);
#2546=ORIENTED_EDGE('',*,*,#1828,.T.);
#2547=ORIENTED_EDGE('',*,*,#1831,.T.);
#2548=ORIENTED_EDGE('',*,*,#1834,.T.);
#2549=ORIENTED_EDGE('',*,*,#1837,.T.);
#2550=ORIENTED_EDGE('',*,*,#1840,.T.);
#2551=ORIENTED_EDGE('',*,*,#1925,.F.);
#2552=ORIENTED_EDGE('',*,*,#1926,.T.);
#2553=ORIENTED_EDGE('',*,*,#1927,.F.);
#2554=ORIENTED_EDGE('',*,*,#1928,.F.);
#2555=ORIENTED_EDGE('',*,*,#1929,.F.);
#2556=ORIENTED_EDGE('',*,*,#1928,.T.);
#2557=ORIENTED_EDGE('',*,*,#1930,.F.);
#2558=ORIENTED_EDGE('',*,*,#1931,.F.);
#2559=ORIENTED_EDGE('',*,*,#1932,.F.);
#2560=ORIENTED_EDGE('',*,*,#1931,.T.);
#2561=ORIENTED_EDGE('',*,*,#1933,.F.);
#2562=ORIENTED_EDGE('',*,*,#1934,.F.);
#2563=ORIENTED_EDGE('',*,*,#1935,.F.);
#2564=ORIENTED_EDGE('',*,*,#1934,.T.);
#2565=ORIENTED_EDGE('',*,*,#1936,.F.);
#2566=ORIENTED_EDGE('',*,*,#1937,.F.);
#2567=ORIENTED_EDGE('',*,*,#1938,.F.);
#2568=ORIENTED_EDGE('',*,*,#1937,.T.);
#2569=ORIENTED_EDGE('',*,*,#1939,.F.);
#2570=ORIENTED_EDGE('',*,*,#1940,.F.);
#2571=ORIENTED_EDGE('',*,*,#1941,.F.);
#2572=ORIENTED_EDGE('',*,*,#1940,.T.);
#2573=ORIENTED_EDGE('',*,*,#1942,.F.);
#2574=ORIENTED_EDGE('',*,*,#1943,.F.);
#2575=ORIENTED_EDGE('',*,*,#1944,.F.);
#2576=ORIENTED_EDGE('',*,*,#1943,.T.);
#2577=ORIENTED_EDGE('',*,*,#1945,.F.);
#2578=ORIENTED_EDGE('',*,*,#1946,.F.);
#2579=ORIENTED_EDGE('',*,*,#1947,.F.);
#2580=ORIENTED_EDGE('',*,*,#1946,.T.);
#2581=ORIENTED_EDGE('',*,*,#1948,.F.);
#2582=ORIENTED_EDGE('',*,*,#1926,.F.);
#2583=ORIENTED_EDGE('',*,*,#1846,.F.);
#2584=ORIENTED_EDGE('',*,*,#1949,.T.);
#2585=ORIENTED_EDGE('',*,*,#1950,.F.);
#2586=ORIENTED_EDGE('',*,*,#1951,.F.);
#2587=ORIENTED_EDGE('',*,*,#1847,.F.);
#2588=ORIENTED_EDGE('',*,*,#1951,.T.);
#2589=ORIENTED_EDGE('',*,*,#1952,.F.);
#2590=ORIENTED_EDGE('',*,*,#1953,.F.);
#2591=ORIENTED_EDGE('',*,*,#1848,.F.);
#2592=ORIENTED_EDGE('',*,*,#1953,.T.);
#2593=ORIENTED_EDGE('',*,*,#1954,.F.);
#2594=ORIENTED_EDGE('',*,*,#1955,.F.);
#2595=ORIENTED_EDGE('',*,*,#1849,.F.);
#2596=ORIENTED_EDGE('',*,*,#1955,.T.);
#2597=ORIENTED_EDGE('',*,*,#1956,.F.);
#2598=ORIENTED_EDGE('',*,*,#1957,.F.);
#2599=ORIENTED_EDGE('',*,*,#1850,.F.);
#2600=ORIENTED_EDGE('',*,*,#1957,.T.);
#2601=ORIENTED_EDGE('',*,*,#1958,.F.);
#2602=ORIENTED_EDGE('',*,*,#1959,.F.);
#2603=ORIENTED_EDGE('',*,*,#1851,.F.);
#2604=ORIENTED_EDGE('',*,*,#1959,.T.);
#2605=ORIENTED_EDGE('',*,*,#1960,.F.);
#2606=ORIENTED_EDGE('',*,*,#1961,.F.);
#2607=ORIENTED_EDGE('',*,*,#1852,.F.);
#2608=ORIENTED_EDGE('',*,*,#1961,.T.);
#2609=ORIENTED_EDGE('',*,*,#1962,.F.);
#2610=ORIENTED_EDGE('',*,*,#1963,.F.);
#2611=ORIENTED_EDGE('',*,*,#1853,.F.);
#2612=ORIENTED_EDGE('',*,*,#1963,.T.);
#2613=ORIENTED_EDGE('',*,*,#1964,.F.);
#2614=ORIENTED_EDGE('',*,*,#1965,.F.);
#2615=ORIENTED_EDGE('',*,*,#1854,.F.);
#2616=ORIENTED_EDGE('',*,*,#1965,.T.);
#2617=ORIENTED_EDGE('',*,*,#1966,.F.);
#2618=ORIENTED_EDGE('',*,*,#1967,.F.);
#2619=ORIENTED_EDGE('',*,*,#1855,.F.);
#2620=ORIENTED_EDGE('',*,*,#1967,.T.);
#2621=ORIENTED_EDGE('',*,*,#1968,.F.);
#2622=ORIENTED_EDGE('',*,*,#1969,.F.);
#2623=ORIENTED_EDGE('',*,*,#1856,.F.);
#2624=ORIENTED_EDGE('',*,*,#1969,.T.);
#2625=ORIENTED_EDGE('',*,*,#1970,.F.);
#2626=ORIENTED_EDGE('',*,*,#1949,.F.);
#2627=ORIENTED_EDGE('',*,*,#1970,.T.);
#2628=ORIENTED_EDGE('',*,*,#1968,.T.);
#2629=ORIENTED_EDGE('',*,*,#1966,.T.);
#2630=ORIENTED_EDGE('',*,*,#1964,.T.);
#2631=ORIENTED_EDGE('',*,*,#1962,.T.);
#2632=ORIENTED_EDGE('',*,*,#1960,.T.);
#2633=ORIENTED_EDGE('',*,*,#1958,.T.);
#2634=ORIENTED_EDGE('',*,*,#1956,.T.);
#2635=ORIENTED_EDGE('',*,*,#1954,.T.);
#2636=ORIENTED_EDGE('',*,*,#1952,.T.);
#2637=ORIENTED_EDGE('',*,*,#1950,.T.);
#2638=ORIENTED_EDGE('',*,*,#1948,.T.);
#2639=ORIENTED_EDGE('',*,*,#1945,.T.);
#2640=ORIENTED_EDGE('',*,*,#1942,.T.);
#2641=ORIENTED_EDGE('',*,*,#1939,.T.);
#2642=ORIENTED_EDGE('',*,*,#1936,.T.);
#2643=ORIENTED_EDGE('',*,*,#1933,.T.);
#2644=ORIENTED_EDGE('',*,*,#1930,.T.);
#2645=ORIENTED_EDGE('',*,*,#1927,.T.);
#2646=ORIENTED_EDGE('',*,*,#1773,.T.);
#2647=ORIENTED_EDGE('',*,*,#1777,.T.);
#2648=ORIENTED_EDGE('',*,*,#1780,.T.);
#2649=ORIENTED_EDGE('',*,*,#1783,.T.);
#2650=ORIENTED_EDGE('',*,*,#1786,.T.);
#2651=ORIENTED_EDGE('',*,*,#1789,.T.);
#2652=ORIENTED_EDGE('',*,*,#1792,.T.);
#2653=ORIENTED_EDGE('',*,*,#1795,.T.);
#2654=ORIENTED_EDGE('',*,*,#1971,.F.);
#2655=ORIENTED_EDGE('',*,*,#1972,.T.);
#2656=ORIENTED_EDGE('',*,*,#1973,.F.);
#2657=ORIENTED_EDGE('',*,*,#1974,.F.);
#2658=ORIENTED_EDGE('',*,*,#1975,.F.);
#2659=ORIENTED_EDGE('',*,*,#1974,.T.);
#2660=ORIENTED_EDGE('',*,*,#1976,.F.);
#2661=ORIENTED_EDGE('',*,*,#1977,.F.);
#2662=ORIENTED_EDGE('',*,*,#1978,.F.);
#2663=ORIENTED_EDGE('',*,*,#1977,.T.);
#2664=ORIENTED_EDGE('',*,*,#1979,.F.);
#2665=ORIENTED_EDGE('',*,*,#1980,.F.);
#2666=ORIENTED_EDGE('',*,*,#1981,.F.);
#2667=ORIENTED_EDGE('',*,*,#1980,.T.);
#2668=ORIENTED_EDGE('',*,*,#1982,.F.);
#2669=ORIENTED_EDGE('',*,*,#1983,.F.);
#2670=ORIENTED_EDGE('',*,*,#1984,.F.);
#2671=ORIENTED_EDGE('',*,*,#1983,.T.);
#2672=ORIENTED_EDGE('',*,*,#1985,.F.);
#2673=ORIENTED_EDGE('',*,*,#1986,.F.);
#2674=ORIENTED_EDGE('',*,*,#1987,.F.);
#2675=ORIENTED_EDGE('',*,*,#1986,.T.);
#2676=ORIENTED_EDGE('',*,*,#1988,.F.);
#2677=ORIENTED_EDGE('',*,*,#1989,.F.);
#2678=ORIENTED_EDGE('',*,*,#1990,.F.);
#2679=ORIENTED_EDGE('',*,*,#1989,.T.);
#2680=ORIENTED_EDGE('',*,*,#1991,.F.);
#2681=ORIENTED_EDGE('',*,*,#1992,.F.);
#2682=ORIENTED_EDGE('',*,*,#1993,.F.);
#2683=ORIENTED_EDGE('',*,*,#1992,.T.);
#2684=ORIENTED_EDGE('',*,*,#1994,.F.);
#2685=ORIENTED_EDGE('',*,*,#1995,.F.);
#2686=ORIENTED_EDGE('',*,*,#1996,.F.);
#2687=ORIENTED_EDGE('',*,*,#1995,.T.);
#2688=ORIENTED_EDGE('',*,*,#1997,.F.);
#2689=ORIENTED_EDGE('',*,*,#1998,.F.);
#2690=ORIENTED_EDGE('',*,*,#1999,.F.);
#2691=ORIENTED_EDGE('',*,*,#1998,.T.);
#2692=ORIENTED_EDGE('',*,*,#2000,.F.);
#2693=ORIENTED_EDGE('',*,*,#1972,.F.);
#2694=ORIENTED_EDGE('',*,*,#1898,.F.);
#2695=ORIENTED_EDGE('',*,*,#2001,.T.);
#2696=ORIENTED_EDGE('',*,*,#2002,.F.);
#2697=ORIENTED_EDGE('',*,*,#2003,.F.);
#2698=ORIENTED_EDGE('',*,*,#1899,.F.);
#2699=ORIENTED_EDGE('',*,*,#2003,.T.);
#2700=ORIENTED_EDGE('',*,*,#2004,.F.);
#2701=ORIENTED_EDGE('',*,*,#2005,.F.);
#2702=ORIENTED_EDGE('',*,*,#1900,.F.);
#2703=ORIENTED_EDGE('',*,*,#2005,.T.);
#2704=ORIENTED_EDGE('',*,*,#2006,.F.);
#2705=ORIENTED_EDGE('',*,*,#2007,.F.);
#2706=ORIENTED_EDGE('',*,*,#1901,.F.);
#2707=ORIENTED_EDGE('',*,*,#2007,.T.);
#2708=ORIENTED_EDGE('',*,*,#2008,.F.);
#2709=ORIENTED_EDGE('',*,*,#2009,.F.);
#2710=ORIENTED_EDGE('',*,*,#1902,.F.);
#2711=ORIENTED_EDGE('',*,*,#2009,.T.);
#2712=ORIENTED_EDGE('',*,*,#2010,.F.);
#2713=ORIENTED_EDGE('',*,*,#2011,.F.);
#2714=ORIENTED_EDGE('',*,*,#1903,.F.);
#2715=ORIENTED_EDGE('',*,*,#2011,.T.);
#2716=ORIENTED_EDGE('',*,*,#2012,.F.);
#2717=ORIENTED_EDGE('',*,*,#2013,.F.);
#2718=ORIENTED_EDGE('',*,*,#1904,.F.);
#2719=ORIENTED_EDGE('',*,*,#2013,.T.);
#2720=ORIENTED_EDGE('',*,*,#2014,.F.);
#2721=ORIENTED_EDGE('',*,*,#2015,.F.);
#2722=ORIENTED_EDGE('',*,*,#1905,.F.);
#2723=ORIENTED_EDGE('',*,*,#2015,.T.);
#2724=ORIENTED_EDGE('',*,*,#2016,.F.);
#2725=ORIENTED_EDGE('',*,*,#2017,.F.);
#2726=ORIENTED_EDGE('',*,*,#1906,.F.);
#2727=ORIENTED_EDGE('',*,*,#2017,.T.);
#2728=ORIENTED_EDGE('',*,*,#2018,.F.);
#2729=ORIENTED_EDGE('',*,*,#2019,.F.);
#2730=ORIENTED_EDGE('',*,*,#1907,.F.);
#2731=ORIENTED_EDGE('',*,*,#2019,.T.);
#2732=ORIENTED_EDGE('',*,*,#2020,.F.);
#2733=ORIENTED_EDGE('',*,*,#2021,.F.);
#2734=ORIENTED_EDGE('',*,*,#1908,.F.);
#2735=ORIENTED_EDGE('',*,*,#2021,.T.);
#2736=ORIENTED_EDGE('',*,*,#2022,.F.);
#2737=ORIENTED_EDGE('',*,*,#2023,.F.);
#2738=ORIENTED_EDGE('',*,*,#1909,.F.);
#2739=ORIENTED_EDGE('',*,*,#2023,.T.);
#2740=ORIENTED_EDGE('',*,*,#2024,.F.);
#2741=ORIENTED_EDGE('',*,*,#2025,.F.);
#2742=ORIENTED_EDGE('',*,*,#1910,.F.);
#2743=ORIENTED_EDGE('',*,*,#2025,.T.);
#2744=ORIENTED_EDGE('',*,*,#2026,.F.);
#2745=ORIENTED_EDGE('',*,*,#2027,.F.);
#2746=ORIENTED_EDGE('',*,*,#1911,.F.);
#2747=ORIENTED_EDGE('',*,*,#2027,.T.);
#2748=ORIENTED_EDGE('',*,*,#2028,.F.);
#2749=ORIENTED_EDGE('',*,*,#2029,.F.);
#2750=ORIENTED_EDGE('',*,*,#1912,.F.);
#2751=ORIENTED_EDGE('',*,*,#2029,.T.);
#2752=ORIENTED_EDGE('',*,*,#2030,.F.);
#2753=ORIENTED_EDGE('',*,*,#2031,.F.);
#2754=ORIENTED_EDGE('',*,*,#1913,.F.);
#2755=ORIENTED_EDGE('',*,*,#2031,.T.);
#2756=ORIENTED_EDGE('',*,*,#2032,.F.);
#2757=ORIENTED_EDGE('',*,*,#2033,.F.);
#2758=ORIENTED_EDGE('',*,*,#1914,.F.);
#2759=ORIENTED_EDGE('',*,*,#2033,.T.);
#2760=ORIENTED_EDGE('',*,*,#2034,.F.);
#2761=ORIENTED_EDGE('',*,*,#2035,.F.);
#2762=ORIENTED_EDGE('',*,*,#1915,.F.);
#2763=ORIENTED_EDGE('',*,*,#2035,.T.);
#2764=ORIENTED_EDGE('',*,*,#2036,.F.);
#2765=ORIENTED_EDGE('',*,*,#2037,.F.);
#2766=ORIENTED_EDGE('',*,*,#1916,.F.);
#2767=ORIENTED_EDGE('',*,*,#2037,.T.);
#2768=ORIENTED_EDGE('',*,*,#2038,.F.);
#2769=ORIENTED_EDGE('',*,*,#2039,.F.);
#2770=ORIENTED_EDGE('',*,*,#1917,.F.);
#2771=ORIENTED_EDGE('',*,*,#2039,.T.);
#2772=ORIENTED_EDGE('',*,*,#2040,.F.);
#2773=ORIENTED_EDGE('',*,*,#2041,.F.);
#2774=ORIENTED_EDGE('',*,*,#1918,.F.);
#2775=ORIENTED_EDGE('',*,*,#2041,.T.);
#2776=ORIENTED_EDGE('',*,*,#2042,.F.);
#2777=ORIENTED_EDGE('',*,*,#2043,.F.);
#2778=ORIENTED_EDGE('',*,*,#1919,.F.);
#2779=ORIENTED_EDGE('',*,*,#2043,.T.);
#2780=ORIENTED_EDGE('',*,*,#2044,.F.);
#2781=ORIENTED_EDGE('',*,*,#2045,.F.);
#2782=ORIENTED_EDGE('',*,*,#1920,.F.);
#2783=ORIENTED_EDGE('',*,*,#2045,.T.);
#2784=ORIENTED_EDGE('',*,*,#2046,.F.);
#2785=ORIENTED_EDGE('',*,*,#2047,.F.);
#2786=ORIENTED_EDGE('',*,*,#1921,.F.);
#2787=ORIENTED_EDGE('',*,*,#2047,.T.);
#2788=ORIENTED_EDGE('',*,*,#2048,.F.);
#2789=ORIENTED_EDGE('',*,*,#2049,.F.);
#2790=ORIENTED_EDGE('',*,*,#1922,.F.);
#2791=ORIENTED_EDGE('',*,*,#2049,.T.);
#2792=ORIENTED_EDGE('',*,*,#2050,.F.);
#2793=ORIENTED_EDGE('',*,*,#2051,.F.);
#2794=ORIENTED_EDGE('',*,*,#1923,.F.);
#2795=ORIENTED_EDGE('',*,*,#2051,.T.);
#2796=ORIENTED_EDGE('',*,*,#2052,.F.);
#2797=ORIENTED_EDGE('',*,*,#2053,.F.);
#2798=ORIENTED_EDGE('',*,*,#1924,.F.);
#2799=ORIENTED_EDGE('',*,*,#2053,.T.);
#2800=ORIENTED_EDGE('',*,*,#2054,.F.);
#2801=ORIENTED_EDGE('',*,*,#2001,.F.);
#2802=ORIENTED_EDGE('',*,*,#2054,.T.);
#2803=ORIENTED_EDGE('',*,*,#2052,.T.);
#2804=ORIENTED_EDGE('',*,*,#2050,.T.);
#2805=ORIENTED_EDGE('',*,*,#2048,.T.);
#2806=ORIENTED_EDGE('',*,*,#2046,.T.);
#2807=ORIENTED_EDGE('',*,*,#2044,.T.);
#2808=ORIENTED_EDGE('',*,*,#2042,.T.);
#2809=ORIENTED_EDGE('',*,*,#2040,.T.);
#2810=ORIENTED_EDGE('',*,*,#2038,.T.);
#2811=ORIENTED_EDGE('',*,*,#2036,.T.);
#2812=ORIENTED_EDGE('',*,*,#2034,.T.);
#2813=ORIENTED_EDGE('',*,*,#2032,.T.);
#2814=ORIENTED_EDGE('',*,*,#2030,.T.);
#2815=ORIENTED_EDGE('',*,*,#2028,.T.);
#2816=ORIENTED_EDGE('',*,*,#2026,.T.);
#2817=ORIENTED_EDGE('',*,*,#2024,.T.);
#2818=ORIENTED_EDGE('',*,*,#2022,.T.);
#2819=ORIENTED_EDGE('',*,*,#2020,.T.);
#2820=ORIENTED_EDGE('',*,*,#2018,.T.);
#2821=ORIENTED_EDGE('',*,*,#2016,.T.);
#2822=ORIENTED_EDGE('',*,*,#2014,.T.);
#2823=ORIENTED_EDGE('',*,*,#2012,.T.);
#2824=ORIENTED_EDGE('',*,*,#2010,.T.);
#2825=ORIENTED_EDGE('',*,*,#2008,.T.);
#2826=ORIENTED_EDGE('',*,*,#2006,.T.);
#2827=ORIENTED_EDGE('',*,*,#2004,.T.);
#2828=ORIENTED_EDGE('',*,*,#2002,.T.);
#2829=ORIENTED_EDGE('',*,*,#2000,.T.);
#2830=ORIENTED_EDGE('',*,*,#1997,.T.);
#2831=ORIENTED_EDGE('',*,*,#1994,.T.);
#2832=ORIENTED_EDGE('',*,*,#1991,.T.);
#2833=ORIENTED_EDGE('',*,*,#1988,.T.);
#2834=ORIENTED_EDGE('',*,*,#1985,.T.);
#2835=ORIENTED_EDGE('',*,*,#1982,.T.);
#2836=ORIENTED_EDGE('',*,*,#1979,.T.);
#2837=ORIENTED_EDGE('',*,*,#1976,.T.);
#2838=ORIENTED_EDGE('',*,*,#1973,.T.);
#2839=ORIENTED_EDGE('',*,*,#1925,.T.);
#2840=ORIENTED_EDGE('',*,*,#1929,.T.);
#2841=ORIENTED_EDGE('',*,*,#1932,.T.);
#2842=ORIENTED_EDGE('',*,*,#1935,.T.);
#2843=ORIENTED_EDGE('',*,*,#1938,.T.);
#2844=ORIENTED_EDGE('',*,*,#1941,.T.);
#2845=ORIENTED_EDGE('',*,*,#1944,.T.);
#2846=ORIENTED_EDGE('',*,*,#1947,.T.);
#2847=ORIENTED_EDGE('',*,*,#2055,.F.);
#2848=ORIENTED_EDGE('',*,*,#2056,.T.);
#2849=ORIENTED_EDGE('',*,*,#2057,.F.);
#2850=ORIENTED_EDGE('',*,*,#2058,.F.);
#2851=ORIENTED_EDGE('',*,*,#2059,.F.);
#2852=ORIENTED_EDGE('',*,*,#2058,.T.);
#2853=ORIENTED_EDGE('',*,*,#2060,.F.);
#2854=ORIENTED_EDGE('',*,*,#2061,.F.);
#2855=ORIENTED_EDGE('',*,*,#2062,.F.);
#2856=ORIENTED_EDGE('',*,*,#2061,.T.);
#2857=ORIENTED_EDGE('',*,*,#2063,.F.);
#2858=ORIENTED_EDGE('',*,*,#2064,.F.);
#2859=ORIENTED_EDGE('',*,*,#2065,.F.);
#2860=ORIENTED_EDGE('',*,*,#2064,.T.);
#2861=ORIENTED_EDGE('',*,*,#2066,.F.);
#2862=ORIENTED_EDGE('',*,*,#2067,.F.);
#2863=ORIENTED_EDGE('',*,*,#2068,.F.);
#2864=ORIENTED_EDGE('',*,*,#2067,.T.);
#2865=ORIENTED_EDGE('',*,*,#2069,.F.);
#2866=ORIENTED_EDGE('',*,*,#2070,.F.);
#2867=ORIENTED_EDGE('',*,*,#2071,.F.);
#2868=ORIENTED_EDGE('',*,*,#2070,.T.);
#2869=ORIENTED_EDGE('',*,*,#2072,.F.);
#2870=ORIENTED_EDGE('',*,*,#2073,.F.);
#2871=ORIENTED_EDGE('',*,*,#2074,.F.);
#2872=ORIENTED_EDGE('',*,*,#2073,.T.);
#2873=ORIENTED_EDGE('',*,*,#2075,.F.);
#2874=ORIENTED_EDGE('',*,*,#2076,.F.);
#2875=ORIENTED_EDGE('',*,*,#2077,.F.);
#2876=ORIENTED_EDGE('',*,*,#2076,.T.);
#2877=ORIENTED_EDGE('',*,*,#2078,.F.);
#2878=ORIENTED_EDGE('',*,*,#2056,.F.);
#2879=ORIENTED_EDGE('',*,*,#1857,.F.);
#2880=ORIENTED_EDGE('',*,*,#2079,.T.);
#2881=ORIENTED_EDGE('',*,*,#2080,.F.);
#2882=ORIENTED_EDGE('',*,*,#2081,.F.);
#2883=ORIENTED_EDGE('',*,*,#1858,.F.);
#2884=ORIENTED_EDGE('',*,*,#2081,.T.);
#2885=ORIENTED_EDGE('',*,*,#2082,.F.);
#2886=ORIENTED_EDGE('',*,*,#2083,.F.);
#2887=ORIENTED_EDGE('',*,*,#1859,.F.);
#2888=ORIENTED_EDGE('',*,*,#2083,.T.);
#2889=ORIENTED_EDGE('',*,*,#2084,.F.);
#2890=ORIENTED_EDGE('',*,*,#2085,.F.);
#2891=ORIENTED_EDGE('',*,*,#1860,.F.);
#2892=ORIENTED_EDGE('',*,*,#2085,.T.);
#2893=ORIENTED_EDGE('',*,*,#2086,.F.);
#2894=ORIENTED_EDGE('',*,*,#2087,.F.);
#2895=ORIENTED_EDGE('',*,*,#1861,.F.);
#2896=ORIENTED_EDGE('',*,*,#2087,.T.);
#2897=ORIENTED_EDGE('',*,*,#2088,.F.);
#2898=ORIENTED_EDGE('',*,*,#2089,.F.);
#2899=ORIENTED_EDGE('',*,*,#1862,.F.);
#2900=ORIENTED_EDGE('',*,*,#2089,.T.);
#2901=ORIENTED_EDGE('',*,*,#2090,.F.);
#2902=ORIENTED_EDGE('',*,*,#2091,.F.);
#2903=ORIENTED_EDGE('',*,*,#1863,.F.);
#2904=ORIENTED_EDGE('',*,*,#2091,.T.);
#2905=ORIENTED_EDGE('',*,*,#2092,.F.);
#2906=ORIENTED_EDGE('',*,*,#2093,.F.);
#2907=ORIENTED_EDGE('',*,*,#1864,.F.);
#2908=ORIENTED_EDGE('',*,*,#2093,.T.);
#2909=ORIENTED_EDGE('',*,*,#2094,.F.);
#2910=ORIENTED_EDGE('',*,*,#2095,.F.);
#2911=ORIENTED_EDGE('',*,*,#1865,.F.);
#2912=ORIENTED_EDGE('',*,*,#2095,.T.);
#2913=ORIENTED_EDGE('',*,*,#2096,.F.);
#2914=ORIENTED_EDGE('',*,*,#2097,.F.);
#2915=ORIENTED_EDGE('',*,*,#1866,.F.);
#2916=ORIENTED_EDGE('',*,*,#2097,.T.);
#2917=ORIENTED_EDGE('',*,*,#2098,.F.);
#2918=ORIENTED_EDGE('',*,*,#2099,.F.);
#2919=ORIENTED_EDGE('',*,*,#1867,.F.);
#2920=ORIENTED_EDGE('',*,*,#2099,.T.);
#2921=ORIENTED_EDGE('',*,*,#2100,.F.);
#2922=ORIENTED_EDGE('',*,*,#2101,.F.);
#2923=ORIENTED_EDGE('',*,*,#1868,.F.);
#2924=ORIENTED_EDGE('',*,*,#2101,.T.);
#2925=ORIENTED_EDGE('',*,*,#2102,.F.);
#2926=ORIENTED_EDGE('',*,*,#2103,.F.);
#2927=ORIENTED_EDGE('',*,*,#1869,.F.);
#2928=ORIENTED_EDGE('',*,*,#2103,.T.);
#2929=ORIENTED_EDGE('',*,*,#2104,.F.);
#2930=ORIENTED_EDGE('',*,*,#2105,.F.);
#2931=ORIENTED_EDGE('',*,*,#1870,.F.);
#2932=ORIENTED_EDGE('',*,*,#2105,.T.);
#2933=ORIENTED_EDGE('',*,*,#2106,.F.);
#2934=ORIENTED_EDGE('',*,*,#2079,.F.);
#2935=ORIENTED_EDGE('',*,*,#2106,.T.);
#2936=ORIENTED_EDGE('',*,*,#2104,.T.);
#2937=ORIENTED_EDGE('',*,*,#2102,.T.);
#2938=ORIENTED_EDGE('',*,*,#2100,.T.);
#2939=ORIENTED_EDGE('',*,*,#2098,.T.);
#2940=ORIENTED_EDGE('',*,*,#2096,.T.);
#2941=ORIENTED_EDGE('',*,*,#2094,.T.);
#2942=ORIENTED_EDGE('',*,*,#2092,.T.);
#2943=ORIENTED_EDGE('',*,*,#2090,.T.);
#2944=ORIENTED_EDGE('',*,*,#2088,.T.);
#2945=ORIENTED_EDGE('',*,*,#2086,.T.);
#2946=ORIENTED_EDGE('',*,*,#2084,.T.);
#2947=ORIENTED_EDGE('',*,*,#2082,.T.);
#2948=ORIENTED_EDGE('',*,*,#2080,.T.);
#2949=ORIENTED_EDGE('',*,*,#2078,.T.);
#2950=ORIENTED_EDGE('',*,*,#2075,.T.);
#2951=ORIENTED_EDGE('',*,*,#2072,.T.);
#2952=ORIENTED_EDGE('',*,*,#2069,.T.);
#2953=ORIENTED_EDGE('',*,*,#2066,.T.);
#2954=ORIENTED_EDGE('',*,*,#2063,.T.);
#2955=ORIENTED_EDGE('',*,*,#2060,.T.);
#2956=ORIENTED_EDGE('',*,*,#2057,.T.);
#2957=ORIENTED_EDGE('',*,*,#1971,.T.);
#2958=ORIENTED_EDGE('',*,*,#1975,.T.);
#2959=ORIENTED_EDGE('',*,*,#1978,.T.);
#2960=ORIENTED_EDGE('',*,*,#1981,.T.);
#2961=ORIENTED_EDGE('',*,*,#1984,.T.);
#2962=ORIENTED_EDGE('',*,*,#1987,.T.);
#2963=ORIENTED_EDGE('',*,*,#1990,.T.);
#2964=ORIENTED_EDGE('',*,*,#1993,.T.);
#2965=ORIENTED_EDGE('',*,*,#1996,.T.);
#2966=ORIENTED_EDGE('',*,*,#1999,.T.);
#2967=ORIENTED_EDGE('',*,*,#2107,.F.);
#2968=ORIENTED_EDGE('',*,*,#2108,.T.);
#2969=ORIENTED_EDGE('',*,*,#2109,.F.);
#2970=ORIENTED_EDGE('',*,*,#2110,.F.);
#2971=ORIENTED_EDGE('',*,*,#2111,.F.);
#2972=ORIENTED_EDGE('',*,*,#2110,.T.);
#2973=ORIENTED_EDGE('',*,*,#2112,.F.);
#2974=ORIENTED_EDGE('',*,*,#2113,.F.);
#2975=ORIENTED_EDGE('',*,*,#2114,.F.);
#2976=ORIENTED_EDGE('',*,*,#2113,.T.);
#2977=ORIENTED_EDGE('',*,*,#2115,.F.);
#2978=ORIENTED_EDGE('',*,*,#2116,.F.);
#2979=ORIENTED_EDGE('',*,*,#2117,.F.);
#2980=ORIENTED_EDGE('',*,*,#2116,.T.);
#2981=ORIENTED_EDGE('',*,*,#2118,.F.);
#2982=ORIENTED_EDGE('',*,*,#2119,.F.);
#2983=ORIENTED_EDGE('',*,*,#2120,.F.);
#2984=ORIENTED_EDGE('',*,*,#2119,.T.);
#2985=ORIENTED_EDGE('',*,*,#2121,.F.);
#2986=ORIENTED_EDGE('',*,*,#2122,.F.);
#2987=ORIENTED_EDGE('',*,*,#2123,.F.);
#2988=ORIENTED_EDGE('',*,*,#2122,.T.);
#2989=ORIENTED_EDGE('',*,*,#2124,.F.);
#2990=ORIENTED_EDGE('',*,*,#2125,.F.);
#2991=ORIENTED_EDGE('',*,*,#2126,.F.);
#2992=ORIENTED_EDGE('',*,*,#2125,.T.);
#2993=ORIENTED_EDGE('',*,*,#2127,.F.);
#2994=ORIENTED_EDGE('',*,*,#2128,.F.);
#2995=ORIENTED_EDGE('',*,*,#2129,.F.);
#2996=ORIENTED_EDGE('',*,*,#2128,.T.);
#2997=ORIENTED_EDGE('',*,*,#2130,.F.);
#2998=ORIENTED_EDGE('',*,*,#2131,.F.);
#2999=ORIENTED_EDGE('',*,*,#2132,.F.);
#3000=ORIENTED_EDGE('',*,*,#2131,.T.);
#3001=ORIENTED_EDGE('',*,*,#2133,.F.);
#3002=ORIENTED_EDGE('',*,*,#2134,.F.);
#3003=ORIENTED_EDGE('',*,*,#2135,.F.);
#3004=ORIENTED_EDGE('',*,*,#2134,.T.);
#3005=ORIENTED_EDGE('',*,*,#2136,.F.);
#3006=ORIENTED_EDGE('',*,*,#2108,.F.);
#3007=ORIENTED_EDGE('',*,*,#1871,.F.);
#3008=ORIENTED_EDGE('',*,*,#2137,.T.);
#3009=ORIENTED_EDGE('',*,*,#2138,.F.);
#3010=ORIENTED_EDGE('',*,*,#2139,.F.);
#3011=ORIENTED_EDGE('',*,*,#1872,.F.);
#3012=ORIENTED_EDGE('',*,*,#2139,.T.);
#3013=ORIENTED_EDGE('',*,*,#2140,.F.);
#3014=ORIENTED_EDGE('',*,*,#2141,.F.);
#3015=ORIENTED_EDGE('',*,*,#1873,.F.);
#3016=ORIENTED_EDGE('',*,*,#2141,.T.);
#3017=ORIENTED_EDGE('',*,*,#2142,.F.);
#3018=ORIENTED_EDGE('',*,*,#2143,.F.);
#3019=ORIENTED_EDGE('',*,*,#1874,.F.);
#3020=ORIENTED_EDGE('',*,*,#2143,.T.);
#3021=ORIENTED_EDGE('',*,*,#2144,.F.);
#3022=ORIENTED_EDGE('',*,*,#2145,.F.);
#3023=ORIENTED_EDGE('',*,*,#1875,.F.);
#3024=ORIENTED_EDGE('',*,*,#2145,.T.);
#3025=ORIENTED_EDGE('',*,*,#2146,.F.);
#3026=ORIENTED_EDGE('',*,*,#2147,.F.);
#3027=ORIENTED_EDGE('',*,*,#1876,.F.);
#3028=ORIENTED_EDGE('',*,*,#2147,.T.);
#3029=ORIENTED_EDGE('',*,*,#2148,.F.);
#3030=ORIENTED_EDGE('',*,*,#2149,.F.);
#3031=ORIENTED_EDGE('',*,*,#1877,.F.);
#3032=ORIENTED_EDGE('',*,*,#2149,.T.);
#3033=ORIENTED_EDGE('',*,*,#2150,.F.);
#3034=ORIENTED_EDGE('',*,*,#2151,.F.);
#3035=ORIENTED_EDGE('',*,*,#1878,.F.);
#3036=ORIENTED_EDGE('',*,*,#2151,.T.);
#3037=ORIENTED_EDGE('',*,*,#2152,.F.);
#3038=ORIENTED_EDGE('',*,*,#2153,.F.);
#3039=ORIENTED_EDGE('',*,*,#1879,.F.);
#3040=ORIENTED_EDGE('',*,*,#2153,.T.);
#3041=ORIENTED_EDGE('',*,*,#2154,.F.);
#3042=ORIENTED_EDGE('',*,*,#2155,.F.);
#3043=ORIENTED_EDGE('',*,*,#1880,.F.);
#3044=ORIENTED_EDGE('',*,*,#2155,.T.);
#3045=ORIENTED_EDGE('',*,*,#2156,.F.);
#3046=ORIENTED_EDGE('',*,*,#2157,.F.);
#3047=ORIENTED_EDGE('',*,*,#1881,.F.);
#3048=ORIENTED_EDGE('',*,*,#2157,.T.);
#3049=ORIENTED_EDGE('',*,*,#2158,.F.);
#3050=ORIENTED_EDGE('',*,*,#2159,.F.);
#3051=ORIENTED_EDGE('',*,*,#1882,.F.);
#3052=ORIENTED_EDGE('',*,*,#2159,.T.);
#3053=ORIENTED_EDGE('',*,*,#2160,.F.);
#3054=ORIENTED_EDGE('',*,*,#2161,.F.);
#3055=ORIENTED_EDGE('',*,*,#1883,.F.);
#3056=ORIENTED_EDGE('',*,*,#2161,.T.);
#3057=ORIENTED_EDGE('',*,*,#2162,.F.);
#3058=ORIENTED_EDGE('',*,*,#2163,.F.);
#3059=ORIENTED_EDGE('',*,*,#1884,.F.);
#3060=ORIENTED_EDGE('',*,*,#2163,.T.);
#3061=ORIENTED_EDGE('',*,*,#2164,.F.);
#3062=ORIENTED_EDGE('',*,*,#2165,.F.);
#3063=ORIENTED_EDGE('',*,*,#1885,.F.);
#3064=ORIENTED_EDGE('',*,*,#2165,.T.);
#3065=ORIENTED_EDGE('',*,*,#2166,.F.);
#3066=ORIENTED_EDGE('',*,*,#2167,.F.);
#3067=ORIENTED_EDGE('',*,*,#1886,.F.);
#3068=ORIENTED_EDGE('',*,*,#2167,.T.);
#3069=ORIENTED_EDGE('',*,*,#2168,.F.);
#3070=ORIENTED_EDGE('',*,*,#2169,.F.);
#3071=ORIENTED_EDGE('',*,*,#1887,.F.);
#3072=ORIENTED_EDGE('',*,*,#2169,.T.);
#3073=ORIENTED_EDGE('',*,*,#2170,.F.);
#3074=ORIENTED_EDGE('',*,*,#2171,.F.);
#3075=ORIENTED_EDGE('',*,*,#1888,.F.);
#3076=ORIENTED_EDGE('',*,*,#2171,.T.);
#3077=ORIENTED_EDGE('',*,*,#2172,.F.);
#3078=ORIENTED_EDGE('',*,*,#2173,.F.);
#3079=ORIENTED_EDGE('',*,*,#1889,.F.);
#3080=ORIENTED_EDGE('',*,*,#2173,.T.);
#3081=ORIENTED_EDGE('',*,*,#2174,.F.);
#3082=ORIENTED_EDGE('',*,*,#2175,.F.);
#3083=ORIENTED_EDGE('',*,*,#1890,.F.);
#3084=ORIENTED_EDGE('',*,*,#2175,.T.);
#3085=ORIENTED_EDGE('',*,*,#2176,.F.);
#3086=ORIENTED_EDGE('',*,*,#2177,.F.);
#3087=ORIENTED_EDGE('',*,*,#1891,.F.);
#3088=ORIENTED_EDGE('',*,*,#2177,.T.);
#3089=ORIENTED_EDGE('',*,*,#2178,.F.);
#3090=ORIENTED_EDGE('',*,*,#2179,.F.);
#3091=ORIENTED_EDGE('',*,*,#1892,.F.);
#3092=ORIENTED_EDGE('',*,*,#2179,.T.);
#3093=ORIENTED_EDGE('',*,*,#2180,.F.);
#3094=ORIENTED_EDGE('',*,*,#2181,.F.);
#3095=ORIENTED_EDGE('',*,*,#1893,.F.);
#3096=ORIENTED_EDGE('',*,*,#2181,.T.);
#3097=ORIENTED_EDGE('',*,*,#2182,.F.);
#3098=ORIENTED_EDGE('',*,*,#2183,.F.);
#3099=ORIENTED_EDGE('',*,*,#1894,.F.);
#3100=ORIENTED_EDGE('',*,*,#2183,.T.);
#3101=ORIENTED_EDGE('',*,*,#2184,.F.);
#3102=ORIENTED_EDGE('',*,*,#2185,.F.);
#3103=ORIENTED_EDGE('',*,*,#1895,.F.);
#3104=ORIENTED_EDGE('',*,*,#2185,.T.);
#3105=ORIENTED_EDGE('',*,*,#2186,.F.);
#3106=ORIENTED_EDGE('',*,*,#2187,.F.);
#3107=ORIENTED_EDGE('',*,*,#1896,.F.);
#3108=ORIENTED_EDGE('',*,*,#2187,.T.);
#3109=ORIENTED_EDGE('',*,*,#2188,.F.);
#3110=ORIENTED_EDGE('',*,*,#2189,.F.);
#3111=ORIENTED_EDGE('',*,*,#1897,.F.);
#3112=ORIENTED_EDGE('',*,*,#2189,.T.);
#3113=ORIENTED_EDGE('',*,*,#2190,.F.);
#3114=ORIENTED_EDGE('',*,*,#2137,.F.);
#3115=ORIENTED_EDGE('',*,*,#2190,.T.);
#3116=ORIENTED_EDGE('',*,*,#2188,.T.);
#3117=ORIENTED_EDGE('',*,*,#2186,.T.);
#3118=ORIENTED_EDGE('',*,*,#2184,.T.);
#3119=ORIENTED_EDGE('',*,*,#2182,.T.);
#3120=ORIENTED_EDGE('',*,*,#2180,.T.);
#3121=ORIENTED_EDGE('',*,*,#2178,.T.);
#3122=ORIENTED_EDGE('',*,*,#2176,.T.);
#3123=ORIENTED_EDGE('',*,*,#2174,.T.);
#3124=ORIENTED_EDGE('',*,*,#2172,.T.);
#3125=ORIENTED_EDGE('',*,*,#2170,.T.);
#3126=ORIENTED_EDGE('',*,*,#2168,.T.);
#3127=ORIENTED_EDGE('',*,*,#2166,.T.);
#3128=ORIENTED_EDGE('',*,*,#2164,.T.);
#3129=ORIENTED_EDGE('',*,*,#2162,.T.);
#3130=ORIENTED_EDGE('',*,*,#2160,.T.);
#3131=ORIENTED_EDGE('',*,*,#2158,.T.);
#3132=ORIENTED_EDGE('',*,*,#2156,.T.);
#3133=ORIENTED_EDGE('',*,*,#2154,.T.);
#3134=ORIENTED_EDGE('',*,*,#2152,.T.);
#3135=ORIENTED_EDGE('',*,*,#2150,.T.);
#3136=ORIENTED_EDGE('',*,*,#2148,.T.);
#3137=ORIENTED_EDGE('',*,*,#2146,.T.);
#3138=ORIENTED_EDGE('',*,*,#2144,.T.);
#3139=ORIENTED_EDGE('',*,*,#2142,.T.);
#3140=ORIENTED_EDGE('',*,*,#2140,.T.);
#3141=ORIENTED_EDGE('',*,*,#2138,.T.);
#3142=ORIENTED_EDGE('',*,*,#2136,.T.);
#3143=ORIENTED_EDGE('',*,*,#2133,.T.);
#3144=ORIENTED_EDGE('',*,*,#2130,.T.);
#3145=ORIENTED_EDGE('',*,*,#2127,.T.);
#3146=ORIENTED_EDGE('',*,*,#2124,.T.);
#3147=ORIENTED_EDGE('',*,*,#2121,.T.);
#3148=ORIENTED_EDGE('',*,*,#2118,.T.);
#3149=ORIENTED_EDGE('',*,*,#2115,.T.);
#3150=ORIENTED_EDGE('',*,*,#2112,.T.);
#3151=ORIENTED_EDGE('',*,*,#2109,.T.);
#3152=ORIENTED_EDGE('',*,*,#2055,.T.);
#3153=ORIENTED_EDGE('',*,*,#2059,.T.);
#3154=ORIENTED_EDGE('',*,*,#2062,.T.);
#3155=ORIENTED_EDGE('',*,*,#2065,.T.);
#3156=ORIENTED_EDGE('',*,*,#2068,.T.);
#3157=ORIENTED_EDGE('',*,*,#2071,.T.);
#3158=ORIENTED_EDGE('',*,*,#2074,.T.);
#3159=ORIENTED_EDGE('',*,*,#2077,.T.);
#3160=ORIENTED_EDGE('',*,*,#2191,.F.);
#3161=ORIENTED_EDGE('',*,*,#2192,.F.);
#3162=ORIENTED_EDGE('',*,*,#2193,.F.);
#3163=ORIENTED_EDGE('',*,*,#2194,.F.);
#3164=ORIENTED_EDGE('',*,*,#2195,.F.);
#3165=ORIENTED_EDGE('',*,*,#2196,.F.);
#3166=ORIENTED_EDGE('',*,*,#2197,.F.);
#3167=ORIENTED_EDGE('',*,*,#2198,.F.);
#3168=ORIENTED_EDGE('',*,*,#2199,.T.);
#3169=ORIENTED_EDGE('',*,*,#2200,.F.);
#3170=ORIENTED_EDGE('',*,*,#2201,.F.);
#3171=ORIENTED_EDGE('',*,*,#2202,.F.);
#3172=ORIENTED_EDGE('',*,*,#2203,.F.);
#3173=ORIENTED_EDGE('',*,*,#1842,.F.);
#3174=ORIENTED_EDGE('',*,*,#2204,.F.);
#3175=ORIENTED_EDGE('',*,*,#2205,.F.);
#3176=ORIENTED_EDGE('',*,*,#2206,.F.);
#3177=ORIENTED_EDGE('',*,*,#2207,.F.);
#3178=ORIENTED_EDGE('',*,*,#2208,.F.);
#3179=ORIENTED_EDGE('',*,*,#2209,.F.);
#3180=ORIENTED_EDGE('',*,*,#2210,.F.);
#3181=ORIENTED_EDGE('',*,*,#2211,.F.);
#3182=ORIENTED_EDGE('',*,*,#2197,.T.);
#3183=ORIENTED_EDGE('',*,*,#2212,.F.);
#3184=ORIENTED_EDGE('',*,*,#2213,.T.);
#3185=ORIENTED_EDGE('',*,*,#2214,.F.);
#3186=ORIENTED_EDGE('',*,*,#2196,.T.);
#3187=ORIENTED_EDGE('',*,*,#2215,.F.);
#3188=ORIENTED_EDGE('',*,*,#2216,.T.);
#3189=ORIENTED_EDGE('',*,*,#2212,.T.);
#3190=ORIENTED_EDGE('',*,*,#2195,.T.);
#3191=ORIENTED_EDGE('',*,*,#2217,.F.);
#3192=ORIENTED_EDGE('',*,*,#2218,.T.);
#3193=ORIENTED_EDGE('',*,*,#2215,.T.);
#3194=ORIENTED_EDGE('',*,*,#2194,.T.);
#3195=ORIENTED_EDGE('',*,*,#2219,.F.);
#3196=ORIENTED_EDGE('',*,*,#2220,.T.);
#3197=ORIENTED_EDGE('',*,*,#2217,.T.);
#3198=ORIENTED_EDGE('',*,*,#2193,.T.);
#3199=ORIENTED_EDGE('',*,*,#2221,.F.);
#3200=ORIENTED_EDGE('',*,*,#2222,.T.);
#3201=ORIENTED_EDGE('',*,*,#2219,.T.);
#3202=ORIENTED_EDGE('',*,*,#2223,.T.);
#3203=ORIENTED_EDGE('',*,*,#2224,.T.);
#3204=ORIENTED_EDGE('',*,*,#2199,.F.);
#3205=ORIENTED_EDGE('',*,*,#2225,.T.);
#3206=ORIENTED_EDGE('',*,*,#2226,.T.);
#3207=ORIENTED_EDGE('',*,*,#2227,.T.);
#3208=ORIENTED_EDGE('',*,*,#2223,.F.);
#3209=ORIENTED_EDGE('',*,*,#2228,.T.);
#3210=ORIENTED_EDGE('',*,*,#2191,.T.);
#3211=ORIENTED_EDGE('',*,*,#2229,.T.);
#3212=ORIENTED_EDGE('',*,*,#2226,.F.);
#3213=ORIENTED_EDGE('',*,*,#2230,.T.);
#3214=ORIENTED_EDGE('',*,*,#2230,.F.);
#3215=ORIENTED_EDGE('',*,*,#2231,.T.);
#3216=ORIENTED_EDGE('',*,*,#2221,.T.);
#3217=ORIENTED_EDGE('',*,*,#2192,.T.);
#3218=ORIENTED_EDGE('',*,*,#2228,.F.);
#3219=ORIENTED_EDGE('',*,*,#2232,.F.);
#3220=ORIENTED_EDGE('',*,*,#2213,.F.);
#3221=ORIENTED_EDGE('',*,*,#2216,.F.);
#3222=ORIENTED_EDGE('',*,*,#2218,.F.);
#3223=ORIENTED_EDGE('',*,*,#2220,.F.);
#3224=ORIENTED_EDGE('',*,*,#2222,.F.);
#3225=ORIENTED_EDGE('',*,*,#2231,.F.);
#3226=ORIENTED_EDGE('',*,*,#2225,.F.);
#3227=ORIENTED_EDGE('',*,*,#2198,.T.);
#3228=ORIENTED_EDGE('',*,*,#2214,.T.);
#3229=ORIENTED_EDGE('',*,*,#2232,.T.);
#3230=ORIENTED_EDGE('',*,*,#2203,.T.);
#3231=ORIENTED_EDGE('',*,*,#2233,.T.);
#3232=ORIENTED_EDGE('',*,*,#2234,.T.);
#3233=ORIENTED_EDGE('',*,*,#1843,.T.);
#3234=ORIENTED_EDGE('',*,*,#2107,.T.);
#3235=ORIENTED_EDGE('',*,*,#2111,.T.);
#3236=ORIENTED_EDGE('',*,*,#2114,.T.);
#3237=ORIENTED_EDGE('',*,*,#2117,.T.);
#3238=ORIENTED_EDGE('',*,*,#2120,.T.);
#3239=ORIENTED_EDGE('',*,*,#2123,.T.);
#3240=ORIENTED_EDGE('',*,*,#2126,.T.);
#3241=ORIENTED_EDGE('',*,*,#2129,.T.);
#3242=ORIENTED_EDGE('',*,*,#2132,.T.);
#3243=ORIENTED_EDGE('',*,*,#2135,.T.);
#3244=ORIENTED_EDGE('',*,*,#2204,.T.);
#3245=ORIENTED_EDGE('',*,*,#1845,.T.);
#3246=ORIENTED_EDGE('',*,*,#2235,.T.);
#3247=ORIENTED_EDGE('',*,*,#2236,.T.);
#3248=ORIENTED_EDGE('',*,*,#2206,.T.);
#3249=ORIENTED_EDGE('',*,*,#2237,.T.);
#3250=ORIENTED_EDGE('',*,*,#2238,.T.);
#3251=ORIENTED_EDGE('',*,*,#2239,.T.);
#3252=ORIENTED_EDGE('',*,*,#2200,.T.);
#3253=ORIENTED_EDGE('',*,*,#2224,.F.);
#3254=ORIENTED_EDGE('',*,*,#2227,.F.);
#3255=ORIENTED_EDGE('',*,*,#2229,.F.);
#3256=ORIENTED_EDGE('',*,*,#2207,.T.);
#3257=ORIENTED_EDGE('',*,*,#2239,.F.);
#3258=ORIENTED_EDGE('',*,*,#2240,.F.);
#3259=ORIENTED_EDGE('',*,*,#2241,.F.);
#3260=ORIENTED_EDGE('',*,*,#2201,.T.);
#3261=ORIENTED_EDGE('',*,*,#2241,.T.);
#3262=ORIENTED_EDGE('',*,*,#2242,.T.);
#3263=ORIENTED_EDGE('',*,*,#2243,.T.);
#3264=ORIENTED_EDGE('',*,*,#2244,.T.);
#3265=ORIENTED_EDGE('',*,*,#2245,.T.);
#3266=ORIENTED_EDGE('',*,*,#2246,.T.);
#3267=ORIENTED_EDGE('',*,*,#2245,.F.);
#3268=ORIENTED_EDGE('',*,*,#2247,.T.);
#3269=ORIENTED_EDGE('',*,*,#2248,.T.);
#3270=ORIENTED_EDGE('',*,*,#2249,.T.);
#3271=ORIENTED_EDGE('',*,*,#2248,.F.);
#3272=ORIENTED_EDGE('',*,*,#2250,.T.);
#3273=ORIENTED_EDGE('',*,*,#2251,.T.);
#3274=ORIENTED_EDGE('',*,*,#2252,.T.);
#3275=ORIENTED_EDGE('',*,*,#2251,.F.);
#3276=ORIENTED_EDGE('',*,*,#2253,.T.);
#3277=ORIENTED_EDGE('',*,*,#2254,.T.);
#3278=ORIENTED_EDGE('',*,*,#2255,.T.);
#3279=ORIENTED_EDGE('',*,*,#2254,.F.);
#3280=ORIENTED_EDGE('',*,*,#2202,.T.);
#3281=ORIENTED_EDGE('',*,*,#2243,.F.);
#3282=ORIENTED_EDGE('',*,*,#2256,.F.);
#3283=ORIENTED_EDGE('',*,*,#2233,.F.);
#3284=ORIENTED_EDGE('',*,*,#2205,.T.);
#3285=ORIENTED_EDGE('',*,*,#2236,.F.);
#3286=ORIENTED_EDGE('',*,*,#2257,.F.);
#3287=ORIENTED_EDGE('',*,*,#2237,.F.);
#3288=ORIENTED_EDGE('',*,*,#2234,.F.);
#3289=ORIENTED_EDGE('',*,*,#2256,.T.);
#3290=ORIENTED_EDGE('',*,*,#2242,.F.);
#3291=ORIENTED_EDGE('',*,*,#2240,.T.);
#3292=ORIENTED_EDGE('',*,*,#2238,.F.);
#3293=ORIENTED_EDGE('',*,*,#2257,.T.);
#3294=ORIENTED_EDGE('',*,*,#2235,.F.);
#3295=ORIENTED_EDGE('',*,*,#1844,.T.);
#3296=ORIENTED_EDGE('',*,*,#2249,.F.);
#3297=ORIENTED_EDGE('',*,*,#2246,.F.);
#3298=ORIENTED_EDGE('',*,*,#2252,.F.);
#3299=ORIENTED_EDGE('',*,*,#2255,.F.);
#3300=ORIENTED_EDGE('',*,*,#2210,.T.);
#3301=ORIENTED_EDGE('',*,*,#2258,.T.);
#3302=ORIENTED_EDGE('',*,*,#2259,.T.);
#3303=ORIENTED_EDGE('',*,*,#2258,.F.);
#3304=ORIENTED_EDGE('',*,*,#2253,.F.);
#3305=ORIENTED_EDGE('',*,*,#2259,.F.);
#3306=ORIENTED_EDGE('',*,*,#2209,.T.);
#3307=ORIENTED_EDGE('',*,*,#2260,.T.);
#3308=ORIENTED_EDGE('',*,*,#2261,.T.);
#3309=ORIENTED_EDGE('',*,*,#2260,.F.);
#3310=ORIENTED_EDGE('',*,*,#2250,.F.);
#3311=ORIENTED_EDGE('',*,*,#2261,.F.);
#3312=ORIENTED_EDGE('',*,*,#2211,.T.);
#3313=ORIENTED_EDGE('',*,*,#2262,.T.);
#3314=ORIENTED_EDGE('',*,*,#2263,.T.);
#3315=ORIENTED_EDGE('',*,*,#2262,.F.);
#3316=ORIENTED_EDGE('',*,*,#2247,.F.);
#3317=ORIENTED_EDGE('',*,*,#2263,.F.);
#3318=ORIENTED_EDGE('',*,*,#2208,.T.);
#3319=ORIENTED_EDGE('',*,*,#2264,.T.);
#3320=ORIENTED_EDGE('',*,*,#2265,.T.);
#3321=ORIENTED_EDGE('',*,*,#2264,.F.);
#3322=ORIENTED_EDGE('',*,*,#2244,.F.);
#3323=ORIENTED_EDGE('',*,*,#2265,.F.);
#3324=PLANE('',#3597);
#3325=PLANE('',#3598);
#3326=PLANE('',#3599);
#3327=PLANE('',#3600);
#3328=PLANE('',#3601);
#3329=PLANE('',#3602);
#3330=PLANE('',#3603);
#3331=PLANE('',#3604);
#3332=PLANE('',#3605);
#3333=PLANE('',#3606);
#3334=PLANE('',#3607);
#3335=PLANE('',#3608);
#3336=PLANE('',#3609);
#3337=PLANE('',#3610);
#3338=PLANE('',#3611);
#3339=PLANE('',#3612);
#3340=PLANE('',#3613);
#3341=PLANE('',#3614);
#3342=PLANE('',#3615);
#3343=PLANE('',#3616);
#3344=PLANE('',#3617);
#3345=PLANE('',#3618);
#3346=PLANE('',#3619);
#3347=PLANE('',#3620);
#3348=PLANE('',#3621);
#3349=PLANE('',#3622);
#3350=PLANE('',#3623);
#3351=PLANE('',#3624);
#3352=PLANE('',#3625);
#3353=PLANE('',#3626);
#3354=PLANE('',#3627);
#3355=PLANE('',#3628);
#3356=PLANE('',#3629);
#3357=PLANE('',#3630);
#3358=PLANE('',#3631);
#3359=PLANE('',#3632);
#3360=PLANE('',#3633);
#3361=PLANE('',#3634);
#3362=PLANE('',#3635);
#3363=PLANE('',#3636);
#3364=PLANE('',#3637);
#3365=PLANE('',#3638);
#3366=PLANE('',#3639);
#3367=PLANE('',#3640);
#3368=PLANE('',#3641);
#3369=PLANE('',#3642);
#3370=PLANE('',#3643);
#3371=PLANE('',#3644);
#3372=PLANE('',#3645);
#3373=PLANE('',#3656);
#3374=PLANE('',#3658);
#3375=PLANE('',#3659);
#3376=PLANE('',#3660);
#3377=PLANE('',#3661);
#3378=PLANE('',#3673);
#3379=PLANE('',#3677);
#3380=PLANE('',#3682);
#3381=PLANE('',#3697);
#3382=PLANE('',#3698);
#3383=PLANE('',#3699);
#3384=PLANE('',#3702);
#3385=PLANE('',#3705);
#3386=PLANE('',#3708);
#3387=PLANE('',#3711);
#3388=ADVANCED_FACE('',(#436),#3324,.T.);
#3389=ADVANCED_FACE('',(#437),#3325,.T.);
#3390=ADVANCED_FACE('',(#438),#323,.T.);
#3391=ADVANCED_FACE('',(#439),#3326,.T.);
#3392=ADVANCED_FACE('',(#440),#3327,.T.);
#3393=ADVANCED_FACE('',(#441),#3328,.T.);
#3394=ADVANCED_FACE('',(#442),#3329,.T.);
#3395=ADVANCED_FACE('',(#443),#3330,.T.);
#3396=ADVANCED_FACE('',(#444),#3331,.T.);
#3397=ADVANCED_FACE('',(#445),#3332,.T.);
#3398=ADVANCED_FACE('',(#446),#3333,.T.);
#3399=ADVANCED_FACE('',(#447),#3334,.T.);
#3400=ADVANCED_FACE('',(#448),#3335,.T.);
#3401=ADVANCED_FACE('',(#449),#324,.T.);
#3402=ADVANCED_FACE('',(#450),#325,.T.);
#3403=ADVANCED_FACE('',(#451),#326,.T.);
#3404=ADVANCED_FACE('',(#452),#327,.T.);
#3405=ADVANCED_FACE('',(#453),#328,.T.);
#3406=ADVANCED_FACE('',(#454),#329,.T.);
#3407=ADVANCED_FACE('',(#455),#330,.T.);
#3408=ADVANCED_FACE('',(#456),#331,.T.);
#3409=ADVANCED_FACE('',(#457),#3336,.T.);
#3410=ADVANCED_FACE('',(#458),#3337,.T.);
#3411=ADVANCED_FACE('',(#459),#3338,.T.);
#3412=ADVANCED_FACE('',(#460),#332,.T.);
#3413=ADVANCED_FACE('',(#461),#333,.T.);
#3414=ADVANCED_FACE('',(#462),#334,.T.);
#3415=ADVANCED_FACE('',(#463),#335,.T.);
#3416=ADVANCED_FACE('',(#464),#336,.T.);
#3417=ADVANCED_FACE('',(#465),#337,.T.);
#3418=ADVANCED_FACE('',(#466),#338,.T.);
#3419=ADVANCED_FACE('',(#467),#339,.T.);
#3420=ADVANCED_FACE('',(#468),#340,.T.);
#3421=ADVANCED_FACE('',(#469),#341,.T.);
#3422=ADVANCED_FACE('',(#470),#3339,.T.);
#3423=ADVANCED_FACE('',(#471),#3340,.T.);
#3424=ADVANCED_FACE('',(#472,#74),#3341,.T.);
#3425=ADVANCED_FACE('',(#473,#75,#76,#77,#78,#79,#80),#3342,.T.);
#3426=ADVANCED_FACE('',(#474),#3343,.T.);
#3427=ADVANCED_FACE('',(#475),#342,.T.);
#3428=ADVANCED_FACE('',(#476),#343,.T.);
#3429=ADVANCED_FACE('',(#477),#344,.T.);
#3430=ADVANCED_FACE('',(#478),#345,.T.);
#3431=ADVANCED_FACE('',(#479),#346,.T.);
#3432=ADVANCED_FACE('',(#480),#3344,.T.);
#3433=ADVANCED_FACE('',(#481),#3345,.T.);
#3434=ADVANCED_FACE('',(#482),#3346,.T.);
#3435=ADVANCED_FACE('',(#483),#3347,.T.);
#3436=ADVANCED_FACE('',(#484),#347,.T.);
#3437=ADVANCED_FACE('',(#485),#348,.T.);
#3438=ADVANCED_FACE('',(#486),#349,.T.);
#3439=ADVANCED_FACE('',(#487),#350,.T.);
#3440=ADVANCED_FACE('',(#488),#351,.T.);
#3441=ADVANCED_FACE('',(#489),#352,.T.);
#3442=ADVANCED_FACE('',(#490),#3348,.T.);
#3443=ADVANCED_FACE('',(#491),#3349,.T.);
#3444=ADVANCED_FACE('',(#492),#3350,.T.);
#3445=ADVANCED_FACE('',(#493,#81),#3351,.T.);
#3446=ADVANCED_FACE('',(#494),#3352,.T.);
#3447=ADVANCED_FACE('',(#495),#353,.T.);
#3448=ADVANCED_FACE('',(#496),#354,.T.);
#3449=ADVANCED_FACE('',(#497),#355,.T.);
#3450=ADVANCED_FACE('',(#498),#356,.T.);
#3451=ADVANCED_FACE('',(#499),#357,.T.);
#3452=ADVANCED_FACE('',(#500),#358,.T.);
#3453=ADVANCED_FACE('',(#501),#359,.T.);
#3454=ADVANCED_FACE('',(#502),#360,.T.);
#3455=ADVANCED_FACE('',(#503),#361,.T.);
#3456=ADVANCED_FACE('',(#504),#3353,.T.);
#3457=ADVANCED_FACE('',(#505),#362,.T.);
#3458=ADVANCED_FACE('',(#506),#363,.T.);
#3459=ADVANCED_FACE('',(#507),#364,.T.);
#3460=ADVANCED_FACE('',(#508),#365,.T.);
#3461=ADVANCED_FACE('',(#509),#366,.T.);
#3462=ADVANCED_FACE('',(#510),#367,.T.);
#3463=ADVANCED_FACE('',(#511),#368,.T.);
#3464=ADVANCED_FACE('',(#512),#369,.T.);
#3465=ADVANCED_FACE('',(#513),#370,.T.);
#3466=ADVANCED_FACE('',(#514),#371,.T.);
#3467=ADVANCED_FACE('',(#515),#372,.T.);
#3468=ADVANCED_FACE('',(#516),#373,.T.);
#3469=ADVANCED_FACE('',(#517),#374,.T.);
#3470=ADVANCED_FACE('',(#518),#375,.T.);
#3471=ADVANCED_FACE('',(#519),#3354,.T.);
#3472=ADVANCED_FACE('',(#520),#376,.T.);
#3473=ADVANCED_FACE('',(#521),#377,.T.);
#3474=ADVANCED_FACE('',(#522),#378,.T.);
#3475=ADVANCED_FACE('',(#523),#379,.T.);
#3476=ADVANCED_FACE('',(#524),#380,.T.);
#3477=ADVANCED_FACE('',(#525),#381,.T.);
#3478=ADVANCED_FACE('',(#526),#382,.T.);
#3479=ADVANCED_FACE('',(#527),#3355,.T.);
#3480=ADVANCED_FACE('',(#528),#383,.T.);
#3481=ADVANCED_FACE('',(#529),#384,.T.);
#3482=ADVANCED_FACE('',(#530),#3356,.T.);
#3483=ADVANCED_FACE('',(#531),#385,.T.);
#3484=ADVANCED_FACE('',(#532,#82),#3357,.T.);
#3485=ADVANCED_FACE('',(#533),#3358,.T.);
#3486=ADVANCED_FACE('',(#534),#386,.T.);
#3487=ADVANCED_FACE('',(#535),#387,.T.);
#3488=ADVANCED_FACE('',(#536),#388,.T.);
#3489=ADVANCED_FACE('',(#537),#389,.T.);
#3490=ADVANCED_FACE('',(#538),#390,.T.);
#3491=ADVANCED_FACE('',(#539),#391,.T.);
#3492=ADVANCED_FACE('',(#540),#392,.T.);
#3493=ADVANCED_FACE('',(#541),#393,.T.);
#3494=ADVANCED_FACE('',(#542),#3359,.T.);
#3495=ADVANCED_FACE('',(#543),#394,.T.);
#3496=ADVANCED_FACE('',(#544),#395,.T.);
#3497=ADVANCED_FACE('',(#545),#396,.T.);
#3498=ADVANCED_FACE('',(#546),#397,.T.);
#3499=ADVANCED_FACE('',(#547),#398,.T.);
#3500=ADVANCED_FACE('',(#548),#399,.T.);
#3501=ADVANCED_FACE('',(#549),#400,.T.);
#3502=ADVANCED_FACE('',(#550),#401,.T.);
#3503=ADVANCED_FACE('',(#551),#402,.T.);
#3504=ADVANCED_FACE('',(#552),#3360,.T.);
#3505=ADVANCED_FACE('',(#553),#3361,.T.);
#3506=ADVANCED_FACE('',(#554),#3362,.T.);
#3507=ADVANCED_FACE('',(#555),#3363,.T.);
#3508=ADVANCED_FACE('',(#556,#83),#3364,.T.);
#3509=ADVANCED_FACE('',(#557),#3365,.T.);
#3510=ADVANCED_FACE('',(#558),#403,.T.);
#3511=ADVANCED_FACE('',(#559),#404,.T.);
#3512=ADVANCED_FACE('',(#560),#405,.T.);
#3513=ADVANCED_FACE('',(#561),#406,.T.);
#3514=ADVANCED_FACE('',(#562),#407,.T.);
#3515=ADVANCED_FACE('',(#563),#408,.T.);
#3516=ADVANCED_FACE('',(#564),#409,.T.);
#3517=ADVANCED_FACE('',(#565),#410,.T.);
#3518=ADVANCED_FACE('',(#566),#411,.T.);
#3519=ADVANCED_FACE('',(#567),#3366,.T.);
#3520=ADVANCED_FACE('',(#568),#412,.T.);
#3521=ADVANCED_FACE('',(#569),#413,.T.);
#3522=ADVANCED_FACE('',(#570),#414,.T.);
#3523=ADVANCED_FACE('',(#571),#415,.T.);
#3524=ADVANCED_FACE('',(#572),#416,.T.);
#3525=ADVANCED_FACE('',(#573),#417,.T.);
#3526=ADVANCED_FACE('',(#574),#418,.T.);
#3527=ADVANCED_FACE('',(#575),#419,.T.);
#3528=ADVANCED_FACE('',(#576),#420,.T.);
#3529=ADVANCED_FACE('',(#577),#421,.T.);
#3530=ADVANCED_FACE('',(#578),#422,.T.);
#3531=ADVANCED_FACE('',(#579),#423,.T.);
#3532=ADVANCED_FACE('',(#580),#424,.T.);
#3533=ADVANCED_FACE('',(#581),#425,.T.);
#3534=ADVANCED_FACE('',(#582),#3367,.T.);
#3535=ADVANCED_FACE('',(#583),#426,.T.);
#3536=ADVANCED_FACE('',(#584),#427,.T.);
#3537=ADVANCED_FACE('',(#585),#428,.T.);
#3538=ADVANCED_FACE('',(#586),#429,.T.);
#3539=ADVANCED_FACE('',(#587),#430,.T.);
#3540=ADVANCED_FACE('',(#588),#431,.T.);
#3541=ADVANCED_FACE('',(#589),#432,.T.);
#3542=ADVANCED_FACE('',(#590),#3368,.T.);
#3543=ADVANCED_FACE('',(#591),#433,.T.);
#3544=ADVANCED_FACE('',(#592),#434,.T.);
#3545=ADVANCED_FACE('',(#593),#3369,.T.);
#3546=ADVANCED_FACE('',(#594),#435,.T.);
#3547=ADVANCED_FACE('',(#595,#84),#3370,.T.);
#3548=ADVANCED_FACE('',(#596),#3371,.T.);
#3549=ADVANCED_FACE('',(#597,#85,#86,#87,#88),#3372,.T.);
#3550=ADVANCED_FACE('',(#598),#3373,.T.);
#3551=ADVANCED_FACE('',(#599),#3374,.T.);
#3552=ADVANCED_FACE('',(#600),#3375,.T.);
#3553=ADVANCED_FACE('',(#601),#3376,.T.);
#3554=ADVANCED_FACE('',(#602),#3377,.T.);
#3555=ADVANCED_FACE('',(#603),#38,.T.);
#3556=ADVANCED_FACE('',(#604),#23,.T.);
#3557=ADVANCED_FACE('',(#605),#39,.T.);
#3558=ADVANCED_FACE('',(#606),#24,.F.);
#3559=ADVANCED_FACE('',(#607),#3378,.T.);
#3560=ADVANCED_FACE('',(#608),#25,.F.);
#3561=ADVANCED_FACE('',(#609),#26,.T.);
#3562=ADVANCED_FACE('',(#610),#3379,.T.);
#3563=ADVANCED_FACE('',(#611),#27,.T.);
#3564=ADVANCED_FACE('',(#612),#28,.T.);
#3565=ADVANCED_FACE('',(#613),#3380,.T.);
#3566=ADVANCED_FACE('',(#614),#29,.T.);
#3567=ADVANCED_FACE('',(#615),#30,.F.);
#3568=ADVANCED_FACE('',(#616),#31,.F.);
#3569=ADVANCED_FACE('',(#617),#32,.F.);
#3570=ADVANCED_FACE('',(#618),#33,.F.);
#3571=ADVANCED_FACE('',(#619),#3381,.T.);
#3572=ADVANCED_FACE('',(#620),#3382,.T.);
#3573=ADVANCED_FACE('',(#621,#89,#90,#91,#92),#3383,.F.);
#3574=ADVANCED_FACE('',(#622),#34,.F.);
#3575=ADVANCED_FACE('',(#623,#93),#3384,.F.);
#3576=ADVANCED_FACE('',(#624),#35,.F.);
#3577=ADVANCED_FACE('',(#625,#94),#3385,.F.);
#3578=ADVANCED_FACE('',(#626),#36,.F.);
#3579=ADVANCED_FACE('',(#627,#95),#3386,.F.);
#3580=ADVANCED_FACE('',(#628),#37,.F.);
#3581=ADVANCED_FACE('',(#629,#96),#3387,.F.);
#3582=CLOSED_SHELL('',(#3388,#3389,#3390,#3391,#3392,#3393,#3394,#3395,
#3396,#3397,#3398,#3399,#3400,#3401,#3402,#3403,#3404,#3405,#3406,#3407,
#3408,#3409,#3410,#3411,#3412,#3413,#3414,#3415,#3416,#3417,#3418,#3419,
#3420,#3421,#3422,#3423,#3424,#3425,#3426,#3427,#3428,#3429,#3430,#3431,
#3432,#3433,#3434,#3435,#3436,#3437,#3438,#3439,#3440,#3441,#3442,#3443,
#3444,#3445,#3446,#3447,#3448,#3449,#3450,#3451,#3452,#3453,#3454,#3455,
#3456,#3457,#3458,#3459,#3460,#3461,#3462,#3463,#3464,#3465,#3466,#3467,
#3468,#3469,#3470,#3471,#3472,#3473,#3474,#3475,#3476,#3477,#3478,#3479,
#3480,#3481,#3482,#3483,#3484,#3485,#3486,#3487,#3488,#3489,#3490,#3491,
#3492,#3493,#3494,#3495,#3496,#3497,#3498,#3499,#3500,#3501,#3502,#3503,
#3504,#3505,#3506,#3507,#3508,#3509,#3510,#3511,#3512,#3513,#3514,#3515,
#3516,#3517,#3518,#3519,#3520,#3521,#3522,#3523,#3524,#3525,#3526,#3527,
#3528,#3529,#3530,#3531,#3532,#3533,#3534,#3535,#3536,#3537,#3538,#3539,
#3540,#3541,#3542,#3543,#3544,#3545,#3546,#3547,#3548,#3549,#3550,#3551,
#3552,#3553,#3554,#3555,#3556,#3557,#3558,#3559,#3560,#3561,#3562,#3563,
#3564,#3565,#3566,#3567,#3568,#3569,#3570,#3571,#3572,#3573,#3574,#3575,
#3576,#3577,#3578,#3579,#3580,#3581));
#3583=DERIVED_UNIT_ELEMENT(#3585,1.);
#3584=DERIVED_UNIT_ELEMENT(#7228,-3.);
#3585=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#3586=DERIVED_UNIT((#3583,#3584));
#3587=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#3586);
#3588=PROPERTY_DEFINITION_REPRESENTATION(#3593,#3590);
#3589=PROPERTY_DEFINITION_REPRESENTATION(#3594,#3591);
#3590=REPRESENTATION('material name',(#3592),#7225);
#3591=REPRESENTATION('density',(#3587),#7225);
#3592=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#3593=PROPERTY_DEFINITION('material property','material name',#7240);
#3594=PROPERTY_DEFINITION('material property','density of part',#7240);
#3595=AXIS2_PLACEMENT_3D('',#4221,#3714,#3715);
#3596=AXIS2_PLACEMENT_3D('',#4222,#3716,#3717);
#3597=AXIS2_PLACEMENT_3D('',#4223,#3718,#3719);
#3598=AXIS2_PLACEMENT_3D('',#4232,#3724,#3725);
#3599=AXIS2_PLACEMENT_3D('',#4261,#3730,#3731);
#3600=AXIS2_PLACEMENT_3D('',#4267,#3735,#3736);
#3601=AXIS2_PLACEMENT_3D('',#4273,#3740,#3741);
#3602=AXIS2_PLACEMENT_3D('',#4279,#3745,#3746);
#3603=AXIS2_PLACEMENT_3D('',#4285,#3750,#3751);
#3604=AXIS2_PLACEMENT_3D('',#4291,#3755,#3756);
#3605=AXIS2_PLACEMENT_3D('',#4297,#3760,#3761);
#3606=AXIS2_PLACEMENT_3D('',#4303,#3765,#3766);
#3607=AXIS2_PLACEMENT_3D('',#4309,#3770,#3771);
#3608=AXIS2_PLACEMENT_3D('',#4312,#3774,#3775);
#3609=AXIS2_PLACEMENT_3D('',#4497,#3784,#3785);
#3610=AXIS2_PLACEMENT_3D('',#4506,#3790,#3791);
#3611=AXIS2_PLACEMENT_3D('',#4512,#3795,#3796);
#3612=AXIS2_PLACEMENT_3D('',#4748,#3810,#3811);
#3613=AXIS2_PLACEMENT_3D('',#4754,#3815,#3816);
#3614=AXIS2_PLACEMENT_3D('',#4757,#3819,#3820);
#3615=AXIS2_PLACEMENT_3D('',#4758,#3821,#3822);
#3616=AXIS2_PLACEMENT_3D('',#5177,#3843,#3844);
#3617=AXIS2_PLACEMENT_3D('',#5301,#3854,#3855);
#3618=AXIS2_PLACEMENT_3D('',#5307,#3859,#3860);
#3619=AXIS2_PLACEMENT_3D('',#5310,#3863,#3864);
#3620=AXIS2_PLACEMENT_3D('',#5316,#3868,#3869);
#3621=AXIS2_PLACEMENT_3D('',#5422,#3878,#3879);
#3622=AXIS2_PLACEMENT_3D('',#5426,#3882,#3883);
#3623=AXIS2_PLACEMENT_3D('',#5430,#3886,#3887);
#3624=AXIS2_PLACEMENT_3D('',#5432,#3889,#3890);
#3625=AXIS2_PLACEMENT_3D('',#5433,#3891,#3892);
#3626=AXIS2_PLACEMENT_3D('',#5644,#3903,#3904);
#3627=AXIS2_PLACEMENT_3D('',#5887,#3922,#3923);
#3628=AXIS2_PLACEMENT_3D('',#6010,#3933,#3934);
#3629=AXIS2_PLACEMENT_3D('',#6048,#3939,#3940);
#3630=AXIS2_PLACEMENT_3D('',#6067,#3943,#3944);
#3631=AXIS2_PLACEMENT_3D('',#6068,#3945,#3946);
#3632=AXIS2_PLACEMENT_3D('',#6253,#3955,#3956);
#3633=AXIS2_PLACEMENT_3D('',#6412,#3969,#3970);
#3634=AXIS2_PLACEMENT_3D('',#6416,#3973,#3974);
#3635=AXIS2_PLACEMENT_3D('',#6420,#3977,#3978);
#3636=AXIS2_PLACEMENT_3D('',#6424,#3981,#3982);
#3637=AXIS2_PLACEMENT_3D('',#6426,#3984,#3985);
#3638=AXIS2_PLACEMENT_3D('',#6427,#3986,#3987);
#3639=AXIS2_PLACEMENT_3D('',#6638,#3998,#3999);
#3640=AXIS2_PLACEMENT_3D('',#6881,#4017,#4018);
#3641=AXIS2_PLACEMENT_3D('',#7004,#4028,#4029);
#3642=AXIS2_PLACEMENT_3D('',#7042,#4034,#4035);
#3643=AXIS2_PLACEMENT_3D('',#7061,#4038,#4039);
#3644=AXIS2_PLACEMENT_3D('',#7062,#4040,#4041);
#3645=AXIS2_PLACEMENT_3D('',#7063,#4042,#4043);
#3646=AXIS2_PLACEMENT_3D('',#7066,#4044,#4045);
#3647=AXIS2_PLACEMENT_3D('',#7082,#4053,#4054);
#3648=AXIS2_PLACEMENT_3D('',#7086,#4056,#4057);
#3649=AXIS2_PLACEMENT_3D('',#7089,#4059,#4060);
#3650=AXIS2_PLACEMENT_3D('',#7091,#4061,#4062);
#3651=AXIS2_PLACEMENT_3D('',#7095,#4064,#4065);
#3652=AXIS2_PLACEMENT_3D('',#7098,#4067,#4068);
#3653=AXIS2_PLACEMENT_3D('',#7100,#4069,#4070);
#3654=AXIS2_PLACEMENT_3D('',#7102,#4071,#4072);
#3655=AXIS2_PLACEMENT_3D('',#7104,#4073,#4074);
#3656=AXIS2_PLACEMENT_3D('',#7105,#4075,#4076);
#3657=AXIS2_PLACEMENT_3D('',#7110,#4079,#4080);
#3658=AXIS2_PLACEMENT_3D('',#7111,#4081,#4082);
#3659=AXIS2_PLACEMENT_3D('',#7115,#4085,#4086);
#3660=AXIS2_PLACEMENT_3D('',#7119,#4089,#4090);
#3661=AXIS2_PLACEMENT_3D('',#7123,#4093,#4094);
#3662=AXIS2_PLACEMENT_3D('',#7125,#4095,#4096);
#3663=AXIS2_PLACEMENT_3D('',#7127,#4098,#4099);
#3664=AXIS2_PLACEMENT_3D('',#7130,#4100,#4101);
#3665=AXIS2_PLACEMENT_3D('',#7131,#4102,#4103);
#3666=AXIS2_PLACEMENT_3D('',#7132,#4104,#4105);
#3667=AXIS2_PLACEMENT_3D('',#7133,#4106,#4107);
#3668=AXIS2_PLACEMENT_3D('',#7136,#4108,#4109);
#3669=AXIS2_PLACEMENT_3D('',#7139,#4112,#4113);
#3670=AXIS2_PLACEMENT_3D('',#7140,#4114,#4115);
#3671=AXIS2_PLACEMENT_3D('',#7141,#4116,#4117);
#3672=AXIS2_PLACEMENT_3D('',#7142,#4118,#4119);
#3673=AXIS2_PLACEMENT_3D('',#7144,#4121,#4122);
#3674=AXIS2_PLACEMENT_3D('',#7146,#4124,#4125);
#3675=AXIS2_PLACEMENT_3D('',#7147,#4126,#4127);
#3676=AXIS2_PLACEMENT_3D('',#7150,#4129,#4130);
#3677=AXIS2_PLACEMENT_3D('',#7151,#4131,#4132);
#3678=AXIS2_PLACEMENT_3D('',#7152,#4133,#4134);
#3679=AXIS2_PLACEMENT_3D('',#7154,#4135,#4136);
#3680=AXIS2_PLACEMENT_3D('',#7156,#4138,#4139);
#3681=AXIS2_PLACEMENT_3D('',#7160,#4141,#4142);
#3682=AXIS2_PLACEMENT_3D('',#7162,#4144,#4145);
#3683=AXIS2_PLACEMENT_3D('',#7166,#4148,#4149);
#3684=AXIS2_PLACEMENT_3D('',#7168,#4150,#4151);
#3685=AXIS2_PLACEMENT_3D('',#7170,#4153,#4154);
#3686=AXIS2_PLACEMENT_3D('',#7172,#4155,#4156);
#3687=AXIS2_PLACEMENT_3D('',#7175,#4158,#4159);
#3688=AXIS2_PLACEMENT_3D('',#7176,#4160,#4161);
#3689=AXIS2_PLACEMENT_3D('',#7178,#4162,#4163);
#3690=AXIS2_PLACEMENT_3D('',#7181,#4165,#4166);
#3691=AXIS2_PLACEMENT_3D('',#7182,#4167,#4168);
#3692=AXIS2_PLACEMENT_3D('',#7184,#4169,#4170);
#3693=AXIS2_PLACEMENT_3D('',#7187,#4172,#4173);
#3694=AXIS2_PLACEMENT_3D('',#7188,#4174,#4175);
#3695=AXIS2_PLACEMENT_3D('',#7190,#4176,#4177);
#3696=AXIS2_PLACEMENT_3D('',#7193,#4179,#4180);
#3697=AXIS2_PLACEMENT_3D('',#7194,#4181,#4182);
#3698=AXIS2_PLACEMENT_3D('',#7196,#4184,#4185);
#3699=AXIS2_PLACEMENT_3D('',#7198,#4187,#4188);
#3700=AXIS2_PLACEMENT_3D('',#7199,#4189,#4190);
#3701=AXIS2_PLACEMENT_3D('',#7202,#4192,#4193);
#3702=AXIS2_PLACEMENT_3D('',#7203,#4194,#4195);
#3703=AXIS2_PLACEMENT_3D('',#7204,#4196,#4197);
#3704=AXIS2_PLACEMENT_3D('',#7207,#4199,#4200);
#3705=AXIS2_PLACEMENT_3D('',#7208,#4201,#4202);
#3706=AXIS2_PLACEMENT_3D('',#7209,#4203,#4204);
#3707=AXIS2_PLACEMENT_3D('',#7212,#4206,#4207);
#3708=AXIS2_PLACEMENT_3D('',#7213,#4208,#4209);
#3709=AXIS2_PLACEMENT_3D('',#7214,#4210,#4211);
#3710=AXIS2_PLACEMENT_3D('',#7217,#4213,#4214);
#3711=AXIS2_PLACEMENT_3D('',#7218,#4215,#4216);
#3712=AXIS2_PLACEMENT_3D('',#7219,#4217,#4218);
#3713=AXIS2_PLACEMENT_3D('',#7220,#4219,#4220);
#3714=DIRECTION('axis',(0.,0.,1.));
#3715=DIRECTION('refdir',(1.,0.,0.));
#3716=DIRECTION('axis',(0.,0.,1.));
#3717=DIRECTION('refdir',(1.,0.,0.));
#3718=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011466051));
#3719=DIRECTION('ref_axis',(-0.00600991011466051,0.,-0.999981940327131));
#3720=DIRECTION('',(0.00600991011466051,0.,0.999981940327131));
#3721=DIRECTION('',(0.,-1.,0.));
#3722=DIRECTION('',(-0.00600991011466051,0.,-0.999981940327131));
#3723=DIRECTION('',(0.,-1.,0.));
#3724=DIRECTION('center_axis',(-0.777182074633809,0.,0.629275792373971));
#3725=DIRECTION('ref_axis',(-0.629275792373971,0.,-0.777182074633809));
#3726=DIRECTION('',(0.629275792373971,0.,0.777182074633809));
#3727=DIRECTION('',(-0.629275792373971,0.,-0.777182074633809));
#3728=DIRECTION('',(0.,-1.,0.));
#3729=DIRECTION('',(0.,-1.,0.));
#3730=DIRECTION('center_axis',(-0.00600991011465974,0.,-0.999981940327131));
#3731=DIRECTION('ref_axis',(0.999981940327131,0.,-0.00600991011465974));
#3732=DIRECTION('',(-0.999981940327131,0.,0.00600991011465974));
#3733=DIRECTION('',(0.999981940327131,0.,-0.00600991011465974));
#3734=DIRECTION('',(0.,-1.,0.));
#3735=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011465613));
#3736=DIRECTION('ref_axis',(-0.00600991011465613,0.,-0.999981940327131));
#3737=DIRECTION('',(0.00600991011465613,0.,0.999981940327131));
#3738=DIRECTION('',(-0.00600991011465613,0.,-0.999981940327131));
#3739=DIRECTION('',(0.,-1.,0.));
#3740=DIRECTION('center_axis',(0.00600991011465975,0.,0.999981940327131));
#3741=DIRECTION('ref_axis',(-0.999981940327131,0.,0.00600991011465975));
#3742=DIRECTION('',(0.999981940327131,0.,-0.00600991011465975));
#3743=DIRECTION('',(-0.999981940327131,0.,0.00600991011465975));
#3744=DIRECTION('',(0.,-1.,0.));
#3745=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011466083));
#3746=DIRECTION('ref_axis',(0.00600991011466083,0.,0.999981940327131));
#3747=DIRECTION('',(-0.00600991011466083,0.,-0.999981940327131));
#3748=DIRECTION('',(0.00600991011466083,0.,0.999981940327131));
#3749=DIRECTION('',(0.,-1.,0.));
#3750=DIRECTION('center_axis',(0.773557023910856,0.,-0.633726700367105));
#3751=DIRECTION('ref_axis',(0.633726700367105,0.,0.773557023910856));
#3752=DIRECTION('',(-0.633726700367105,0.,-0.773557023910856));
#3753=DIRECTION('',(0.633726700367105,0.,0.773557023910856));
#3754=DIRECTION('',(0.,-1.,0.));
#3755=DIRECTION('center_axis',(0.752714725973157,0.,-0.658346824480194));
#3756=DIRECTION('ref_axis',(0.658346824480194,0.,0.752714725973157));
#3757=DIRECTION('',(-0.658346824480194,0.,-0.752714725973157));
#3758=DIRECTION('',(0.658346824480193,0.,0.752714725973157));
#3759=DIRECTION('',(0.,-1.,0.));
#3760=DIRECTION('center_axis',(0.00600991011465985,0.,0.999981940327131));
#3761=DIRECTION('ref_axis',(-0.999981940327131,0.,0.00600991011465985));
#3762=DIRECTION('',(0.999981940327131,0.,-0.00600991011465985));
#3763=DIRECTION('',(-0.999981940327131,0.,0.00600991011465985));
#3764=DIRECTION('',(0.,-1.,0.));
#3765=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011466083));
#3766=DIRECTION('ref_axis',(0.00600991011466083,0.,0.999981940327131));
#3767=DIRECTION('',(-0.00600991011466083,0.,-0.999981940327131));
#3768=DIRECTION('',(0.00600991011466083,0.,0.999981940327131));
#3769=DIRECTION('',(0.,-1.,0.));
#3770=DIRECTION('center_axis',(-0.00600991011465982,0.,-0.999981940327131));
#3771=DIRECTION('ref_axis',(0.999981940327131,0.,-0.00600991011465982));
#3772=DIRECTION('',(-0.999981940327131,0.,0.00600991011465982));
#3773=DIRECTION('',(0.999981940327131,0.,-0.00600991011465982));
#3774=DIRECTION('center_axis',(0.,-1.,0.));
#3775=DIRECTION('ref_axis',(1.,0.,0.));
#3776=DIRECTION('',(0.,-1.,0.));
#3777=DIRECTION('',(0.,-1.,0.));
#3778=DIRECTION('',(0.,-1.,0.));
#3779=DIRECTION('',(0.,-1.,0.));
#3780=DIRECTION('',(0.,-1.,0.));
#3781=DIRECTION('',(0.,-1.,0.));
#3782=DIRECTION('',(0.,-1.,0.));
#3783=DIRECTION('',(0.,-1.,0.));
#3784=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011465964));
#3785=DIRECTION('ref_axis',(-0.00600991011465964,0.,-0.999981940327131));
#3786=DIRECTION('',(0.00600991011465964,0.,0.999981940327131));
#3787=DIRECTION('',(0.,-1.,0.));
#3788=DIRECTION('',(-0.00600991011465964,0.,-0.999981940327131));
#3789=DIRECTION('',(0.,-1.,0.));
#3790=DIRECTION('center_axis',(0.0060099101146611,0.,0.999981940327131));
#3791=DIRECTION('ref_axis',(-0.999981940327131,0.,0.0060099101146611));
#3792=DIRECTION('',(0.999981940327131,0.,-0.0060099101146611));
#3793=DIRECTION('',(-0.999981940327131,0.,0.0060099101146611));
#3794=DIRECTION('',(0.,-1.,0.));
#3795=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011465723));
#3796=DIRECTION('ref_axis',(0.00600991011465723,0.,0.999981940327131));
#3797=DIRECTION('',(-0.00600991011465723,0.,-0.999981940327131));
#3798=DIRECTION('',(0.00600991011465723,0.,0.999981940327131));
#3799=DIRECTION('',(0.,-1.,0.));
#3800=DIRECTION('',(0.,-1.,0.));
#3801=DIRECTION('',(0.,-1.,0.));
#3802=DIRECTION('',(0.,-1.,0.));
#3803=DIRECTION('',(0.,-1.,0.));
#3804=DIRECTION('',(0.,-1.,0.));
#3805=DIRECTION('',(0.,-1.,0.));
#3806=DIRECTION('',(0.,-1.,0.));
#3807=DIRECTION('',(0.,-1.,0.));
#3808=DIRECTION('',(0.,-1.,0.));
#3809=DIRECTION('',(0.,-1.,0.));
#3810=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011465999));
#3811=DIRECTION('ref_axis',(0.00600991011465999,0.,0.999981940327131));
#3812=DIRECTION('',(-0.00600991011465999,0.,-0.999981940327131));
#3813=DIRECTION('',(0.00600991011465999,0.,0.999981940327131));
#3814=DIRECTION('',(0.,-1.,0.));
#3815=DIRECTION('center_axis',(-0.00600991011465946,0.,-0.999981940327131));
#3816=DIRECTION('ref_axis',(0.999981940327131,0.,-0.00600991011465946));
#3817=DIRECTION('',(-0.999981940327131,0.,0.00600991011465946));
#3818=DIRECTION('',(0.999981940327131,0.,-0.00600991011465946));
#3819=DIRECTION('center_axis',(0.,-1.,0.));
#3820=DIRECTION('ref_axis',(1.,0.,0.));
#3821=DIRECTION('center_axis',(0.,-1.,0.));
#3822=DIRECTION('ref_axis',(1.,0.,0.));
#3823=DIRECTION('',(-1.,0.,0.));
#3824=DIRECTION('',(0.,0.,1.));
#3825=DIRECTION('',(-1.,0.,0.));
#3826=DIRECTION('',(0.,0.,-1.));
#3827=DIRECTION('',(0.00600991011465918,0.,0.999981940327131));
#3828=DIRECTION('',(0.999981940327131,0.,-0.00600991011465975));
#3829=DIRECTION('',(-0.999981940327131,0.,0.0060099101146603));
#3830=DIRECTION('',(-0.00600991011465896,0.,-0.999981940327131));
#3831=DIRECTION('',(-0.999981940327131,0.,0.0060099101146597));
#3832=DIRECTION('',(0.00600991011465961,0.,0.999981940327131));
#3833=DIRECTION('',(0.00600991011465956,0.,0.999981940327131));
#3834=DIRECTION('',(0.999981940327131,0.,-0.00600991011465929));
#3835=DIRECTION('',(-0.00600991011465932,0.,-0.999981940327131));
#3836=DIRECTION('',(-0.999981940327131,0.,0.00600991011466022));
#3837=DIRECTION('',(-0.990000244039791,0.,0.141065647133366));
#3838=DIRECTION('',(-0.00600991011466043,0.,-0.999981940327131));
#3839=DIRECTION('',(-0.999981940327131,0.,0.0060099101146603));
#3840=DIRECTION('',(-0.990000232319715,0.,0.141065729384956));
#3841=DIRECTION('',(-0.00600991011465873,0.,-0.999981940327131));
#3842=DIRECTION('',(-0.999981940327131,0.,0.00600991011465938));
#3843=DIRECTION('center_axis',(0.00600991011465984,0.,0.999981940327131));
#3844=DIRECTION('ref_axis',(-0.999981940327131,0.,0.00600991011465984));
#3845=DIRECTION('',(0.999981940327131,0.,-0.00600991011465984));
#3846=DIRECTION('',(0.,-1.,0.));
#3847=DIRECTION('',(-0.999981940327131,0.,0.00600991011465984));
#3848=DIRECTION('',(0.,-1.,0.));
#3849=DIRECTION('',(0.,-1.,0.));
#3850=DIRECTION('',(0.,-1.,0.));
#3851=DIRECTION('',(0.,-1.,0.));
#3852=DIRECTION('',(0.,-1.,0.));
#3853=DIRECTION('',(0.,-1.,0.));
#3854=DIRECTION('center_axis',(-0.00600991011465968,0.,-0.999981940327131));
#3855=DIRECTION('ref_axis',(0.999981940327131,0.,-0.00600991011465968));
#3856=DIRECTION('',(-0.999981940327131,0.,0.00600991011465968));
#3857=DIRECTION('',(0.999981940327131,0.,-0.00600991011465968));
#3858=DIRECTION('',(0.,-1.,0.));
#3859=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011465908));
#3860=DIRECTION('ref_axis',(0.00600991011465908,0.,0.999981940327131));
#3861=DIRECTION('',(-0.00600991011465908,0.,-0.999981940327131));
#3862=DIRECTION('',(0.00600991011465908,0.,0.999981940327131));
#3863=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011465918));
#3864=DIRECTION('ref_axis',(-0.00600991011465918,0.,-0.999981940327131));
#3865=DIRECTION('',(0.,-1.,0.));
#3866=DIRECTION('',(-0.00600991011465918,0.,-0.999981940327131));
#3867=DIRECTION('',(0.,-1.,0.));
#3868=DIRECTION('center_axis',(0.00600991011465975,0.,0.999981940327131));
#3869=DIRECTION('ref_axis',(-0.999981940327131,0.,0.00600991011465975));
#3870=DIRECTION('',(-0.999981940327131,0.,0.00600991011465975));
#3871=DIRECTION('',(0.,-1.,0.));
#3872=DIRECTION('',(0.,-1.,0.));
#3873=DIRECTION('',(0.,-1.,0.));
#3874=DIRECTION('',(0.,-1.,0.));
#3875=DIRECTION('',(0.,-1.,0.));
#3876=DIRECTION('',(0.,-1.,0.));
#3877=DIRECTION('',(0.,-1.,0.));
#3878=DIRECTION('center_axis',(-0.0060099101146603,0.,-0.999981940327131));
#3879=DIRECTION('ref_axis',(0.999981940327131,0.,-0.0060099101146603));
#3880=DIRECTION('',(0.999981940327131,0.,-0.0060099101146603));
#3881=DIRECTION('',(0.,-1.,0.));
#3882=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011465896));
#3883=DIRECTION('ref_axis',(0.00600991011465896,0.,0.999981940327131));
#3884=DIRECTION('',(0.00600991011465896,0.,0.999981940327131));
#3885=DIRECTION('',(0.,-1.,0.));
#3886=DIRECTION('center_axis',(-0.0060099101146597,0.,-0.999981940327131));
#3887=DIRECTION('ref_axis',(0.999981940327131,0.,-0.0060099101146597));
#3888=DIRECTION('',(0.999981940327131,0.,-0.0060099101146597));
#3889=DIRECTION('center_axis',(0.,-1.,0.));
#3890=DIRECTION('ref_axis',(1.,0.,0.));
#3891=DIRECTION('center_axis',(0.,-1.,0.));
#3892=DIRECTION('ref_axis',(1.,0.,0.));
#3893=DIRECTION('',(0.,-1.,0.));
#3894=DIRECTION('',(0.,-1.,0.));
#3895=DIRECTION('',(0.,-1.,0.));
#3896=DIRECTION('',(0.,-1.,0.));
#3897=DIRECTION('',(0.,-1.,0.));
#3898=DIRECTION('',(0.,-1.,0.));
#3899=DIRECTION('',(0.,-1.,0.));
#3900=DIRECTION('',(0.,-1.,0.));
#3901=DIRECTION('',(0.,-1.,0.));
#3902=DIRECTION('',(0.,-1.,0.));
#3903=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011466224));
#3904=DIRECTION('ref_axis',(-0.00600991011466224,0.,-0.999981940327131));
#3905=DIRECTION('',(0.00600991011466224,0.,0.999981940327131));
#3906=DIRECTION('',(-0.00600991011466224,0.,-0.999981940327131));
#3907=DIRECTION('',(0.,-1.,0.));
#3908=DIRECTION('',(0.,-1.,0.));
#3909=DIRECTION('',(0.,-1.,0.));
#3910=DIRECTION('',(0.,-1.,0.));
#3911=DIRECTION('',(0.,-1.,0.));
#3912=DIRECTION('',(0.,-1.,0.));
#3913=DIRECTION('',(0.,-1.,0.));
#3914=DIRECTION('',(0.,-1.,0.));
#3915=DIRECTION('',(0.,-1.,0.));
#3916=DIRECTION('',(0.,-1.,0.));
#3917=DIRECTION('',(0.,-1.,0.));
#3918=DIRECTION('',(0.,-1.,0.));
#3919=DIRECTION('',(0.,-1.,0.));
#3920=DIRECTION('',(0.,-1.,0.));
#3921=DIRECTION('',(0.,-1.,0.));
#3922=DIRECTION('center_axis',(-0.141065729384956,0.,-0.990000232319715));
#3923=DIRECTION('ref_axis',(0.990000232319715,0.,-0.141065729384956));
#3924=DIRECTION('',(0.990000232319715,0.,-0.141065729384956));
#3925=DIRECTION('',(0.,-1.,0.));
#3926=DIRECTION('',(0.,-1.,0.));
#3927=DIRECTION('',(0.,-1.,0.));
#3928=DIRECTION('',(0.,-1.,0.));
#3929=DIRECTION('',(0.,-1.,0.));
#3930=DIRECTION('',(0.,-1.,0.));
#3931=DIRECTION('',(0.,-1.,0.));
#3932=DIRECTION('',(0.,-1.,0.));
#3933=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011465873));
#3934=DIRECTION('ref_axis',(0.00600991011465873,0.,0.999981940327131));
#3935=DIRECTION('',(0.00600991011465873,0.,0.999981940327131));
#3936=DIRECTION('',(0.,-1.,0.));
#3937=DIRECTION('',(0.,-1.,0.));
#3938=DIRECTION('',(0.,-1.,0.));
#3939=DIRECTION('center_axis',(-0.00600991011465938,0.,-0.999981940327131));
#3940=DIRECTION('ref_axis',(0.999981940327131,0.,-0.00600991011465938));
#3941=DIRECTION('',(0.999981940327131,0.,-0.00600991011465938));
#3942=DIRECTION('',(0.,-1.,0.));
#3943=DIRECTION('center_axis',(0.,-1.,0.));
#3944=DIRECTION('ref_axis',(1.,0.,0.));
#3945=DIRECTION('center_axis',(0.,-1.,0.));
#3946=DIRECTION('ref_axis',(1.,0.,0.));
#3947=DIRECTION('',(0.,-1.,0.));
#3948=DIRECTION('',(0.,-1.,0.));
#3949=DIRECTION('',(0.,-1.,0.));
#3950=DIRECTION('',(0.,-1.,0.));
#3951=DIRECTION('',(0.,-1.,0.));
#3952=DIRECTION('',(0.,-1.,0.));
#3953=DIRECTION('',(0.,-1.,0.));
#3954=DIRECTION('',(0.,-1.,0.));
#3955=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011465961));
#3956=DIRECTION('ref_axis',(-0.00600991011465961,0.,-0.999981940327131));
#3957=DIRECTION('',(0.,-1.,0.));
#3958=DIRECTION('',(-0.00600991011465962,0.,-0.999981940327131));
#3959=DIRECTION('',(0.,-1.,0.));
#3960=DIRECTION('',(0.,-1.,0.));
#3961=DIRECTION('',(0.,-1.,0.));
#3962=DIRECTION('',(0.,-1.,0.));
#3963=DIRECTION('',(0.,-1.,0.));
#3964=DIRECTION('',(0.,-1.,0.));
#3965=DIRECTION('',(0.,-1.,0.));
#3966=DIRECTION('',(0.,-1.,0.));
#3967=DIRECTION('',(0.,-1.,0.));
#3968=DIRECTION('',(0.,-1.,0.));
#3969=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011465956));
#3970=DIRECTION('ref_axis',(-0.00600991011465956,0.,-0.999981940327131));
#3971=DIRECTION('',(-0.00600991011465956,0.,-0.999981940327131));
#3972=DIRECTION('',(0.,-1.,0.));
#3973=DIRECTION('center_axis',(0.00600991011465929,0.,0.999981940327131));
#3974=DIRECTION('ref_axis',(-0.999981940327131,0.,0.00600991011465929));
#3975=DIRECTION('',(-0.999981940327131,0.,0.00600991011465929));
#3976=DIRECTION('',(0.,-1.,0.));
#3977=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011465932));
#3978=DIRECTION('ref_axis',(0.00600991011465932,0.,0.999981940327131));
#3979=DIRECTION('',(0.00600991011465932,0.,0.999981940327131));
#3980=DIRECTION('',(0.,-1.,0.));
#3981=DIRECTION('center_axis',(-0.00600991011466022,0.,-0.999981940327131));
#3982=DIRECTION('ref_axis',(0.999981940327131,0.,-0.00600991011466022));
#3983=DIRECTION('',(0.999981940327131,0.,-0.00600991011466022));
#3984=DIRECTION('center_axis',(0.,-1.,0.));
#3985=DIRECTION('ref_axis',(1.,0.,0.));
#3986=DIRECTION('center_axis',(0.,-1.,0.));
#3987=DIRECTION('ref_axis',(1.,0.,0.));
#3988=DIRECTION('',(0.,-1.,0.));
#3989=DIRECTION('',(0.,-1.,0.));
#3990=DIRECTION('',(0.,-1.,0.));
#3991=DIRECTION('',(0.,-1.,0.));
#3992=DIRECTION('',(0.,-1.,0.));
#3993=DIRECTION('',(0.,-1.,0.));
#3994=DIRECTION('',(0.,-1.,0.));
#3995=DIRECTION('',(0.,-1.,0.));
#3996=DIRECTION('',(0.,-1.,0.));
#3997=DIRECTION('',(0.,-1.,0.));
#3998=DIRECTION('center_axis',(-0.999981940327131,0.,0.00600991011466222));
#3999=DIRECTION('ref_axis',(-0.00600991011466222,0.,-0.999981940327131));
#4000=DIRECTION('',(0.00600991011466222,0.,0.999981940327131));
#4001=DIRECTION('',(-0.00600991011466222,0.,-0.999981940327131));
#4002=DIRECTION('',(0.,-1.,0.));
#4003=DIRECTION('',(0.,-1.,0.));
#4004=DIRECTION('',(0.,-1.,0.));
#4005=DIRECTION('',(0.,-1.,0.));
#4006=DIRECTION('',(0.,-1.,0.));
#4007=DIRECTION('',(0.,-1.,0.));
#4008=DIRECTION('',(0.,-1.,0.));
#4009=DIRECTION('',(0.,-1.,0.));
#4010=DIRECTION('',(0.,-1.,0.));
#4011=DIRECTION('',(0.,-1.,0.));
#4012=DIRECTION('',(0.,-1.,0.));
#4013=DIRECTION('',(0.,-1.,0.));
#4014=DIRECTION('',(0.,-1.,0.));
#4015=DIRECTION('',(0.,-1.,0.));
#4016=DIRECTION('',(0.,-1.,0.));
#4017=DIRECTION('center_axis',(-0.141065647133366,0.,-0.990000244039791));
#4018=DIRECTION('ref_axis',(0.990000244039791,0.,-0.141065647133366));
#4019=DIRECTION('',(0.990000244039791,0.,-0.141065647133366));
#4020=DIRECTION('',(0.,-1.,0.));
#4021=DIRECTION('',(0.,-1.,0.));
#4022=DIRECTION('',(0.,-1.,0.));
#4023=DIRECTION('',(0.,-1.,0.));
#4024=DIRECTION('',(0.,-1.,0.));
#4025=DIRECTION('',(0.,-1.,0.));
#4026=DIRECTION('',(0.,-1.,0.));
#4027=DIRECTION('',(0.,-1.,0.));
#4028=DIRECTION('center_axis',(0.999981940327131,0.,-0.00600991011466043));
#4029=DIRECTION('ref_axis',(0.00600991011466043,0.,0.999981940327131));
#4030=DIRECTION('',(0.00600991011466043,0.,0.999981940327131));
#4031=DIRECTION('',(0.,-1.,0.));
#4032=DIRECTION('',(0.,-1.,0.));
#4033=DIRECTION('',(0.,-1.,0.));
#4034=DIRECTION('center_axis',(-0.0060099101146603,0.,-0.999981940327131));
#4035=DIRECTION('ref_axis',(0.999981940327131,0.,-0.0060099101146603));
#4036=DIRECTION('',(0.999981940327131,0.,-0.0060099101146603));
#4037=DIRECTION('',(0.,-1.,0.));
#4038=DIRECTION('center_axis',(0.,-1.,0.));
#4039=DIRECTION('ref_axis',(1.,0.,0.));
#4040=DIRECTION('center_axis',(0.,-1.,0.));
#4041=DIRECTION('ref_axis',(1.,0.,0.));
#4042=DIRECTION('center_axis',(0.,0.,1.));
#4043=DIRECTION('ref_axis',(1.,0.,0.));
#4044=DIRECTION('center_axis',(0.,0.,-1.));
#4045=DIRECTION('ref_axis',(-1.,0.,0.));
#4046=DIRECTION('',(0.,1.,0.));
#4047=DIRECTION('',(-1.,0.,0.));
#4048=DIRECTION('',(0.,1.,0.));
#4049=DIRECTION('',(1.,0.,0.));
#4050=DIRECTION('',(0.,-1.,0.));
#4051=DIRECTION('',(-1.,0.,0.));
#4052=DIRECTION('',(0.,-1.,0.));
#4053=DIRECTION('center_axis',(0.,0.,1.));
#4054=DIRECTION('ref_axis',(1.,0.,0.));
#4055=DIRECTION('',(1.,0.,0.));
#4056=DIRECTION('center_axis',(0.,0.,-1.));
#4057=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186547,0.));
#4058=DIRECTION('',(0.,1.,0.));
#4059=DIRECTION('center_axis',(0.,0.,-1.));
#4060=DIRECTION('ref_axis',(-0.707106781186547,-0.707106781186547,0.));
#4061=DIRECTION('center_axis',(0.,0.,-1.));
#4062=DIRECTION('ref_axis',(0.707106781186547,-0.707106781186547,0.));
#4063=DIRECTION('',(0.,-1.,0.));
#4064=DIRECTION('center_axis',(0.,0.,-1.));
#4065=DIRECTION('ref_axis',(0.707106781186547,0.707106781186547,0.));
#4066=DIRECTION('',(1.,0.,0.));
#4067=DIRECTION('center_axis',(0.,0.,1.));
#4068=DIRECTION('ref_axis',(1.,0.,0.));
#4069=DIRECTION('center_axis',(0.,0.,1.));
#4070=DIRECTION('ref_axis',(1.,0.,0.));
#4071=DIRECTION('center_axis',(0.,0.,1.));
#4072=DIRECTION('ref_axis',(1.,0.,0.));
#4073=DIRECTION('center_axis',(0.,0.,1.));
#4074=DIRECTION('ref_axis',(1.,0.,0.));
#4075=DIRECTION('center_axis',(0.,-1.,0.));
#4076=DIRECTION('ref_axis',(1.,0.,0.));
#4077=DIRECTION('',(0.,0.,1.));
#4078=DIRECTION('',(1.,0.,0.));
#4079=DIRECTION('center_axis',(0.,-1.,0.));
#4080=DIRECTION('ref_axis',(-0.707106781186548,0.,-0.707106781186547));
#4081=DIRECTION('center_axis',(1.,0.,0.));
#4082=DIRECTION('ref_axis',(0.,1.,0.));
#4083=DIRECTION('',(0.,0.,1.));
#4084=DIRECTION('',(0.,1.,0.));
#4085=DIRECTION('center_axis',(0.,1.,0.));
#4086=DIRECTION('ref_axis',(-1.,0.,0.));
#4087=DIRECTION('',(0.,0.,1.));
#4088=DIRECTION('',(-1.,0.,0.));
#4089=DIRECTION('center_axis',(-1.,0.,0.));
#4090=DIRECTION('ref_axis',(0.,-1.,0.));
#4091=DIRECTION('',(0.,0.,1.));
#4092=DIRECTION('',(0.,-1.,0.));
#4093=DIRECTION('center_axis',(0.,-1.,0.));
#4094=DIRECTION('ref_axis',(1.,0.,0.));
#4095=DIRECTION('center_axis',(0.,-1.,0.));
#4096=DIRECTION('ref_axis',(0.707106781186548,0.,-0.707106781186548));
#4097=DIRECTION('',(1.,0.,0.));
#4098=DIRECTION('center_axis',(0.,1.,0.));
#4099=DIRECTION('ref_axis',(0.,0.,1.));
#4100=DIRECTION('center_axis',(-1.,0.,1.11022302462516E-15));
#4101=DIRECTION('ref_axis',(1.11022302462516E-15,0.,1.));
#4102=DIRECTION('center_axis',(0.,1.,0.));
#4103=DIRECTION('ref_axis',(-0.707106781186548,0.,-0.707106781186547));
#4104=DIRECTION('center_axis',(0.,-1.,0.));
#4105=DIRECTION('ref_axis',(-0.707106781186548,0.,-0.707106781186547));
#4106=DIRECTION('center_axis',(-1.,0.,0.));
#4107=DIRECTION('ref_axis',(0.,0.707106781186546,0.707106781186549));
#4108=DIRECTION('center_axis',(-1.,0.,0.));
#4109=DIRECTION('ref_axis',(0.,0.,1.));
#4110=DIRECTION('',(-1.,0.,0.));
#4111=DIRECTION('',(1.,0.,0.));
#4112=DIRECTION('center_axis',(0.,1.,0.));
#4113=DIRECTION('ref_axis',(0.,0.,1.));
#4114=DIRECTION('center_axis',(0.,1.,0.));
#4115=DIRECTION('ref_axis',(0.707106781186548,0.,-0.707106781186548));
#4116=DIRECTION('center_axis',(0.,-1.,0.));
#4117=DIRECTION('ref_axis',(0.707106781186548,0.,-0.707106781186548));
#4118=DIRECTION('center_axis',(0.,-1.,0.));
#4119=DIRECTION('ref_axis',(0.707106781186548,0.,-0.707106781186548));
#4120=DIRECTION('',(0.,-1.,0.));
#4121=DIRECTION('center_axis',(0.,0.,1.));
#4122=DIRECTION('ref_axis',(1.,0.,0.));
#4123=DIRECTION('',(0.,1.,0.));
#4124=DIRECTION('center_axis',(0.,1.,0.));
#4125=DIRECTION('ref_axis',(-0.707106781186548,0.,-0.707106781186547));
#4126=DIRECTION('center_axis',(0.,0.,1.));
#4127=DIRECTION('ref_axis',(-0.707106781186547,-0.707106781186547,0.));
#4128=DIRECTION('',(0.,0.,-1.));
#4129=DIRECTION('center_axis',(0.,0.,1.));
#4130=DIRECTION('ref_axis',(-0.707106781186547,-0.707106781186547,0.));
#4131=DIRECTION('center_axis',(0.,-1.,0.));
#4132=DIRECTION('ref_axis',(1.,0.,0.));
#4133=DIRECTION('center_axis',(0.,0.,1.));
#4134=DIRECTION('ref_axis',(0.707106781186547,-0.707106781186547,0.));
#4135=DIRECTION('center_axis',(0.,0.,1.));
#4136=DIRECTION('ref_axis',(0.707106781186547,-0.707106781186547,0.));
#4137=DIRECTION('',(0.,0.,1.));
#4138=DIRECTION('center_axis',(0.,0.,1.));
#4139=DIRECTION('ref_axis',(0.707106781186547,0.707106781186547,0.));
#4140=DIRECTION('',(0.,0.,-1.));
#4141=DIRECTION('center_axis',(0.,0.,1.));
#4142=DIRECTION('ref_axis',(0.707106781186547,0.707106781186547,0.));
#4143=DIRECTION('',(0.,0.,1.));
#4144=DIRECTION('center_axis',(0.,1.,0.));
#4145=DIRECTION('ref_axis',(-1.,0.,0.));
#4146=DIRECTION('',(1.,0.,0.));
#4147=DIRECTION('',(0.,0.,-1.));
#4148=DIRECTION('center_axis',(0.,0.,1.));
#4149=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186547,0.));
#4150=DIRECTION('center_axis',(0.,0.,1.));
#4151=DIRECTION('ref_axis',(-0.707106781186547,0.707106781186547,0.));
#4152=DIRECTION('',(0.,0.,1.));
#4153=DIRECTION('center_axis',(0.,0.,1.));
#4154=DIRECTION('ref_axis',(1.,0.,0.));
#4155=DIRECTION('center_axis',(0.,0.,1.));
#4156=DIRECTION('ref_axis',(1.,0.,0.));
#4157=DIRECTION('',(0.,0.,-1.));
#4158=DIRECTION('center_axis',(0.,0.,-1.));
#4159=DIRECTION('ref_axis',(1.,0.,0.));
#4160=DIRECTION('center_axis',(0.,0.,1.));
#4161=DIRECTION('ref_axis',(1.,0.,0.));
#4162=DIRECTION('center_axis',(0.,0.,1.));
#4163=DIRECTION('ref_axis',(1.,0.,0.));
#4164=DIRECTION('',(0.,0.,-1.));
#4165=DIRECTION('center_axis',(0.,0.,-1.));
#4166=DIRECTION('ref_axis',(1.,0.,0.));
#4167=DIRECTION('center_axis',(0.,0.,1.));
#4168=DIRECTION('ref_axis',(1.,0.,0.));
#4169=DIRECTION('center_axis',(0.,0.,1.));
#4170=DIRECTION('ref_axis',(1.,0.,0.));
#4171=DIRECTION('',(0.,0.,-1.));
#4172=DIRECTION('center_axis',(0.,0.,-1.));
#4173=DIRECTION('ref_axis',(1.,0.,0.));
#4174=DIRECTION('center_axis',(0.,0.,1.));
#4175=DIRECTION('ref_axis',(1.,0.,0.));
#4176=DIRECTION('center_axis',(0.,0.,1.));
#4177=DIRECTION('ref_axis',(1.,0.,0.));
#4178=DIRECTION('',(0.,0.,-1.));
#4179=DIRECTION('center_axis',(0.,0.,-1.));
#4180=DIRECTION('ref_axis',(1.,0.,0.));
#4181=DIRECTION('center_axis',(-1.,0.,0.));
#4182=DIRECTION('ref_axis',(0.,-1.,0.));
#4183=DIRECTION('',(0.,1.,0.));
#4184=DIRECTION('center_axis',(1.,0.,0.));
#4185=DIRECTION('ref_axis',(0.,1.,0.));
#4186=DIRECTION('',(0.,-1.,0.));
#4187=DIRECTION('center_axis',(0.,0.,1.));
#4188=DIRECTION('ref_axis',(1.,0.,0.));
#4189=DIRECTION('center_axis',(0.,0.,1.));
#4190=DIRECTION('ref_axis',(1.,0.,0.));
#4191=DIRECTION('',(0.,0.,-1.));
#4192=DIRECTION('center_axis',(0.,0.,-1.));
#4193=DIRECTION('ref_axis',(1.,0.,0.));
#4194=DIRECTION('center_axis',(0.,0.,1.));
#4195=DIRECTION('ref_axis',(1.,0.,0.));
#4196=DIRECTION('center_axis',(0.,0.,1.));
#4197=DIRECTION('ref_axis',(1.,0.,0.));
#4198=DIRECTION('',(0.,0.,-1.));
#4199=DIRECTION('center_axis',(0.,0.,-1.));
#4200=DIRECTION('ref_axis',(1.,0.,0.));
#4201=DIRECTION('center_axis',(0.,0.,1.));
#4202=DIRECTION('ref_axis',(1.,0.,0.));
#4203=DIRECTION('center_axis',(0.,0.,1.));
#4204=DIRECTION('ref_axis',(1.,0.,0.));
#4205=DIRECTION('',(0.,0.,-1.));
#4206=DIRECTION('center_axis',(0.,0.,-1.));
#4207=DIRECTION('ref_axis',(1.,0.,0.));
#4208=DIRECTION('center_axis',(0.,0.,1.));
#4209=DIRECTION('ref_axis',(1.,0.,0.));
#4210=DIRECTION('center_axis',(0.,0.,1.));
#4211=DIRECTION('ref_axis',(1.,0.,0.));
#4212=DIRECTION('',(0.,0.,-1.));
#4213=DIRECTION('center_axis',(0.,0.,-1.));
#4214=DIRECTION('ref_axis',(1.,0.,0.));
#4215=DIRECTION('center_axis',(0.,0.,1.));
#4216=DIRECTION('ref_axis',(1.,0.,0.));
#4217=DIRECTION('',(0.,0.,1.));
#4218=DIRECTION('',(1.,0.,0.));
#4219=DIRECTION('center_axis',(0.,0.,1.));
#4220=DIRECTION('ref_axis',(1.,0.,0.));
#4221=CARTESIAN_POINT('',(0.,0.,0.));
#4222=CARTESIAN_POINT('',(0.,0.,0.));
#4223=CARTESIAN_POINT('Origin',(-23.0280245176882,-53.179865365992,4.16266028691298));
#4224=CARTESIAN_POINT('',(-23.0309761794961,-53.179865365992,3.67153671842132));
#4225=CARTESIAN_POINT('',(-23.0280245176882,-53.179865365992,4.16266028691298));
#4226=CARTESIAN_POINT('',(-23.0410959453847,-53.179865365992,1.98772066528799));
#4227=CARTESIAN_POINT('',(-23.0309761794961,-53.679865365992,3.67153671842132));
#4228=CARTESIAN_POINT('',(-23.0309761794961,-53.179865365992,3.67153671842132));
#4229=CARTESIAN_POINT('',(-23.0280245176882,-53.679865365992,4.16266028691298));
#4230=CARTESIAN_POINT('',(-23.0309761794961,-53.679865365992,3.67153671842132));
#4231=CARTESIAN_POINT('',(-23.0280245176882,-53.179865365992,4.16266028691298));
#4232=CARTESIAN_POINT('Origin',(-20.9635505346081,-53.179865365992,6.71237249997108));
#4233=CARTESIAN_POINT('',(-20.9635505346081,-53.179865365992,6.71237249997108));
#4234=CARTESIAN_POINT('',(-29.1866028135564,-53.179865365992,-3.44344297461702));
#4235=CARTESIAN_POINT('',(-20.9635505346081,-53.679865365992,6.71237249997108));
#4236=CARTESIAN_POINT('',(-23.0280245176882,-53.679865365992,4.16266028691298));
#4237=CARTESIAN_POINT('',(-20.9635505346081,-53.179865365992,6.71237249997108));
#4238=CARTESIAN_POINT('Ctrl Pts',(-20.5459708643488,-53.179865365992,7.18463283900316));
#4239=CARTESIAN_POINT('Ctrl Pts',(-20.5459708643488,-53.679865365992,7.18463283900316));
#4240=CARTESIAN_POINT('Ctrl Pts',(-20.6145594201773,-53.179865365992,7.11257394800412));
#4241=CARTESIAN_POINT('Ctrl Pts',(-20.6145594201773,-53.679865365992,7.11257394800412));
#4242=CARTESIAN_POINT('Ctrl Pts',(-20.75952628227,-53.179865365992,6.96027228210902));
#4243=CARTESIAN_POINT('Ctrl Pts',(-20.75952628227,-53.679865365992,6.96027228210902));
#4244=CARTESIAN_POINT('Ctrl Pts',(-20.8931490573765,-53.179865365992,6.79791385433413));
#4245=CARTESIAN_POINT('Ctrl Pts',(-20.8931490573765,-53.679865365992,6.79791385433413));
#4246=CARTESIAN_POINT('Ctrl Pts',(-20.9635505346081,-53.179865365992,6.71237249997108));
#4247=CARTESIAN_POINT('Ctrl Pts',(-20.9635505346081,-53.679865365992,6.71237249997108));
#4248=CARTESIAN_POINT('',(-20.5459708643488,-53.179865365992,7.18463283900316));
#4249=CARTESIAN_POINT('Ctrl Pts',(-20.9635505346081,-53.179865365992,6.71237249997108));
#4250=CARTESIAN_POINT('Ctrl Pts',(-20.8931490573765,-53.179865365992,6.79791385433413));
#4251=CARTESIAN_POINT('Ctrl Pts',(-20.75952628227,-53.179865365992,6.96027228210902));
#4252=CARTESIAN_POINT('Ctrl Pts',(-20.6145594201773,-53.179865365992,7.11257394800412));
#4253=CARTESIAN_POINT('Ctrl Pts',(-20.5459708643488,-53.179865365992,7.18463283900316));
#4254=CARTESIAN_POINT('',(-20.5459708643488,-53.679865365992,7.18463283900316));
#4255=CARTESIAN_POINT('Ctrl Pts',(-20.5459708643488,-53.679865365992,7.18463283900316));
#4256=CARTESIAN_POINT('Ctrl Pts',(-20.6145594201773,-53.679865365992,7.11257394800412));
#4257=CARTESIAN_POINT('Ctrl Pts',(-20.75952628227,-53.679865365992,6.96027228210902));
#4258=CARTESIAN_POINT('Ctrl Pts',(-20.8931490573765,-53.679865365992,6.79791385433413));
#4259=CARTESIAN_POINT('Ctrl Pts',(-20.9635505346081,-53.679865365992,6.71237249997108));
#4260=CARTESIAN_POINT('',(-20.5459708643488,-53.179865365992,7.18463283900316));
#4261=CARTESIAN_POINT('Origin',(-22.7778540100512,-53.179865365992,7.19804649834156));
#4262=CARTESIAN_POINT('',(-22.7778540100512,-53.179865365992,7.19804649834156));
#4263=CARTESIAN_POINT('',(-38.4693856961042,-53.179865365992,7.29235289647877));
#4264=CARTESIAN_POINT('',(-22.7778540100512,-53.679865365992,7.19804649834156));
#4265=CARTESIAN_POINT('',(-20.5459708643488,-53.679865365992,7.18463283900316));
#4266=CARTESIAN_POINT('',(-22.7778540100512,-53.179865365992,7.19804649834156));
#4267=CARTESIAN_POINT('Origin',(-22.7750171357979,-53.179865365992,7.67007069939595));
#4268=CARTESIAN_POINT('',(-22.7750171357979,-53.179865365992,7.67007069939595));
#4269=CARTESIAN_POINT('',(-22.7986325529989,-53.179865365992,3.74072895156082));
#4270=CARTESIAN_POINT('',(-22.7750171357979,-53.679865365992,7.67007069939595));
#4271=CARTESIAN_POINT('',(-22.7778540100512,-53.679865365992,7.19804649834156));
#4272=CARTESIAN_POINT('',(-22.7750171357979,-53.179865365992,7.67007069939595));
#4273=CARTESIAN_POINT('Origin',(-19.9101303855571,-53.179865365992,7.65285267658647));
#4274=CARTESIAN_POINT('',(-19.9101303855571,-53.179865365992,7.65285267658647));
#4275=CARTESIAN_POINT('',(-37.0341054467305,-53.179865365992,7.75576808612843));
#4276=CARTESIAN_POINT('',(-19.9101303855571,-53.679865365992,7.65285267658647));
#4277=CARTESIAN_POINT('',(-22.7750171357979,-53.679865365992,7.67007069939595));
#4278=CARTESIAN_POINT('',(-19.9101303855571,-53.179865365992,7.65285267658647));
#4279=CARTESIAN_POINT('Origin',(-19.9129672598104,-53.179865365992,7.18082847553207));
#4280=CARTESIAN_POINT('',(-19.9129672598104,-53.179865365992,7.18082847553207));
#4281=CARTESIAN_POINT('',(-19.9351642398847,-53.179865365992,3.48749882822406));
#4282=CARTESIAN_POINT('',(-19.9129672598104,-53.679865365992,7.18082847553207));
#4283=CARTESIAN_POINT('',(-19.9101303855571,-53.679865365992,7.65285267658647));
#4284=CARTESIAN_POINT('',(-19.9129672598104,-53.179865365992,7.18082847553207));
#4285=CARTESIAN_POINT('Origin',(-22.1751696174372,-53.179865365992,4.41947642545088));
#4286=CARTESIAN_POINT('',(-22.1751696174372,-53.179865365992,4.41947642545088));
#4287=CARTESIAN_POINT('',(-29.690240843304,-53.179865365992,-4.75377780005695));
#4288=CARTESIAN_POINT('',(-22.1751696174372,-53.679865365992,4.41947642545088));
#4289=CARTESIAN_POINT('',(-19.9129672598104,-53.679865365992,7.18082847553207));
#4290=CARTESIAN_POINT('',(-22.1751696174372,-53.179865365992,4.41947642545088));
#4291=CARTESIAN_POINT('Origin',(-22.4196919052055,-53.179865365992,4.13990413925173));
#4292=CARTESIAN_POINT('',(-22.4196919052055,-53.179865365992,4.13990413925173));
#4293=CARTESIAN_POINT('',(-30.3337213581164,-53.179865365992,-4.90852791735423));
#4294=CARTESIAN_POINT('',(-22.4196919052055,-53.679865365992,4.13990413925173));
#4295=CARTESIAN_POINT('',(-22.1751696174372,-53.679865365992,4.41947642545088));
#4296=CARTESIAN_POINT('',(-22.4196919052055,-53.179865365992,4.13990413925173));
#4297=CARTESIAN_POINT('Origin',(-19.8658499897872,-53.179865365992,4.12455550170164));
#4298=CARTESIAN_POINT('',(-19.8658499897872,-53.179865365992,4.12455550170164));
#4299=CARTESIAN_POINT('',(-37.0225666321274,-53.179865365992,4.22766768875717));
#4300=CARTESIAN_POINT('',(-19.8658499897872,-53.679865365992,4.12455550170164));
#4301=CARTESIAN_POINT('',(-22.4196919052055,-53.679865365992,4.13990413925173));
#4302=CARTESIAN_POINT('',(-19.8658499897872,-53.179865365992,4.12455550170164));
#4303=CARTESIAN_POINT('Origin',(-19.8686868640405,-53.179865365992,3.65253130064725));
#4304=CARTESIAN_POINT('',(-19.8686868640405,-53.179865365992,3.65253130064725));
#4305=CARTESIAN_POINT('',(-19.8802824608331,-53.179865365992,1.72315346326809));
#4306=CARTESIAN_POINT('',(-19.8686868640405,-53.679865365992,3.65253130064725));
#4307=CARTESIAN_POINT('',(-19.8658499897872,-53.679865365992,4.12455550170164));
#4308=CARTESIAN_POINT('',(-19.8686868640405,-53.179865365992,3.65253130064725));
#4309=CARTESIAN_POINT('Origin',(-23.0309761794961,-53.179865365992,3.67153671842132));
#4310=CARTESIAN_POINT('',(-38.6065481641085,-53.179865365992,3.7651461965898));
#4311=CARTESIAN_POINT('',(-19.8686868640405,-53.679865365992,3.65253130064725));
#4312=CARTESIAN_POINT('Origin',(-13.4911611848099,-53.679865365992,5.09371901156747));
#4313=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.179865365992,4.23248873811123));
#4314=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.679865365992,4.23248873811123));
#4315=CARTESIAN_POINT('Ctrl Pts',(-15.6437520442833,-53.179865365992,4.34361217890199));
#4316=CARTESIAN_POINT('Ctrl Pts',(-15.6437520442833,-53.679865365992,4.34361217890199));
#4317=CARTESIAN_POINT('Ctrl Pts',(-15.7926956751858,-53.179865365992,4.59776864612636));
#4318=CARTESIAN_POINT('Ctrl Pts',(-15.7926956751858,-53.679865365992,4.59776864612636));
#4319=CARTESIAN_POINT('Ctrl Pts',(-15.7974490742423,-53.179865365992,4.89391219456719));
#4320=CARTESIAN_POINT('Ctrl Pts',(-15.7974490742423,-53.679865365992,4.89391219456719));
#4321=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.179865365992,5.06057452055431));
#4322=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.679865365992,5.06057452055431));
#4323=CARTESIAN_POINT('',(-15.8001241706518,-53.179865365992,5.06057452055431));
#4324=CARTESIAN_POINT('',(-15.5786302359323,-53.179865365992,4.23248873811123));
#4325=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.179865365992,5.06057452055431));
#4326=CARTESIAN_POINT('Ctrl Pts',(-15.7974490742423,-53.179865365992,4.89391219456719));
#4327=CARTESIAN_POINT('Ctrl Pts',(-15.7926956751858,-53.179865365992,4.59776864612636));
#4328=CARTESIAN_POINT('Ctrl Pts',(-15.6437520442833,-53.179865365992,4.34361217890199));
#4329=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.179865365992,4.23248873811123));
#4330=CARTESIAN_POINT('',(-15.8001241706518,-53.679865365992,5.06057452055431));
#4331=CARTESIAN_POINT('',(-15.8001241706518,-53.179865365992,5.06057452055431));
#4332=CARTESIAN_POINT('',(-15.5786302359323,-53.679865365992,4.23248873811123));
#4333=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.679865365992,4.23248873811123));
#4334=CARTESIAN_POINT('Ctrl Pts',(-15.6437520442833,-53.679865365992,4.34361217890199));
#4335=CARTESIAN_POINT('Ctrl Pts',(-15.7926956751858,-53.679865365992,4.59776864612636));
#4336=CARTESIAN_POINT('Ctrl Pts',(-15.7974490742423,-53.679865365992,4.89391219456719));
#4337=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.679865365992,5.06057452055431));
#4338=CARTESIAN_POINT('',(-15.5786302359323,-53.179865365992,4.23248873811123));
#4339=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.179865365992,3.96180335941603));
#4340=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.679865365992,3.96180335941603));
#4341=CARTESIAN_POINT('Ctrl Pts',(-15.1359550225071,-53.179865365992,3.97461866326413));
#4342=CARTESIAN_POINT('Ctrl Pts',(-15.1359550225071,-53.679865365992,3.97461866326413));
#4343=CARTESIAN_POINT('Ctrl Pts',(-15.3494387179853,-53.179865365992,4.00088984535831));
#4344=CARTESIAN_POINT('Ctrl Pts',(-15.3494387179853,-53.679865365992,4.00088984535831));
#4345=CARTESIAN_POINT('Ctrl Pts',(-15.5010012058929,-53.179865365992,4.15404431065752));
#4346=CARTESIAN_POINT('Ctrl Pts',(-15.5010012058929,-53.679865365992,4.15404431065752));
#4347=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.179865365992,4.23248873811123));
#4348=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.679865365992,4.23248873811123));
#4349=CARTESIAN_POINT('',(-15.0318158781641,-53.179865365992,3.96180335941603));
#4350=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.179865365992,4.23248873811123));
#4351=CARTESIAN_POINT('Ctrl Pts',(-15.5010012058929,-53.179865365992,4.15404431065752));
#4352=CARTESIAN_POINT('Ctrl Pts',(-15.3494387179853,-53.179865365992,4.00088984535831));
#4353=CARTESIAN_POINT('Ctrl Pts',(-15.1359550225071,-53.179865365992,3.97461866326413));
#4354=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.179865365992,3.96180335941603));
#4355=CARTESIAN_POINT('',(-15.0318158781641,-53.679865365992,3.96180335941603));
#4356=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.679865365992,3.96180335941603));
#4357=CARTESIAN_POINT('Ctrl Pts',(-15.1359550225071,-53.679865365992,3.97461866326413));
#4358=CARTESIAN_POINT('Ctrl Pts',(-15.3494387179853,-53.679865365992,4.00088984535831));
#4359=CARTESIAN_POINT('Ctrl Pts',(-15.5010012058929,-53.679865365992,4.15404431065752));
#4360=CARTESIAN_POINT('Ctrl Pts',(-15.5786302359323,-53.679865365992,4.23248873811123));
#4361=CARTESIAN_POINT('',(-15.0318158781641,-53.179865365992,3.96180335941603));
#4362=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.179865365992,4.23401646097182));
#4363=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.679865365992,4.23401646097182));
#4364=CARTESIAN_POINT('Ctrl Pts',(-14.5510146961696,-53.179865365992,4.15413753731628));
#4365=CARTESIAN_POINT('Ctrl Pts',(-14.5510146961696,-53.679865365992,4.15413753731628));
#4366=CARTESIAN_POINT('Ctrl Pts',(-14.707103650891,-53.179865365992,3.998653727333));
#4367=CARTESIAN_POINT('Ctrl Pts',(-14.707103650891,-53.679865365992,3.998653727333));
#4368=CARTESIAN_POINT('Ctrl Pts',(-14.9255803442582,-53.179865365992,3.97385963011197));
#4369=CARTESIAN_POINT('Ctrl Pts',(-14.9255803442582,-53.679865365992,3.97385963011197));
#4370=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.179865365992,3.96180335941603));
#4371=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.679865365992,3.96180335941603));
#4372=CARTESIAN_POINT('',(-14.4708248828444,-53.179865365992,4.23401646097182));
#4373=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.179865365992,3.96180335941603));
#4374=CARTESIAN_POINT('Ctrl Pts',(-14.9255803442582,-53.179865365992,3.97385963011197));
#4375=CARTESIAN_POINT('Ctrl Pts',(-14.707103650891,-53.179865365992,3.998653727333));
#4376=CARTESIAN_POINT('Ctrl Pts',(-14.5510146961696,-53.179865365992,4.15413753731628));
#4377=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.179865365992,4.23401646097182));
#4378=CARTESIAN_POINT('',(-14.4708248828444,-53.679865365992,4.23401646097182));
#4379=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.679865365992,4.23401646097182));
#4380=CARTESIAN_POINT('Ctrl Pts',(-14.5510146961696,-53.679865365992,4.15413753731628));
#4381=CARTESIAN_POINT('Ctrl Pts',(-14.707103650891,-53.679865365992,3.998653727333));
#4382=CARTESIAN_POINT('Ctrl Pts',(-14.9255803442582,-53.679865365992,3.97385963011197));
#4383=CARTESIAN_POINT('Ctrl Pts',(-15.0318158781641,-53.679865365992,3.96180335941603));
#4384=CARTESIAN_POINT('',(-14.4708248828444,-53.179865365992,4.23401646097182));
#4385=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.179865365992,5.09207239292835));
#4386=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.679865365992,5.09207239292835));
#4387=CARTESIAN_POINT('Ctrl Pts',(-14.2358634400377,-53.179865365992,4.91894154586963));
#4388=CARTESIAN_POINT('Ctrl Pts',(-14.2358634400377,-53.679865365992,4.91894154586963));
#4389=CARTESIAN_POINT('Ctrl Pts',(-14.2444850414005,-53.179865365992,4.61129534480651));
#4390=CARTESIAN_POINT('Ctrl Pts',(-14.2444850414005,-53.679865365992,4.61129534480651));
#4391=CARTESIAN_POINT('Ctrl Pts',(-14.4019672819331,-53.179865365992,4.34879306702401));
#4392=CARTESIAN_POINT('Ctrl Pts',(-14.4019672819331,-53.679865365992,4.34879306702401));
#4393=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.179865365992,4.23401646097182));
#4394=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.679865365992,4.23401646097182));
#4395=CARTESIAN_POINT('',(-14.2310115512714,-53.179865365992,5.09207239292835));
#4396=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.179865365992,4.23401646097182));
#4397=CARTESIAN_POINT('Ctrl Pts',(-14.4019672819331,-53.179865365992,4.34879306702401));
#4398=CARTESIAN_POINT('Ctrl Pts',(-14.2444850414005,-53.179865365992,4.61129534480651));
#4399=CARTESIAN_POINT('Ctrl Pts',(-14.2358634400377,-53.179865365992,4.91894154586963));
#4400=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.179865365992,5.09207239292835));
#4401=CARTESIAN_POINT('',(-14.2310115512714,-53.679865365992,5.09207239292835));
#4402=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.679865365992,5.09207239292835));
#4403=CARTESIAN_POINT('Ctrl Pts',(-14.2358634400377,-53.679865365992,4.91894154586963));
#4404=CARTESIAN_POINT('Ctrl Pts',(-14.2444850414005,-53.679865365992,4.61129534480651));
#4405=CARTESIAN_POINT('Ctrl Pts',(-14.4019672819331,-53.679865365992,4.34879306702401));
#4406=CARTESIAN_POINT('Ctrl Pts',(-14.4708248828444,-53.679865365992,4.23401646097182));
#4407=CARTESIAN_POINT('',(-14.2310115512714,-53.179865365992,5.09207239292835));
#4408=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.179865365992,5.92290262993968));
#4409=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.679865365992,5.92290262993968));
#4410=CARTESIAN_POINT('Ctrl Pts',(-14.3892786419937,-53.179865365992,5.81023122261995));
#4411=CARTESIAN_POINT('Ctrl Pts',(-14.3892786419937,-53.679865365992,5.81023122261995));
#4412=CARTESIAN_POINT('Ctrl Pts',(-14.2399412999495,-53.179865365992,5.55505563526676));
#4413=CARTESIAN_POINT('Ctrl Pts',(-14.2399412999495,-53.679865365992,5.55505563526676));
#4414=CARTESIAN_POINT('Ctrl Pts',(-14.2342114287046,-53.179865365992,5.25797735649296));
#4415=CARTESIAN_POINT('Ctrl Pts',(-14.2342114287046,-53.679865365992,5.25797735649296));
#4416=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.179865365992,5.09207239292835));
#4417=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.679865365992,5.09207239292835));
#4418=CARTESIAN_POINT('',(-14.4552177416859,-53.179865365992,5.92290262993968));
#4419=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.179865365992,5.09207239292835));
#4420=CARTESIAN_POINT('Ctrl Pts',(-14.2342114287046,-53.179865365992,5.25797735649296));
#4421=CARTESIAN_POINT('Ctrl Pts',(-14.2399412999495,-53.179865365992,5.55505563526676));
#4422=CARTESIAN_POINT('Ctrl Pts',(-14.3892786419937,-53.179865365992,5.81023122261995));
#4423=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.179865365992,5.92290262993968));
#4424=CARTESIAN_POINT('',(-14.4552177416859,-53.679865365992,5.92290262993968));
#4425=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.679865365992,5.92290262993968));
#4426=CARTESIAN_POINT('Ctrl Pts',(-14.3892786419937,-53.679865365992,5.81023122261995));
#4427=CARTESIAN_POINT('Ctrl Pts',(-14.2399412999495,-53.679865365992,5.55505563526676));
#4428=CARTESIAN_POINT('Ctrl Pts',(-14.2342114287046,-53.679865365992,5.25797735649296));
#4429=CARTESIAN_POINT('Ctrl Pts',(-14.2310115512714,-53.679865365992,5.09207239292835));
#4430=CARTESIAN_POINT('',(-14.4552177416859,-53.179865365992,5.92290262993968));
#4431=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.179865365992,6.20174086620105));
#4432=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.679865365992,6.20174086620105));
#4433=CARTESIAN_POINT('Ctrl Pts',(-14.8936456998424,-53.179865365992,6.18813171828139));
#4434=CARTESIAN_POINT('Ctrl Pts',(-14.8936456998424,-53.679865365992,6.18813171828139));
#4435=CARTESIAN_POINT('Ctrl Pts',(-14.6805184802608,-53.179865365992,6.15993884649375));
#4436=CARTESIAN_POINT('Ctrl Pts',(-14.6805184802608,-53.679865365992,6.15993884649375));
#4437=CARTESIAN_POINT('Ctrl Pts',(-14.5320289911149,-53.179865365992,6.00371482819793));
#4438=CARTESIAN_POINT('Ctrl Pts',(-14.5320289911149,-53.679865365992,6.00371482819793));
#4439=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.179865365992,5.92290262993968));
#4440=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.679865365992,5.92290262993968));
#4441=CARTESIAN_POINT('',(-14.9965256007918,-53.179865365992,6.20174086620105));
#4442=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.179865365992,5.92290262993968));
#4443=CARTESIAN_POINT('Ctrl Pts',(-14.5320289911149,-53.179865365992,6.00371482819793));
#4444=CARTESIAN_POINT('Ctrl Pts',(-14.6805184802608,-53.179865365992,6.15993884649375));
#4445=CARTESIAN_POINT('Ctrl Pts',(-14.8936456998424,-53.179865365992,6.18813171828139));
#4446=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.179865365992,6.20174086620105));
#4447=CARTESIAN_POINT('',(-14.9965256007918,-53.679865365992,6.20174086620105));
#4448=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.679865365992,6.20174086620105));
#4449=CARTESIAN_POINT('Ctrl Pts',(-14.8936456998424,-53.679865365992,6.18813171828139));
#4450=CARTESIAN_POINT('Ctrl Pts',(-14.6805184802608,-53.679865365992,6.15993884649375));
#4451=CARTESIAN_POINT('Ctrl Pts',(-14.5320289911149,-53.679865365992,6.00371482819793));
#4452=CARTESIAN_POINT('Ctrl Pts',(-14.4552177416859,-53.679865365992,5.92290262993968));
#4453=CARTESIAN_POINT('',(-14.9965256007918,-53.179865365992,6.20174086620105));
#4454=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.179865365992,5.91041200154461));
#4455=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.679865365992,5.91041200154461));
#4456=CARTESIAN_POINT('Ctrl Pts',(-15.47378195314,-53.179865365992,5.99579078640885));
#4457=CARTESIAN_POINT('Ctrl Pts',(-15.47378195314,-53.679865365992,5.99579078640885));
#4458=CARTESIAN_POINT('Ctrl Pts',(-15.3202300288793,-53.179865365992,6.1574014183055));
#4459=CARTESIAN_POINT('Ctrl Pts',(-15.3202300288793,-53.679865365992,6.1574014183055));
#4460=CARTESIAN_POINT('Ctrl Pts',(-15.1002774055862,-53.179865365992,6.18752945188728));
#4461=CARTESIAN_POINT('Ctrl Pts',(-15.1002774055862,-53.679865365992,6.18752945188728));
#4462=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.179865365992,6.20174086620105));
#4463=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.679865365992,6.20174086620105));
#4464=CARTESIAN_POINT('',(-15.5549033283434,-53.179865365992,5.91041200154461));
#4465=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.179865365992,6.20174086620105));
#4466=CARTESIAN_POINT('Ctrl Pts',(-15.1002774055862,-53.179865365992,6.18752945188728));
#4467=CARTESIAN_POINT('Ctrl Pts',(-15.3202300288793,-53.179865365992,6.1574014183055));
#4468=CARTESIAN_POINT('Ctrl Pts',(-15.47378195314,-53.179865365992,5.99579078640885));
#4469=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.179865365992,5.91041200154461));
#4470=CARTESIAN_POINT('',(-15.5549033283434,-53.679865365992,5.91041200154461));
#4471=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.679865365992,5.91041200154461));
#4472=CARTESIAN_POINT('Ctrl Pts',(-15.47378195314,-53.679865365992,5.99579078640885));
#4473=CARTESIAN_POINT('Ctrl Pts',(-15.3202300288793,-53.679865365992,6.1574014183055));
#4474=CARTESIAN_POINT('Ctrl Pts',(-15.1002774055862,-53.679865365992,6.18752945188728));
#4475=CARTESIAN_POINT('Ctrl Pts',(-14.9965256007918,-53.679865365992,6.20174086620105));
#4476=CARTESIAN_POINT('',(-15.5549033283434,-53.179865365992,5.91041200154461));
#4477=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.179865365992,5.06057452055431));
#4478=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.679865365992,5.06057452055431));
#4479=CARTESIAN_POINT('Ctrl Pts',(-15.7940741129583,-53.179865365992,5.22922741251946));
#4480=CARTESIAN_POINT('Ctrl Pts',(-15.7940741129583,-53.679865365992,5.22922741251946));
#4481=CARTESIAN_POINT('Ctrl Pts',(-15.7831286173372,-53.179865365992,5.53434673270265));
#4482=CARTESIAN_POINT('Ctrl Pts',(-15.7831286173372,-53.679865365992,5.53434673270265));
#4483=CARTESIAN_POINT('Ctrl Pts',(-15.625433435193,-53.179865365992,5.79419383960998));
#4484=CARTESIAN_POINT('Ctrl Pts',(-15.625433435193,-53.679865365992,5.79419383960998));
#4485=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.179865365992,5.91041200154461));
#4486=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.679865365992,5.91041200154461));
#4487=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.179865365992,5.91041200154461));
#4488=CARTESIAN_POINT('Ctrl Pts',(-15.625433435193,-53.179865365992,5.79419383960998));
#4489=CARTESIAN_POINT('Ctrl Pts',(-15.7831286173372,-53.179865365992,5.53434673270265));
#4490=CARTESIAN_POINT('Ctrl Pts',(-15.7940741129583,-53.179865365992,5.22922741251946));
#4491=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.179865365992,5.06057452055431));
#4492=CARTESIAN_POINT('Ctrl Pts',(-15.8001241706518,-53.679865365992,5.06057452055431));
#4493=CARTESIAN_POINT('Ctrl Pts',(-15.7940741129583,-53.679865365992,5.22922741251946));
#4494=CARTESIAN_POINT('Ctrl Pts',(-15.7831286173372,-53.679865365992,5.53434673270265));
#4495=CARTESIAN_POINT('Ctrl Pts',(-15.625433435193,-53.679865365992,5.79419383960998));
#4496=CARTESIAN_POINT('Ctrl Pts',(-15.5549033283434,-53.679865365992,5.91041200154461));
#4497=CARTESIAN_POINT('Origin',(-16.2360581565604,-53.179865365992,6.52843216628949));
#4498=CARTESIAN_POINT('',(-16.2601469915317,-53.179865365992,2.52031898733795));
#4499=CARTESIAN_POINT('',(-16.2360581565604,-53.179865365992,6.52843216628949));
#4500=CARTESIAN_POINT('',(-16.2563611536264,-53.179865365992,3.15024014458046));
#4501=CARTESIAN_POINT('',(-16.2601469915317,-53.679865365992,2.52031898733795));
#4502=CARTESIAN_POINT('',(-16.2601469915317,-53.179865365992,2.52031898733795));
#4503=CARTESIAN_POINT('',(-16.2360581565604,-53.679865365992,6.52843216628949));
#4504=CARTESIAN_POINT('',(-16.2601469915317,-53.679865365992,2.52031898733795));
#4505=CARTESIAN_POINT('',(-16.2360581565604,-53.179865365992,6.52843216628949));
#4506=CARTESIAN_POINT('Origin',(-15.7885897316065,-53.179865365992,6.5257428727086));
#4507=CARTESIAN_POINT('',(-15.7885897316065,-53.179865365992,6.5257428727086));
#4508=CARTESIAN_POINT('',(-34.9766475398902,-53.179865365992,6.64106345806336));
#4509=CARTESIAN_POINT('',(-15.7885897316065,-53.679865365992,6.5257428727086));
#4510=CARTESIAN_POINT('',(-16.2360581565604,-53.679865365992,6.52843216628949));
#4511=CARTESIAN_POINT('',(-15.7885897316065,-53.179865365992,6.5257428727086));
#4512=CARTESIAN_POINT('Origin',(-15.7908526716691,-53.179865365992,6.14921491280487));
#4513=CARTESIAN_POINT('',(-15.7908526716691,-53.179865365992,6.14921491280487));
#4514=CARTESIAN_POINT('',(-15.8100241987038,-53.179865365992,2.95928687104776));
#4515=CARTESIAN_POINT('',(-15.7908526716691,-53.679865365992,6.14921491280487));
#4516=CARTESIAN_POINT('',(-15.7885897316065,-53.679865365992,6.5257428727086));
#4517=CARTESIAN_POINT('',(-15.7908526716691,-53.179865365992,6.14921491280487));
#4518=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.179865365992,6.4772109235133));
#4519=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.679865365992,6.4772109235133));
#4520=CARTESIAN_POINT('Ctrl Pts',(-15.4974937553163,-53.179865365992,6.43498560165175));
#4521=CARTESIAN_POINT('Ctrl Pts',(-15.4974937553163,-53.679865365992,6.43498560165175));
#4522=CARTESIAN_POINT('Ctrl Pts',(-15.6360459154843,-53.179865365992,6.34641569577802));
#4523=CARTESIAN_POINT('Ctrl Pts',(-15.6360459154843,-53.679865365992,6.34641569577802));
#4524=CARTESIAN_POINT('Ctrl Pts',(-15.7376748793969,-53.179865365992,6.21695551102746));
#4525=CARTESIAN_POINT('Ctrl Pts',(-15.7376748793969,-53.679865365992,6.21695551102746));
#4526=CARTESIAN_POINT('Ctrl Pts',(-15.7908526716691,-53.179865365992,6.14921491280487));
#4527=CARTESIAN_POINT('Ctrl Pts',(-15.7908526716691,-53.679865365992,6.14921491280487));
#4528=CARTESIAN_POINT('',(-15.4314396085947,-53.179865365992,6.4772109235133));
#4529=CARTESIAN_POINT('Ctrl Pts',(-15.7908526716691,-53.179865365992,6.14921491280487));
#4530=CARTESIAN_POINT('Ctrl Pts',(-15.7376748793969,-53.179865365992,6.21695551102746));
#4531=CARTESIAN_POINT('Ctrl Pts',(-15.6360459154843,-53.179865365992,6.34641569577802));
#4532=CARTESIAN_POINT('Ctrl Pts',(-15.4974937553163,-53.179865365992,6.43498560165175));
#4533=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.179865365992,6.4772109235133));
#4534=CARTESIAN_POINT('',(-15.4314396085947,-53.679865365992,6.4772109235133));
#4535=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.679865365992,6.4772109235133));
#4536=CARTESIAN_POINT('Ctrl Pts',(-15.4974937553163,-53.679865365992,6.43498560165175));
#4537=CARTESIAN_POINT('Ctrl Pts',(-15.6360459154843,-53.679865365992,6.34641569577802));
#4538=CARTESIAN_POINT('Ctrl Pts',(-15.7376748793969,-53.679865365992,6.21695551102746));
#4539=CARTESIAN_POINT('Ctrl Pts',(-15.7908526716691,-53.679865365992,6.14921491280487));
#4540=CARTESIAN_POINT('',(-15.4314396085947,-53.179865365992,6.4772109235133));
#4541=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.179865365992,6.58617541488058));
#4542=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.679865365992,6.58617541488058));
#4543=CARTESIAN_POINT('Ctrl Pts',(-15.0367803678242,-53.179865365992,6.58320121726134));
#4544=CARTESIAN_POINT('Ctrl Pts',(-15.0367803678242,-53.679865365992,6.58320121726134));
#4545=CARTESIAN_POINT('Ctrl Pts',(-15.2051365734502,-53.179865365992,6.57757199826649));
#4546=CARTESIAN_POINT('Ctrl Pts',(-15.2051365734502,-53.679865365992,6.57757199826649));
#4547=CARTESIAN_POINT('Ctrl Pts',(-15.3589116073772,-53.179865365992,6.50937570710643));
#4548=CARTESIAN_POINT('Ctrl Pts',(-15.3589116073772,-53.679865365992,6.50937570710643));
#4549=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.179865365992,6.4772109235133));
#4550=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.679865365992,6.4772109235133));
#4551=CARTESIAN_POINT('',(-14.9478293736607,-53.179865365992,6.58617541488058));
#4552=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.179865365992,6.4772109235133));
#4553=CARTESIAN_POINT('Ctrl Pts',(-15.3589116073772,-53.179865365992,6.50937570710643));
#4554=CARTESIAN_POINT('Ctrl Pts',(-15.2051365734502,-53.179865365992,6.57757199826649));
#4555=CARTESIAN_POINT('Ctrl Pts',(-15.0367803678242,-53.179865365992,6.58320121726134));
#4556=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.179865365992,6.58617541488058));
#4557=CARTESIAN_POINT('',(-14.9478293736607,-53.679865365992,6.58617541488058));
#4558=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.679865365992,6.58617541488058));
#4559=CARTESIAN_POINT('Ctrl Pts',(-15.0367803678242,-53.679865365992,6.58320121726134));
#4560=CARTESIAN_POINT('Ctrl Pts',(-15.2051365734502,-53.679865365992,6.57757199826649));
#4561=CARTESIAN_POINT('Ctrl Pts',(-15.3589116073772,-53.679865365992,6.50937570710643));
#4562=CARTESIAN_POINT('Ctrl Pts',(-15.4314396085947,-53.679865365992,6.4772109235133));
#4563=CARTESIAN_POINT('',(-14.9478293736607,-53.179865365992,6.58617541488058));
#4564=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.179865365992,6.39124738589022));
#4565=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.679865365992,6.39124738589022));
#4566=CARTESIAN_POINT('Ctrl Pts',(-14.3933264140582,-53.179865365992,6.44754425344718));
#4567=CARTESIAN_POINT('Ctrl Pts',(-14.3933264140582,-53.679865365992,6.44754425344718));
#4568=CARTESIAN_POINT('Ctrl Pts',(-14.5960942336323,-53.179865365992,6.56263973785091));
#4569=CARTESIAN_POINT('Ctrl Pts',(-14.5960942336323,-53.679865365992,6.56263973785091));
#4570=CARTESIAN_POINT('Ctrl Pts',(-14.8288975664208,-53.179865365992,6.57821732288122));
#4571=CARTESIAN_POINT('Ctrl Pts',(-14.8288975664208,-53.679865365992,6.57821732288122));
#4572=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.179865365992,6.58617541488058));
#4573=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.679865365992,6.58617541488058));
#4574=CARTESIAN_POINT('',(-14.2941462147436,-53.179865365992,6.39124738589022));
#4575=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.179865365992,6.58617541488058));
#4576=CARTESIAN_POINT('Ctrl Pts',(-14.8288975664208,-53.179865365992,6.57821732288122));
#4577=CARTESIAN_POINT('Ctrl Pts',(-14.5960942336323,-53.179865365992,6.56263973785091));
#4578=CARTESIAN_POINT('Ctrl Pts',(-14.3933264140582,-53.179865365992,6.44754425344718));
#4579=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.179865365992,6.39124738589022));
#4580=CARTESIAN_POINT('',(-14.2941462147436,-53.679865365992,6.39124738589022));
#4581=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.679865365992,6.39124738589022));
#4582=CARTESIAN_POINT('Ctrl Pts',(-14.3933264140582,-53.679865365992,6.44754425344718));
#4583=CARTESIAN_POINT('Ctrl Pts',(-14.5960942336323,-53.679865365992,6.56263973785091));
#4584=CARTESIAN_POINT('Ctrl Pts',(-14.8288975664208,-53.679865365992,6.57821732288122));
#4585=CARTESIAN_POINT('Ctrl Pts',(-14.9478293736607,-53.679865365992,6.58617541488058));
#4586=CARTESIAN_POINT('',(-14.2941462147436,-53.179865365992,6.39124738589022));
#4587=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.179865365992,5.84843716313557));
#4588=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.679865365992,5.84843716313557));
#4589=CARTESIAN_POINT('Ctrl Pts',(-13.9210507080152,-53.179865365992,5.95767091591371));
#4590=CARTESIAN_POINT('Ctrl Pts',(-13.9210507080152,-53.679865365992,5.95767091591371));
#4591=CARTESIAN_POINT('Ctrl Pts',(-14.0224057020274,-53.179865365992,6.1704728280116));
#4592=CARTESIAN_POINT('Ctrl Pts',(-14.0224057020274,-53.679865365992,6.1704728280116));
#4593=CARTESIAN_POINT('Ctrl Pts',(-14.2051881644814,-53.179865365992,6.31897375241515));
#4594=CARTESIAN_POINT('Ctrl Pts',(-14.2051881644814,-53.679865365992,6.31897375241515));
#4595=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.179865365992,6.39124738589022));
#4596=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.679865365992,6.39124738589022));
#4597=CARTESIAN_POINT('',(-13.8690239839618,-53.179865365992,5.84843716313557));
#4598=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.179865365992,6.39124738589022));
#4599=CARTESIAN_POINT('Ctrl Pts',(-14.2051881644814,-53.179865365992,6.31897375241515));
#4600=CARTESIAN_POINT('Ctrl Pts',(-14.0224057020274,-53.179865365992,6.1704728280116));
#4601=CARTESIAN_POINT('Ctrl Pts',(-13.9210507080152,-53.179865365992,5.95767091591371));
#4602=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.179865365992,5.84843716313557));
#4603=CARTESIAN_POINT('',(-13.8690239839618,-53.679865365992,5.84843716313557));
#4604=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.679865365992,5.84843716313557));
#4605=CARTESIAN_POINT('Ctrl Pts',(-13.9210507080152,-53.679865365992,5.95767091591371));
#4606=CARTESIAN_POINT('Ctrl Pts',(-14.0224057020274,-53.679865365992,6.1704728280116));
#4607=CARTESIAN_POINT('Ctrl Pts',(-14.2051881644814,-53.679865365992,6.31897375241515));
#4608=CARTESIAN_POINT('Ctrl Pts',(-14.2941462147436,-53.679865365992,6.39124738589022));
#4609=CARTESIAN_POINT('',(-13.8690239839618,-53.179865365992,5.84843716313557));
#4610=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.179865365992,5.08632648815313));
#4611=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.679865365992,5.08632648815313));
#4612=CARTESIAN_POINT('Ctrl Pts',(-13.7335709736571,-53.179865365992,5.22020064184085));
#4613=CARTESIAN_POINT('Ctrl Pts',(-13.7335709736571,-53.679865365992,5.22020064184085));
#4614=CARTESIAN_POINT('Ctrl Pts',(-13.7425031667137,-53.179865365992,5.48127284229904));
#4615=CARTESIAN_POINT('Ctrl Pts',(-13.7425031667137,-53.679865365992,5.48127284229904));
#4616=CARTESIAN_POINT('Ctrl Pts',(-13.8275755316412,-53.179865365992,5.72815345665133));
#4617=CARTESIAN_POINT('Ctrl Pts',(-13.8275755316412,-53.679865365992,5.72815345665133));
#4618=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.179865365992,5.84843716313557));
#4619=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.679865365992,5.84843716313557));
#4620=CARTESIAN_POINT('',(-13.7289906706454,-53.179865365992,5.08632648815313));
#4621=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.179865365992,5.84843716313557));
#4622=CARTESIAN_POINT('Ctrl Pts',(-13.8275755316412,-53.179865365992,5.72815345665133));
#4623=CARTESIAN_POINT('Ctrl Pts',(-13.7425031667137,-53.179865365992,5.48127284229904));
#4624=CARTESIAN_POINT('Ctrl Pts',(-13.7335709736571,-53.179865365992,5.22020064184085));
#4625=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.179865365992,5.08632648815313));
#4626=CARTESIAN_POINT('',(-13.7289906706454,-53.679865365992,5.08632648815313));
#4627=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.679865365992,5.08632648815313));
#4628=CARTESIAN_POINT('Ctrl Pts',(-13.7335709736571,-53.679865365992,5.22020064184085));
#4629=CARTESIAN_POINT('Ctrl Pts',(-13.7425031667137,-53.679865365992,5.48127284229904));
#4630=CARTESIAN_POINT('Ctrl Pts',(-13.8275755316412,-53.679865365992,5.72815345665133));
#4631=CARTESIAN_POINT('Ctrl Pts',(-13.8690239839618,-53.679865365992,5.84843716313557));
#4632=CARTESIAN_POINT('',(-13.7289906706454,-53.179865365992,5.08632648815313));
#4633=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.179865365992,4.28512590753568));
#4634=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.679865365992,4.28512590753568));
#4635=CARTESIAN_POINT('Ctrl Pts',(-13.8471008894898,-53.179865365992,4.40989498662708));
#4636=CARTESIAN_POINT('Ctrl Pts',(-13.8471008894898,-53.679865365992,4.40989498662708));
#4637=CARTESIAN_POINT('Ctrl Pts',(-13.7485083698186,-53.179865365992,4.66783827681143));
#4638=CARTESIAN_POINT('Ctrl Pts',(-13.7485083698186,-53.679865365992,4.66783827681143));
#4639=CARTESIAN_POINT('Ctrl Pts',(-13.7356363824359,-53.179865365992,4.94383264112475));
#4640=CARTESIAN_POINT('Ctrl Pts',(-13.7356363824359,-53.679865365992,4.94383264112475));
#4641=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.179865365992,5.08632648815313));
#4642=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.679865365992,5.08632648815313));
#4643=CARTESIAN_POINT('',(-13.8947908211639,-53.179865365992,4.28512590753568));
#4644=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.179865365992,5.08632648815313));
#4645=CARTESIAN_POINT('Ctrl Pts',(-13.7356363824359,-53.179865365992,4.94383264112475));
#4646=CARTESIAN_POINT('Ctrl Pts',(-13.7485083698186,-53.179865365992,4.66783827681143));
#4647=CARTESIAN_POINT('Ctrl Pts',(-13.8471008894898,-53.179865365992,4.40989498662708));
#4648=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.179865365992,4.28512590753568));
#4649=CARTESIAN_POINT('',(-13.8947908211639,-53.679865365992,4.28512590753568));
#4650=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.679865365992,4.28512590753568));
#4651=CARTESIAN_POINT('Ctrl Pts',(-13.8471008894898,-53.679865365992,4.40989498662708));
#4652=CARTESIAN_POINT('Ctrl Pts',(-13.7485083698186,-53.679865365992,4.66783827681143));
#4653=CARTESIAN_POINT('Ctrl Pts',(-13.7356363824359,-53.679865365992,4.94383264112475));
#4654=CARTESIAN_POINT('Ctrl Pts',(-13.7289906706454,-53.679865365992,5.08632648815313));
#4655=CARTESIAN_POINT('',(-13.8947908211639,-53.179865365992,4.28512590753568));
#4656=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.179865365992,3.74222086799832));
#4657=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.679865365992,3.74222086799832));
#4658=CARTESIAN_POINT('Ctrl Pts',(-14.2652535202638,-53.179865365992,3.81326912625955));
#4659=CARTESIAN_POINT('Ctrl Pts',(-14.2652535202638,-53.679865365992,3.81326912625955));
#4660=CARTESIAN_POINT('Ctrl Pts',(-14.0679961926059,-53.179865365992,3.95826560947067));
#4661=CARTESIAN_POINT('Ctrl Pts',(-14.0679961926059,-53.679865365992,3.95826560947067));
#4662=CARTESIAN_POINT('Ctrl Pts',(-13.9532906583031,-53.179865365992,4.1747293647212));
#4663=CARTESIAN_POINT('Ctrl Pts',(-13.9532906583031,-53.679865365992,4.1747293647212));
#4664=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.179865365992,4.28512590753568));
#4665=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.679865365992,4.28512590753568));
#4666=CARTESIAN_POINT('',(-14.3619095856316,-53.179865365992,3.74222086799832));
#4667=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.179865365992,4.28512590753568));
#4668=CARTESIAN_POINT('Ctrl Pts',(-13.9532906583031,-53.179865365992,4.1747293647212));
#4669=CARTESIAN_POINT('Ctrl Pts',(-14.0679961926059,-53.179865365992,3.95826560947067));
#4670=CARTESIAN_POINT('Ctrl Pts',(-14.2652535202638,-53.179865365992,3.81326912625955));
#4671=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.179865365992,3.74222086799832));
#4672=CARTESIAN_POINT('',(-14.3619095856316,-53.679865365992,3.74222086799832));
#4673=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.679865365992,3.74222086799832));
#4674=CARTESIAN_POINT('Ctrl Pts',(-14.2652535202638,-53.679865365992,3.81326912625955));
#4675=CARTESIAN_POINT('Ctrl Pts',(-14.0679961926059,-53.679865365992,3.95826560947067));
#4676=CARTESIAN_POINT('Ctrl Pts',(-13.9532906583031,-53.679865365992,4.1747293647212));
#4677=CARTESIAN_POINT('Ctrl Pts',(-13.8947908211639,-53.679865365992,4.28512590753568));
#4678=CARTESIAN_POINT('',(-14.3619095856316,-53.179865365992,3.74222086799832));
#4679=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.179865365992,3.55779419543541));
#4680=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.679865365992,3.55779419543541));
#4681=CARTESIAN_POINT('Ctrl Pts',(-14.8901632483411,-53.179865365992,3.56546225048836));
#4682=CARTESIAN_POINT('Ctrl Pts',(-14.8901632483411,-53.679865365992,3.56546225048836));
#4683=CARTESIAN_POINT('Ctrl Pts',(-14.6638478476845,-53.179865365992,3.58104899641383));
#4684=CARTESIAN_POINT('Ctrl Pts',(-14.6638478476845,-53.679865365992,3.58104899641383));
#4685=CARTESIAN_POINT('Ctrl Pts',(-14.4636288508523,-53.179865365992,3.68792405831162));
#4686=CARTESIAN_POINT('Ctrl Pts',(-14.4636288508523,-53.679865365992,3.68792405831162));
#4687=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.179865365992,3.74222086799832));
#4688=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.679865365992,3.74222086799832));
#4689=CARTESIAN_POINT('',(-15.0015013659869,-53.179865365992,3.55779419543541));
#4690=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.179865365992,3.74222086799832));
#4691=CARTESIAN_POINT('Ctrl Pts',(-14.4636288508523,-53.179865365992,3.68792405831162));
#4692=CARTESIAN_POINT('Ctrl Pts',(-14.6638478476845,-53.179865365992,3.58104899641383));
#4693=CARTESIAN_POINT('Ctrl Pts',(-14.8901632483411,-53.179865365992,3.56546225048836));
#4694=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.179865365992,3.55779419543541));
#4695=CARTESIAN_POINT('',(-15.0015013659869,-53.679865365992,3.55779419543541));
#4696=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.679865365992,3.55779419543541));
#4697=CARTESIAN_POINT('Ctrl Pts',(-14.8901632483411,-53.679865365992,3.56546225048836));
#4698=CARTESIAN_POINT('Ctrl Pts',(-14.6638478476845,-53.679865365992,3.58104899641383));
#4699=CARTESIAN_POINT('Ctrl Pts',(-14.4636288508523,-53.679865365992,3.68792405831162));
#4700=CARTESIAN_POINT('Ctrl Pts',(-14.3619095856316,-53.679865365992,3.74222086799832));
#4701=CARTESIAN_POINT('',(-15.0015013659869,-53.179865365992,3.55779419543541));
#4702=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.179865365992,3.66413228971527));
#4703=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.679865365992,3.66413228971527));
#4704=CARTESIAN_POINT('Ctrl Pts',(-15.3745219659569,-53.179865365992,3.63348561354694));
#4705=CARTESIAN_POINT('Ctrl Pts',(-15.3745219659569,-53.679865365992,3.63348561354694));
#4706=CARTESIAN_POINT('Ctrl Pts',(-15.2341109400895,-53.179865365992,3.57054433418309));
#4707=CARTESIAN_POINT('Ctrl Pts',(-15.2341109400895,-53.679865365992,3.57054433418309));
#4708=CARTESIAN_POINT('Ctrl Pts',(-15.0803795544726,-53.179865365992,3.56211778293708));
#4709=CARTESIAN_POINT('Ctrl Pts',(-15.0803795544726,-53.679865365992,3.56211778293708));
#4710=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.179865365992,3.55779419543541));
#4711=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.679865365992,3.55779419543541));
#4712=CARTESIAN_POINT('',(-15.4428893598789,-53.179865365992,3.66413228971527));
#4713=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.179865365992,3.55779419543541));
#4714=CARTESIAN_POINT('Ctrl Pts',(-15.0803795544726,-53.179865365992,3.56211778293708));
#4715=CARTESIAN_POINT('Ctrl Pts',(-15.2341109400895,-53.179865365992,3.57054433418309));
#4716=CARTESIAN_POINT('Ctrl Pts',(-15.3745219659569,-53.179865365992,3.63348561354694));
#4717=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.179865365992,3.66413228971527));
#4718=CARTESIAN_POINT('',(-15.4428893598789,-53.679865365992,3.66413228971527));
#4719=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.679865365992,3.66413228971527));
#4720=CARTESIAN_POINT('Ctrl Pts',(-15.3745219659569,-53.679865365992,3.63348561354694));
#4721=CARTESIAN_POINT('Ctrl Pts',(-15.2341109400895,-53.679865365992,3.57054433418309));
#4722=CARTESIAN_POINT('Ctrl Pts',(-15.0803795544726,-53.679865365992,3.56211778293708));
#4723=CARTESIAN_POINT('Ctrl Pts',(-15.0015013659869,-53.679865365992,3.55779419543541));
#4724=CARTESIAN_POINT('',(-15.4428893598789,-53.179865365992,3.66413228971527));
#4725=CARTESIAN_POINT('Ctrl Pts',(-15.7605452971712,-53.179865365992,3.92798322022109));
#4726=CARTESIAN_POINT('Ctrl Pts',(-15.7605452971712,-53.679865365992,3.92798322022109));
#4727=CARTESIAN_POINT('Ctrl Pts',(-15.7154985537044,-53.179865365992,3.87687709847634));
#4728=CARTESIAN_POINT('Ctrl Pts',(-15.7154985537044,-53.679865365992,3.87687709847634));
#4729=CARTESIAN_POINT('Ctrl Pts',(-15.6234060877409,-53.179865365992,3.77239698703893));
#4730=CARTESIAN_POINT('Ctrl Pts',(-15.6234060877409,-53.679865365992,3.77239698703893));
#4731=CARTESIAN_POINT('Ctrl Pts',(-15.5039260887525,-53.179865365992,3.70073899677999));
#4732=CARTESIAN_POINT('Ctrl Pts',(-15.5039260887525,-53.679865365992,3.70073899677999));
#4733=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.179865365992,3.66413228971527));
#4734=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.679865365992,3.66413228971527));
#4735=CARTESIAN_POINT('',(-15.7605452971712,-53.179865365992,3.92798322022109));
#4736=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.179865365992,3.66413228971527));
#4737=CARTESIAN_POINT('Ctrl Pts',(-15.5039260887525,-53.179865365992,3.70073899677999));
#4738=CARTESIAN_POINT('Ctrl Pts',(-15.6234060877409,-53.179865365992,3.77239698703893));
#4739=CARTESIAN_POINT('Ctrl Pts',(-15.7154985537044,-53.179865365992,3.87687709847634));
#4740=CARTESIAN_POINT('Ctrl Pts',(-15.7605452971712,-53.179865365992,3.92798322022109));
#4741=CARTESIAN_POINT('',(-15.7605452971712,-53.679865365992,3.92798322022109));
#4742=CARTESIAN_POINT('Ctrl Pts',(-15.7605452971712,-53.679865365992,3.92798322022109));
#4743=CARTESIAN_POINT('Ctrl Pts',(-15.7154985537044,-53.679865365992,3.87687709847634));
#4744=CARTESIAN_POINT('Ctrl Pts',(-15.6234060877409,-53.679865365992,3.77239698703893));
#4745=CARTESIAN_POINT('Ctrl Pts',(-15.5039260887525,-53.679865365992,3.70073899677999));
#4746=CARTESIAN_POINT('Ctrl Pts',(-15.4428893598789,-53.679865365992,3.66413228971527));
#4747=CARTESIAN_POINT('',(-15.7605452971712,-53.179865365992,3.92798322022109));
#4748=CARTESIAN_POINT('Origin',(-15.7690231250222,-53.179865365992,2.51736732373898));
#4749=CARTESIAN_POINT('',(-15.7690231250222,-53.179865365992,2.51736732373898));
#4750=CARTESIAN_POINT('',(-15.7772817046025,-53.179865365992,1.14323189150572));
#4751=CARTESIAN_POINT('',(-15.7690231250222,-53.679865365992,2.51736732373898));
#4752=CARTESIAN_POINT('',(-15.7605452971712,-53.679865365992,3.92798322022109));
#4753=CARTESIAN_POINT('',(-15.7690231250222,-53.179865365992,2.51736732373898));
#4754=CARTESIAN_POINT('Origin',(-16.2601469915317,-53.179865365992,2.52031898733795));
#4755=CARTESIAN_POINT('',(-35.2244705873385,-53.179865365992,2.63429492590223));
#4756=CARTESIAN_POINT('',(-15.7690231250222,-53.679865365992,2.51736732373898));
#4757=CARTESIAN_POINT('Origin',(-13.4911611848099,-53.679865365992,5.09371901156747));
#4758=CARTESIAN_POINT('Origin',(-54.2053113423059,-53.179865365992,0.));
#4759=CARTESIAN_POINT('',(22.7076886576941,-53.179865365992,10.));
#4760=CARTESIAN_POINT('',(-49.2053113423059,-53.179865365992,10.));
#4761=CARTESIAN_POINT('',(-51.7053113423059,-53.179865365992,10.));
#4762=CARTESIAN_POINT('',(-49.2053113423059,-53.179865365992,0.));
#4763=CARTESIAN_POINT('',(-49.2053113423059,-53.179865365992,0.));
#4764=CARTESIAN_POINT('',(22.7076886576941,-53.179865365992,0.));
#4765=CARTESIAN_POINT('',(27.7076886576941,-53.179865365992,0.));
#4766=CARTESIAN_POINT('',(22.7076886576941,-53.179865365992,0.));
#4767=CARTESIAN_POINT('',(-13.0829970356625,-53.179865365992,3.61174917820095));
#4768=CARTESIAN_POINT('',(-13.0589573948457,-53.179865365992,7.61167699911304));
#4769=CARTESIAN_POINT('',(-13.0825728120466,-53.179865365992,3.68233525127786));
#4770=CARTESIAN_POINT('',(-11.5501180959437,-53.179865365992,7.60260884678131));
#4771=CARTESIAN_POINT('',(-32.8540993019239,-53.179865365992,7.73064617122584));
#4772=CARTESIAN_POINT('',(-10.9419001406327,-53.179865365992,7.56075333089919));
#4773=CARTESIAN_POINT('Ctrl Pts',(-11.5501180959437,-53.179865365992,7.60260884678131));
#4774=CARTESIAN_POINT('Ctrl Pts',(-11.4328289665597,-53.179865365992,7.60153893771994));
#4775=CARTESIAN_POINT('Ctrl Pts',(-11.2291799901218,-53.179865365992,7.59968125588383));
#4776=CARTESIAN_POINT('Ctrl Pts',(-11.0274473442138,-53.179865365992,7.57234542526143));
#4777=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.179865365992,7.56075333089919));
#4778=CARTESIAN_POINT('',(-10.4491790919006,-53.179865365992,7.36952143711172));
#4779=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.179865365992,7.56075333089919));
#4780=CARTESIAN_POINT('Ctrl Pts',(-10.8497639910406,-53.179865365992,7.54143229794249));
#4781=CARTESIAN_POINT('Ctrl Pts',(-10.6748340459972,-53.179865365992,7.50474933204626));
#4782=CARTESIAN_POINT('Ctrl Pts',(-10.5216707842693,-53.179865365992,7.41296342135046));
#4783=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.179865365992,7.36952143711172));
#4784=CARTESIAN_POINT('',(-10.1322616665437,-53.179865365992,6.98288954356071));
#4785=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.179865365992,7.36952143711172));
#4786=CARTESIAN_POINT('Ctrl Pts',(-10.3857486626935,-53.179865365992,7.31764305340637));
#4787=CARTESIAN_POINT('Ctrl Pts',(-10.2542927802496,-53.179865365992,7.21012811467747));
#4788=CARTESIAN_POINT('Ctrl Pts',(-10.1738756941945,-53.179865365992,7.06038053659198));
#4789=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.179865365992,6.98288954356071));
#4790=CARTESIAN_POINT('',(-10.0127603233512,-53.179865365992,6.43645890623372));
#4791=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.179865365992,6.98288954356071));
#4792=CARTESIAN_POINT('Ctrl Pts',(-10.0969693030987,-53.179865365992,6.89698011015736));
#4793=CARTESIAN_POINT('Ctrl Pts',(-10.0250550853855,-53.179865365992,6.72192496807218));
#4794=CARTESIAN_POINT('Ctrl Pts',(-10.0169087781024,-53.179865365992,6.53277984658392));
#4795=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.179865365992,6.43645890623372));
#4796=CARTESIAN_POINT('',(-10.342645388267,-53.179865365992,5.57348740329565));
#4797=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.179865365992,6.43645890623372));
#4798=CARTESIAN_POINT('Ctrl Pts',(-10.0263550369002,-53.179865365992,6.27394756058809));
#4799=CARTESIAN_POINT('Ctrl Pts',(-10.0531051113646,-53.179865365992,5.95417689519697));
#4800=CARTESIAN_POINT('Ctrl Pts',(-10.2471945604295,-53.179865365992,5.69898678990867));
#4801=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.179865365992,5.57348740329565));
#4802=CARTESIAN_POINT('',(-11.5179997709062,-53.179865365992,5.22856640790731));
#4803=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.179865365992,5.57348740329565));
#4804=CARTESIAN_POINT('Ctrl Pts',(-10.4904348177378,-53.179865365992,5.47233934818574));
#4805=CARTESIAN_POINT('Ctrl Pts',(-10.8411233630871,-53.179865365992,5.23232580703854));
#4806=CARTESIAN_POINT('Ctrl Pts',(-11.2699132720111,-53.179865365992,5.22994429042418));
#4807=CARTESIAN_POINT('Ctrl Pts',(-11.5179997709062,-53.179865365992,5.22856640790731));
#4808=CARTESIAN_POINT('',(-12.5439023494451,-53.179865365992,5.23473210154114));
#4809=CARTESIAN_POINT('',(-33.3581246106306,-53.179865365992,5.35982596559169));
#4810=CARTESIAN_POINT('',(-12.5536756263499,-53.179865365992,3.60856794665721));
#4811=CARTESIAN_POINT('',(-12.5652712231424,-53.179865365992,1.67919010927808));
#4812=CARTESIAN_POINT('',(-33.6325585921917,-53.179865365992,3.73525242647962));
#4813=CARTESIAN_POINT('',(-4.43103961393397,-53.179865365992,3.55975075270594));
#4814=CARTESIAN_POINT('',(-4.42884226548031,-53.179865365992,3.92536500122969));
#4815=CARTESIAN_POINT('',(-4.4415365364997,-53.179865365992,1.81318003958868));
#4816=CARTESIAN_POINT('',(-5.24178659325359,-53.179865365992,3.49913812759717));
#4817=CARTESIAN_POINT('Ctrl Pts',(-4.42884226548031,-53.179865365992,3.92536500122969));
#4818=CARTESIAN_POINT('Ctrl Pts',(-4.53341966268081,-53.179865365992,3.80025849583665));
#4819=CARTESIAN_POINT('Ctrl Pts',(-4.74540703106753,-53.179865365992,3.54665686148458));
#4820=CARTESIAN_POINT('Ctrl Pts',(-5.07485935915977,-53.179865365992,3.51511817879132));
#4821=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.179865365992,3.49913812759717));
#4822=CARTESIAN_POINT('',(-5.87910022995425,-53.179865365992,3.69396747742505));
#4823=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.179865365992,3.49913812759717));
#4824=CARTESIAN_POINT('Ctrl Pts',(-5.35505761390894,-53.179865365992,3.50783425578694));
#4825=CARTESIAN_POINT('Ctrl Pts',(-5.58200844233149,-53.179865365992,3.5252578958788));
#4826=CARTESIAN_POINT('Ctrl Pts',(-5.77995078854183,-53.179865365992,3.63766346139997));
#4827=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.179865365992,3.69396747742505));
#4828=CARTESIAN_POINT('',(-6.32609907765045,-53.179865365992,4.2287235253582));
#4829=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.179865365992,3.69396747742505));
#4830=CARTESIAN_POINT('Ctrl Pts',(-5.97030250038754,-53.179865365992,3.76565462517864));
#4831=CARTESIAN_POINT('Ctrl Pts',(-6.15687188381731,-53.179865365992,3.91230258572484));
#4832=CARTESIAN_POINT('Ctrl Pts',(-6.26885672300346,-53.179865365992,4.1216917934364));
#4833=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.179865365992,4.2287235253582));
#4834=CARTESIAN_POINT('',(-6.47960987973377,-53.179865365992,5.01820061328078));
#4835=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.179865365992,4.2287235253582));
#4836=CARTESIAN_POINT('Ctrl Pts',(-6.37150212251345,-53.179865365992,4.35065906950817));
#4837=CARTESIAN_POINT('Ctrl Pts',(-6.4662578408353,-53.179865365992,4.60513737839161));
#4838=CARTESIAN_POINT('Ctrl Pts',(-6.47503721878306,-53.179865365992,4.87673921261257));
#4839=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.179865365992,5.01820061328078));
#4840=CARTESIAN_POINT('',(-6.33027942784774,-53.179865365992,5.80312916772836));
#4841=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.179865365992,5.01820061328078));
#4842=CARTESIAN_POINT('Ctrl Pts',(-6.47360269870993,-53.179865365992,5.15724654626795));
#4843=CARTESIAN_POINT('Ctrl Pts',(-6.46197972408242,-53.179865365992,5.42627911657908));
#4844=CARTESIAN_POINT('Ctrl Pts',(-6.37318282275842,-53.179865365992,5.68036447373134));
#4845=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.179865365992,5.80312916772836));
#4846=CARTESIAN_POINT('',(-5.89319043946068,-53.179865365992,6.34348593686198));
#4847=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.179865365992,5.80312916772836));
#4848=CARTESIAN_POINT('Ctrl Pts',(-6.27680163164874,-53.179865365992,5.91317266869139));
#4849=CARTESIAN_POINT('Ctrl Pts',(-6.17275175277424,-53.179865365992,6.1272804622048));
#4850=CARTESIAN_POINT('Ctrl Pts',(-5.98462633710445,-53.179865365992,6.27277178326018));
#4851=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.179865365992,6.34348593686198));
#4852=CARTESIAN_POINT('',(-5.24541270323915,-53.179865365992,6.52786370970605));
#4853=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.179865365992,6.34348593686198));
#4854=CARTESIAN_POINT('Ctrl Pts',(-5.79306585673279,-53.179865365992,6.39779521912784));
#4855=CARTESIAN_POINT('Ctrl Pts',(-5.5909269808181,-53.179865365992,6.50743879485976));
#4856=CARTESIAN_POINT('Ctrl Pts',(-5.36129969174987,-53.179865365992,6.52101310460599));
#4857=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.179865365992,6.52786370970605));
#4858=CARTESIAN_POINT('',(-4.77951782938334,-53.179865365992,6.41319267492323));
#4859=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.179865365992,6.52786370970605));
#4860=CARTESIAN_POINT('Ctrl Pts',(-5.16161995999254,-53.179865365992,6.52329207389514));
#4861=CARTESIAN_POINT('Ctrl Pts',(-4.99897707819382,-53.179865365992,6.51441846510743));
#4862=CARTESIAN_POINT('Ctrl Pts',(-4.85117369815383,-53.179865365992,6.44624401350633));
#4863=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.179865365992,6.41319267492323));
#4864=CARTESIAN_POINT('',(-4.44836681558938,-53.179865365992,6.1247033796356));
#4865=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.179865365992,6.41319267492323));
#4866=CARTESIAN_POINT('Ctrl Pts',(-4.71505585118702,-53.179865365992,6.37403969431253));
#4867=CARTESIAN_POINT('Ctrl Pts',(-4.58804494606992,-53.179865365992,6.29689568408293));
#4868=CARTESIAN_POINT('Ctrl Pts',(-4.49445631188157,-53.179865365992,6.18152155546646));
#4869=CARTESIAN_POINT('Ctrl Pts',(-4.44836681558938,-53.179865365992,6.1247033796356));
#4870=CARTESIAN_POINT('',(-4.4397414052749,-53.179865365992,7.55987535023605));
#4871=CARTESIAN_POINT('',(-4.46335682247588,-53.179865365992,3.63053360240086));
#4872=CARTESIAN_POINT('',(-3.95134619012377,-53.179865365992,7.55694008588264));
#4873=CARTESIAN_POINT('',(-29.0547133490139,-53.179865365992,7.7078117907765));
#4874=CARTESIAN_POINT('',(-3.97538583094063,-53.179865365992,3.55701226497055));
#4875=CARTESIAN_POINT('',(-3.98698142773318,-53.179865365992,1.62763442759142));
#4876=CARTESIAN_POINT('',(-29.3065798813274,-53.179865365992,3.70925321373212));
#4877=CARTESIAN_POINT('',(-17.4682192676127,-53.179865365992,3.995546246547));
#4878=CARTESIAN_POINT('',(-17.9967803949472,-53.179865365992,3.67129556877814));
#4879=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.179865365992,3.995546246547));
#4880=CARTESIAN_POINT('Ctrl Pts',(-17.5553439244099,-53.179865365992,3.92580159601428));
#4881=CARTESIAN_POINT('Ctrl Pts',(-17.7180955229753,-53.179865365992,3.79551639567177));
#4882=CARTESIAN_POINT('Ctrl Pts',(-17.9083660412272,-53.179865365992,3.71070532952565));
#4883=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.179865365992,3.67129556877814));
#4884=CARTESIAN_POINT('',(-18.5375900458662,-53.179865365992,3.57904615436238));
#4885=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.179865365992,3.67129556877814));
#4886=CARTESIAN_POINT('Ctrl Pts',(-18.0830938799431,-53.179865365992,3.64396312284845));
#4887=CARTESIAN_POINT('Ctrl Pts',(-18.259059311806,-53.179865365992,3.58824105749433));
#4888=CARTESIAN_POINT('Ctrl Pts',(-18.4435795459125,-53.179865365992,3.58214964469488));
#4889=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.179865365992,3.57904615436238));
#4890=CARTESIAN_POINT('',(-19.2701530571004,-53.179865365992,3.81537652122272));
#4891=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.179865365992,3.57904615436238));
#4892=CARTESIAN_POINT('Ctrl Pts',(-18.6828386529162,-53.179865365992,3.58566946116175));
#4893=CARTESIAN_POINT('Ctrl Pts',(-18.9497452282753,-53.179865365992,3.59784034645476));
#4894=CARTESIAN_POINT('Ctrl Pts',(-19.1698346772575,-53.179865365992,3.74726683523292));
#4895=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.179865365992,3.81537652122272));
#4896=CARTESIAN_POINT('',(-19.5230375141276,-53.179865365992,4.41445146565348));
#4897=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.179865365992,3.81537652122272));
#4898=CARTESIAN_POINT('Ctrl Pts',(-19.344448789443,-53.179865365992,3.90010882505328));
#4899=CARTESIAN_POINT('Ctrl Pts',(-19.4948805990596,-53.179865365992,4.07167229709018));
#4900=CARTESIAN_POINT('Ctrl Pts',(-19.5135756389659,-53.179865365992,4.29926364436147));
#4901=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.179865365992,4.41445146565348));
#4902=CARTESIAN_POINT('',(-19.4252127413053,-53.179865365992,4.80131920274653));
#4903=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.179865365992,4.41445146565348));
#4904=CARTESIAN_POINT('Ctrl Pts',(-19.5189316288004,-53.179865365992,4.48324758576116));
#4905=CARTESIAN_POINT('Ctrl Pts',(-19.5108614835729,-53.179865365992,4.61846682256525));
#4906=CARTESIAN_POINT('Ctrl Pts',(-19.4534263398067,-53.179865365992,4.74108570132111));
#4907=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.179865365992,4.80131920274653));
#4908=CARTESIAN_POINT('',(-19.1697600466847,-53.179865365992,5.08355424888615));
#4909=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.179865365992,4.80131920274653));
#4910=CARTESIAN_POINT('Ctrl Pts',(-19.3902588775645,-53.179865365992,4.8569193384579));
#4911=CARTESIAN_POINT('Ctrl Pts',(-19.3216563003775,-53.179865365992,4.96604354372531));
#4912=CARTESIAN_POINT('Ctrl Pts',(-19.2197458559826,-53.179865365992,5.04488398839039));
#4913=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.179865365992,5.08355424888615));
#4914=CARTESIAN_POINT('',(-18.8140923165499,-53.179865365992,5.24240219533447));
#4915=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.179865365992,5.08355424888615));
#4916=CARTESIAN_POINT('Ctrl Pts',(-19.1146770537267,-53.179865365992,5.11668298446601));
#4917=CARTESIAN_POINT('Ctrl Pts',(-19.0026016258659,-53.179865365992,5.18408885722665));
#4918=CARTESIAN_POINT('Ctrl Pts',(-18.8776384238003,-53.179865365992,5.22274488689186));
#4919=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.179865365992,5.24240219533447));
#4920=CARTESIAN_POINT('',(-18.3770954516211,-53.179865365992,5.31344671929976));
#4921=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.179865365992,5.24240219533447));
#4922=CARTESIAN_POINT('Ctrl Pts',(-18.7536710889591,-53.179865365992,5.25534807882544));
#4923=CARTESIAN_POINT('Ctrl Pts',(-18.6092254882953,-53.179865365992,5.28629706797279));
#4924=CARTESIAN_POINT('Ctrl Pts',(-18.4624658855733,-53.179865365992,5.30346189549737));
#4925=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.179865365992,5.31344671929976));
#4926=CARTESIAN_POINT('',(-17.5002418800229,-53.179865365992,5.47734798281611));
#4927=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.179865365992,5.31344671929976));
#4928=CARTESIAN_POINT('Ctrl Pts',(-18.2038469939087,-53.179865365992,5.33336458563949));
#4929=CARTESIAN_POINT('Ctrl Pts',(-17.9075380623137,-53.179865365992,5.36743035322383));
#4930=CARTESIAN_POINT('Ctrl Pts',(-17.6197595666939,-53.179865365992,5.44509356604418));
#4931=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.179865365992,5.47734798281611));
#4932=CARTESIAN_POINT('',(-17.4967425178558,-53.179865365992,5.60556925687899));
#4933=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.179865365992,5.47734798281611));
#4934=CARTESIAN_POINT('Ctrl Pts',(-17.4993068375188,-53.179865365992,5.50485363410765));
#4935=CARTESIAN_POINT('Ctrl Pts',(-17.4978541473476,-53.179865365992,5.54758665331348));
#4936=CARTESIAN_POINT('Ctrl Pts',(-17.4970345654438,-53.179865365992,5.59033605225441));
#4937=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.179865365992,5.60556925687899));
#4938=CARTESIAN_POINT('',(-17.6333524930269,-53.179865365992,6.02931731588761));
#4939=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.179865365992,5.60556925687899));
#4940=CARTESIAN_POINT('Ctrl Pts',(-17.498168638955,-53.179865365992,5.69432717939977));
#4941=CARTESIAN_POINT('Ctrl Pts',(-17.5006832297982,-53.179865365992,5.85082851673556));
#4942=CARTESIAN_POINT('Ctrl Pts',(-17.593273655748,-53.179865365992,5.975396585771));
#4943=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.179865365992,6.02931731588761));
#4944=CARTESIAN_POINT('',(-18.1916872912747,-53.179865365992,6.19911533836006));
#4945=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.179865365992,6.02931731588761));
#4946=CARTESIAN_POINT('Ctrl Pts',(-17.7088840405669,-53.179865365992,6.07817617392611));
#4947=CARTESIAN_POINT('Ctrl Pts',(-17.8778445896253,-53.179865365992,6.18747116058281));
#4948=CARTESIAN_POINT('Ctrl Pts',(-18.0799373752263,-53.179865365992,6.19496919800943));
#4949=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.179865365992,6.19911533836006));
#4950=CARTESIAN_POINT('',(-18.7053764964439,-53.179865365992,6.0794172185496));
#4951=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.179865365992,6.19911533836006));
#4952=CARTESIAN_POINT('Ctrl Pts',(-18.2944335725293,-53.179865365992,6.19804747987701));
#4953=CARTESIAN_POINT('Ctrl Pts',(-18.4753742961506,-53.179865365992,6.19616693408705));
#4954=CARTESIAN_POINT('Ctrl Pts',(-18.635972972123,-53.179865365992,6.11464662814405));
#4955=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.179865365992,6.0794172185496));
#4956=CARTESIAN_POINT('',(-18.9507842825755,-53.179865365992,5.65250789160473));
#4957=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.179865365992,6.0794172185496));
#4958=CARTESIAN_POINT('Ctrl Pts',(-18.757895262004,-53.179865365992,6.02680712716127));
#4959=CARTESIAN_POINT('Ctrl Pts',(-18.8773180803213,-53.179865365992,5.90717664235211));
#4960=CARTESIAN_POINT('Ctrl Pts',(-18.9244049061434,-53.179865365992,5.74395133954849));
#4961=CARTESIAN_POINT('Ctrl Pts',(-18.9507842825755,-53.179865365992,5.65250789160473));
#4962=CARTESIAN_POINT('',(-19.4306002866422,-53.179865365992,5.72087712234595));
#4963=CARTESIAN_POINT('',(-36.072481221998,-53.179865365992,8.09218734958669));
#4964=CARTESIAN_POINT('',(-19.2120672735058,-53.179865365992,6.21616194667383));
#4965=CARTESIAN_POINT('Ctrl Pts',(-19.4306002866422,-53.179865365992,5.72087712234595));
#4966=CARTESIAN_POINT('Ctrl Pts',(-19.4066428086306,-53.179865365992,5.81594079432957));
#4967=CARTESIAN_POINT('Ctrl Pts',(-19.3618486122387,-53.179865365992,5.99368491207509));
#4968=CARTESIAN_POINT('Ctrl Pts',(-19.2596203448692,-53.179865365992,6.14552920702489));
#4969=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.179865365992,6.21616194667383));
#4970=CARTESIAN_POINT('',(-18.776486910036,-53.179865365992,6.50550037388947));
#4971=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.179865365992,6.21616194667383));
#4972=CARTESIAN_POINT('Ctrl Pts',(-19.1536771147305,-53.179865365992,6.2765561085174));
#4973=CARTESIAN_POINT('Ctrl Pts',(-19.0298605487794,-53.179865365992,6.4046221710152));
#4974=CARTESIAN_POINT('Ctrl Pts',(-18.8640850204553,-53.179865365992,6.47062405448186));
#4975=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.179865365992,6.50550037388947));
#4976=CARTESIAN_POINT('',(-18.118304103034,-53.179865365992,6.60523002714498));
#4977=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.179865365992,6.50550037388947));
#4978=CARTESIAN_POINT('Ctrl Pts',(-18.6749136626558,-53.179865365992,6.53510734738655));
#4979=CARTESIAN_POINT('Ctrl Pts',(-18.4601123887653,-53.179865365992,6.59771847568304));
#4980=CARTESIAN_POINT('Ctrl Pts',(-18.2362883123438,-53.179865365992,6.60263721562776));
#4981=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.179865365992,6.60523002714498));
#4982=CARTESIAN_POINT('',(-17.5158380193312,-53.179865365992,6.51429515793513));
#4983=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.179865365992,6.60523002714498));
#4984=CARTESIAN_POINT('Ctrl Pts',(-18.0053934854633,-53.179865365992,6.60264984351407));
#4985=CARTESIAN_POINT('Ctrl Pts',(-17.8004670622934,-53.179865365992,6.59796695527928));
#4986=CARTESIAN_POINT('Ctrl Pts',(-17.6040382419592,-53.179865365992,6.5402231907059));
#4987=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.179865365992,6.51429515793513));
#4988=CARTESIAN_POINT('',(-17.1761086885063,-53.179865365992,6.29123983440859));
#4989=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.179865365992,6.51429515793513));
#4990=CARTESIAN_POINT('Ctrl Pts',(-17.4459871038143,-53.179865365992,6.48533360629333));
#4991=CARTESIAN_POINT('Ctrl Pts',(-17.318304057179,-53.179865365992,6.43239372538342));
#4992=CARTESIAN_POINT('Ctrl Pts',(-17.220436388833,-53.179865365992,6.33524286655711));
#4993=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.179865365992,6.29123983440859));
#4994=CARTESIAN_POINT('',(-17.0253149150884,-53.179865365992,5.95744901920609));
#4995=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.179865365992,6.29123983440859));
#4996=CARTESIAN_POINT('Ctrl Pts',(-17.1424310659699,-53.179865365992,6.24288855863731));
#4997=CARTESIAN_POINT('Ctrl Pts',(-17.0715322927043,-53.179865365992,6.1410985307247));
#4998=CARTESIAN_POINT('Ctrl Pts',(-17.0412256178987,-53.179865365992,6.02067183602851));
#4999=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.179865365992,5.95744901920609));
#5000=CARTESIAN_POINT('',(-17.0034809294599,-53.179865365992,5.50437630510761));
#5001=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.179865365992,5.95744901920609));
#5002=CARTESIAN_POINT('Ctrl Pts',(-17.0185864578987,-53.179865365992,5.89880404418814));
#5003=CARTESIAN_POINT('Ctrl Pts',(-17.0013247399637,-53.179865365992,5.74835157216759));
#5004=CARTESIAN_POINT('Ctrl Pts',(-17.0026638140218,-53.179865365992,5.59683383612124));
#5005=CARTESIAN_POINT('Ctrl Pts',(-17.0034809294599,-53.179865365992,5.50437630510761));
#5006=CARTESIAN_POINT('',(-17.0074164761489,-53.179865365992,4.84954527780918));
#5007=CARTESIAN_POINT('',(-17.0226606543263,-53.179865365992,2.31308423568909));
#5008=CARTESIAN_POINT('',(-16.98261829722,-53.179865365992,3.98171337204134));
#5009=CARTESIAN_POINT('Ctrl Pts',(-17.0074164761489,-53.179865365992,4.84954527780918));
#5010=CARTESIAN_POINT('Ctrl Pts',(-17.0096035253256,-53.179865365992,4.66295266735441));
#5011=CARTESIAN_POINT('Ctrl Pts',(-17.0129992185845,-53.179865365992,4.37324209466735));
#5012=CARTESIAN_POINT('Ctrl Pts',(-16.9905933255679,-53.179865365992,4.08449013198545));
#5013=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.179865365992,3.98171337204134));
#5014=CARTESIAN_POINT('',(-16.8591912401334,-53.179865365992,3.63444417580955));
#5015=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.179865365992,3.98171337204134));
#5016=CARTESIAN_POINT('Ctrl Pts',(-16.9694065702617,-53.179865365992,3.92189530528231));
#5017=CARTESIAN_POINT('Ctrl Pts',(-16.9427091394009,-53.179865365992,3.80101870023571));
#5018=CARTESIAN_POINT('Ctrl Pts',(-16.887220354007,-53.179865365992,3.69034759716629));
#5019=CARTESIAN_POINT('Ctrl Pts',(-16.8591912401334,-53.179865365992,3.63444417580955));
#5020=CARTESIAN_POINT('',(-17.3721425294028,-53.179865365992,3.63752702262646));
#5021=CARTESIAN_POINT('',(-35.7771313390618,-53.179865365992,3.74814134869239));
#5022=CARTESIAN_POINT('Ctrl Pts',(-17.3721425294028,-53.179865365992,3.63752702262646));
#5023=CARTESIAN_POINT('Ctrl Pts',(-17.3951539748287,-53.179865365992,3.69224212851364));
#5024=CARTESIAN_POINT('Ctrl Pts',(-17.4434740943284,-53.179865365992,3.80713453991937));
#5025=CARTESIAN_POINT('Ctrl Pts',(-17.4597135255632,-53.179865365992,3.93078285381297));
#5026=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.179865365992,3.995546246547));
#5027=CARTESIAN_POINT('',(-7.52569856268888,-53.179865365992,3.9357915116463));
#5028=CARTESIAN_POINT('',(-8.0542590939877,-53.179865365992,3.61154083029525));
#5029=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.179865365992,3.9357915116463));
#5030=CARTESIAN_POINT('Ctrl Pts',(-7.61282300775472,-53.179865365992,3.86604684355902));
#5031=CARTESIAN_POINT('Ctrl Pts',(-7.7755743196687,-53.179865365992,3.73576152327227));
#5032=CARTESIAN_POINT('Ctrl Pts',(-7.96584472473011,-53.179865365992,3.65095056929209));
#5033=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.179865365992,3.61154083029525));
#5034=CARTESIAN_POINT('',(-8.59506904292454,-53.179865365992,3.51929141767058));
#5035=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.179865365992,3.61154083029525));
#5036=CARTESIAN_POINT('Ctrl Pts',(-8.14057277859556,-53.179865365992,3.584208385098));
#5037=CARTESIAN_POINT('Ctrl Pts',(-8.31653833189115,-53.179865365992,3.52848641164841));
#5038=CARTESIAN_POINT('Ctrl Pts',(-8.50105864803172,-53.179865365992,3.5223949354539));
#5039=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.179865365992,3.51929141767058));
#5040=CARTESIAN_POINT('',(-9.32763235217657,-53.179865365992,3.75562178632202));
#5041=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.179865365992,3.51929141767058));
#5042=CARTESIAN_POINT('Ctrl Pts',(-8.74031798210232,-53.179865365992,3.52591463797103));
#5043=CARTESIAN_POINT('Ctrl Pts',(-9.00722473754987,-53.179865365992,3.5380853446971));
#5044=CARTESIAN_POINT('Ctrl Pts',(-9.22731417201049,-53.179865365992,3.68751210681568));
#5045=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.179865365992,3.75562178632202));
#5046=CARTESIAN_POINT('',(-9.58051651118595,-53.179865365992,4.35469672896169));
#5047=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.179865365992,3.75562178632202));
#5048=CARTESIAN_POINT('Ctrl Pts',(-9.40192781231176,-53.179865365992,3.84035412403689));
#5049=CARTESIAN_POINT('Ctrl Pts',(-9.55235916430731,-53.179865365992,4.01191777135795));
#5050=CARTESIAN_POINT('Ctrl Pts',(-9.57105448710629,-53.179865365992,4.23950893215589));
#5051=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.179865365992,4.35469672896169));
#5052=CARTESIAN_POINT('',(-9.48269233439936,-53.179865365992,4.74156446963692));
#5053=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.179865365992,4.35469672896169));
#5054=CARTESIAN_POINT('Ctrl Pts',(-9.57641029336573,-53.179865365992,4.42349280752662));
#5055=CARTESIAN_POINT('Ctrl Pts',(-9.56833950463621,-53.179865365992,4.5587117948835));
#5056=CARTESIAN_POINT('Ctrl Pts',(-9.51090539074124,-53.179865365992,4.6813309230899));
#5057=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.179865365992,4.74156446963692));
#5058=CARTESIAN_POINT('',(-9.22723904374304,-53.179865365992,5.02379951219435));
#5059=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.179865365992,4.74156446963692));
#5060=CARTESIAN_POINT('Ctrl Pts',(-9.44773836796607,-53.179865365992,4.79716460774061));
#5061=CARTESIAN_POINT('Ctrl Pts',(-9.37913557876154,-53.179865365992,4.90628883435305));
#5062=CARTESIAN_POINT('Ctrl Pts',(-9.27722495093455,-53.179865365992,4.9851292565768));
#5063=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.179865365992,5.02379951219435));
#5064=CARTESIAN_POINT('',(-8.87157190964391,-53.179865365992,5.18264746222486));
#5065=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.179865365992,5.02379951219435));
#5066=CARTESIAN_POINT('Ctrl Pts',(-9.17215594204646,-53.179865365992,5.05692822380969));
#5067=CARTESIAN_POINT('Ctrl Pts',(-9.06008061134495,-53.179865365992,5.1243338563112));
#5068=CARTESIAN_POINT('Ctrl Pts',(-8.93511769640171,-53.179865365992,5.16299009928601));
#5069=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.179865365992,5.18264746222486));
#5070=CARTESIAN_POINT('',(-8.43457474669731,-53.179865365992,5.25369198439906));
#5071=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.179865365992,5.18264746222486));
#5072=CARTESIAN_POINT('Ctrl Pts',(-8.8111503821793,-53.179865365992,5.19559333447541));
#5073=CARTESIAN_POINT('Ctrl Pts',(-8.66670465412422,-53.179865365992,5.22654217044505));
#5074=CARTESIAN_POINT('Ctrl Pts',(-8.5199449747195,-53.179865365992,5.24370711931336));
#5075=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.179865365992,5.25369198439906));
#5076=CARTESIAN_POINT('',(-7.5577205790634,-53.179865365992,5.41759324433322));
#5077=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.179865365992,5.25369198439906));
#5078=CARTESIAN_POINT('Ctrl Pts',(-8.2613260875168,-53.179865365992,5.27360983304771));
#5079=CARTESIAN_POINT('Ctrl Pts',(-7.96501695024711,-53.179865365992,5.30767555440606));
#5080=CARTESIAN_POINT('Ctrl Pts',(-7.67723826543409,-53.179865365992,5.38533882489627));
#5081=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.179865365992,5.41759324433322));
#5082=CARTESIAN_POINT('',(-7.55422240896771,-53.179865365992,5.54581452556047));
#5083=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.179865365992,5.41759324433322));
#5084=CARTESIAN_POINT('Ctrl Pts',(-7.55678606174325,-53.179865365992,5.44509890195052));
#5085=CARTESIAN_POINT('Ctrl Pts',(-7.55533418731718,-53.179865365992,5.48783193643314));
#5086=CARTESIAN_POINT('Ctrl Pts',(-7.5545144957132,-53.179865365992,5.53058132214222));
#5087=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.179865365992,5.54581452556047));
#5088=CARTESIAN_POINT('',(-7.69083238413879,-53.179865365992,5.9695625845691));
#5089=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.179865365992,5.54581452556047));
#5090=CARTESIAN_POINT('Ctrl Pts',(-7.5556485300669,-53.179865365992,5.63457244808125));
#5091=CARTESIAN_POINT('Ctrl Pts',(-7.55816312091004,-53.179865365992,5.79107378541704));
#5092=CARTESIAN_POINT('Ctrl Pts',(-7.65075354685986,-53.179865365992,5.91564185445248));
#5093=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.179865365992,5.9695625845691));
#5094=CARTESIAN_POINT('',(-8.24916628833305,-53.179865365992,6.13936060166827));
#5095=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.179865365992,5.9695625845691));
#5096=CARTESIAN_POINT('Ctrl Pts',(-7.76636372904896,-53.179865365992,6.01842144003674));
#5097=CARTESIAN_POINT('Ctrl Pts',(-7.9353239259101,-53.179865365992,6.12771648632568));
#5098=CARTESIAN_POINT('Ctrl Pts',(-8.13741645827153,-53.179865365992,6.13521448225754));
#5099=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.179865365992,6.13936060166827));
#5100=CARTESIAN_POINT('',(-8.76285608953794,-53.179865365992,6.01966248543999));
#5101=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.179865365992,6.13936060166827));
#5102=CARTESIAN_POINT('Ctrl Pts',(-8.35191255569615,-53.179865365992,6.13829270721667));
#5103=CARTESIAN_POINT('Ctrl Pts',(-8.53285344012592,-53.179865365992,6.13641209615887));
#5104=CARTESIAN_POINT('Ctrl Pts',(-8.69345236449357,-53.179865365992,6.05489189650149));
#5105=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.179865365992,6.01966248543999));
#5106=CARTESIAN_POINT('',(-9.00826387566953,-53.179865365992,5.59275315849512));
#5107=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.179865365992,6.01966248543999));
#5108=CARTESIAN_POINT('Ctrl Pts',(-8.81537485509798,-53.179865365992,5.96705239405166));
#5109=CARTESIAN_POINT('Ctrl Pts',(-8.93479767341532,-53.179865365992,5.8474219092425));
#5110=CARTESIAN_POINT('Ctrl Pts',(-8.98188449923743,-53.179865365992,5.68419660643888));
#5111=CARTESIAN_POINT('Ctrl Pts',(-9.00826387566953,-53.179865365992,5.59275315849512));
#5112=CARTESIAN_POINT('',(-9.48807958171841,-53.179865365992,5.66112238744525));
#5113=CARTESIAN_POINT('',(-31.0064669704714,-53.179865365992,8.72729036709293));
#5114=CARTESIAN_POINT('',(-9.26954597254625,-53.179865365992,6.15640720819095));
#5115=CARTESIAN_POINT('Ctrl Pts',(-9.48807958171841,-53.179865365992,5.66112238744525));
#5116=CARTESIAN_POINT('Ctrl Pts',(-9.46412200367381,-53.179865365992,5.75618606507093));
#5117=CARTESIAN_POINT('Ctrl Pts',(-9.41932761412119,-53.179865365992,5.93393021767033));
#5118=CARTESIAN_POINT('Ctrl Pts',(-9.31709914515962,-53.179865365992,6.08577447495683));
#5119=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.179865365992,6.15640720819095));
#5120=CARTESIAN_POINT('',(-8.83396560907652,-53.179865365992,6.44574563540659));
#5121=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.179865365992,6.15640720819095));
#5122=CARTESIAN_POINT('Ctrl Pts',(-9.21115613855355,-53.179865365992,6.21680140640416));
#5123=CARTESIAN_POINT('Ctrl Pts',(-9.08733990039489,-53.179865365992,6.34486791932323));
#5124=CARTESIAN_POINT('Ctrl Pts',(-8.92156409420348,-53.179865365992,6.41086942493555));
#5125=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.179865365992,6.44574563540659));
#5126=CARTESIAN_POINT('',(-8.17578280207455,-53.179865365992,6.54547528866209));
#5127=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.179865365992,6.44574563540659));
#5128=CARTESIAN_POINT('Ctrl Pts',(-8.73239256088601,-53.179865365992,6.47535260858206));
#5129=CARTESIAN_POINT('Ctrl Pts',(-8.51759131014624,-53.179865365992,6.53796385223384));
#5130=CARTESIAN_POINT('Ctrl Pts',(-8.29376721657436,-53.179865365992,6.54288251402918));
#5131=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.179865365992,6.54547528866209));
#5132=CARTESIAN_POINT('',(-7.57331731440734,-53.179865365992,6.45454042303442));
#5133=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.179865365992,6.54547528866209));
#5134=CARTESIAN_POINT('Ctrl Pts',(-8.06287217977795,-53.179865365992,6.54289512564256));
#5135=CARTESIAN_POINT('Ctrl Pts',(-7.85794594197565,-53.179865365992,6.53821227924818));
#5136=CARTESIAN_POINT('Ctrl Pts',(-7.66151733762561,-53.179865365992,6.48046845326743));
#5137=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.179865365992,6.45454042303442));
#5138=CARTESIAN_POINT('',(-7.23358798358248,-53.179865365992,6.23148509950789));
#5139=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.179865365992,6.45454042303442));
#5140=CARTESIAN_POINT('Ctrl Pts',(-7.5034663988905,-53.179865365992,6.42557887139263));
#5141=CARTESIAN_POINT('Ctrl Pts',(-7.37578335225521,-53.179865365992,6.37263899048271));
#5142=CARTESIAN_POINT('Ctrl Pts',(-7.27791568390916,-53.179865365992,6.2754881316564));
#5143=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.179865365992,6.23148509950789));
#5144=CARTESIAN_POINT('',(-7.08279480620025,-53.179865365992,5.89769428788757));
#5145=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.179865365992,6.23148509950789));
#5146=CARTESIAN_POINT('Ctrl Pts',(-7.19991003949418,-53.179865365992,6.18313387347449));
#5147=CARTESIAN_POINT('Ctrl Pts',(-7.12901072727269,-53.179865365992,6.08134414836999));
#5148=CARTESIAN_POINT('Ctrl Pts',(-7.0987049892125,-53.179865365992,5.96091715184107));
#5149=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.179865365992,5.89769428788757));
#5150=CARTESIAN_POINT('',(-7.06096022453606,-53.179865365992,5.44462157020691));
#5151=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.179865365992,5.89769428788757));
#5152=CARTESIAN_POINT('Ctrl Pts',(-7.07606611147406,-53.179865365992,5.83904931548419));
#5153=CARTESIAN_POINT('Ctrl Pts',(-7.05880378783699,-53.179865365992,5.68859688235872));
#5154=CARTESIAN_POINT('Ctrl Pts',(-7.06014301548669,-53.179865365992,5.53707911046172));
#5155=CARTESIAN_POINT('Ctrl Pts',(-7.06096022453606,-53.179865365992,5.44462157020691));
#5156=CARTESIAN_POINT('',(-7.06489577122509,-53.179865365992,4.78979054290848));
#5157=CARTESIAN_POINT('',(-7.08013994940243,-53.179865365992,2.25332950078842));
#5158=CARTESIAN_POINT('',(-7.0400975922962,-53.179865365992,3.92195863714063));
#5159=CARTESIAN_POINT('Ctrl Pts',(-7.06489577122509,-53.179865365992,4.78979054290848));
#5160=CARTESIAN_POINT('Ctrl Pts',(-7.06708244278517,-53.179865365992,4.60319793943947));
#5161=CARTESIAN_POINT('Ctrl Pts',(-7.07047754955563,-53.179865365992,4.31348739354906));
#5162=CARTESIAN_POINT('Ctrl Pts',(-7.04807236697943,-53.179865365992,4.02473539840139));
#5163=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.179865365992,3.92195863714063));
#5164=CARTESIAN_POINT('',(-6.91667053520953,-53.179865365992,3.57468944090885));
#5165=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.179865365992,3.92195863714063));
#5166=CARTESIAN_POINT('Ctrl Pts',(-7.0268855854292,-53.179865365992,3.86214059765933));
#5167=CARTESIAN_POINT('Ctrl Pts',(-7.00018761831887,-53.179865365992,3.74126418071879));
#5168=CARTESIAN_POINT('Ctrl Pts',(-6.94469935510409,-53.179865365992,3.63059289451729));
#5169=CARTESIAN_POINT('Ctrl Pts',(-6.91667053520953,-53.179865365992,3.57468944090885));
#5170=CARTESIAN_POINT('',(-7.42962242051468,-53.179865365992,3.57777229130795));
#5171=CARTESIAN_POINT('',(-30.8058712846178,-53.179865365992,3.71826398303311));
#5172=CARTESIAN_POINT('Ctrl Pts',(-7.42962242051468,-53.179865365992,3.57777229130795));
#5173=CARTESIAN_POINT('Ctrl Pts',(-7.45263362139022,-53.179865365992,3.63248741261884));
#5174=CARTESIAN_POINT('Ctrl Pts',(-7.50095325751147,-53.179865365992,3.74737992806695));
#5175=CARTESIAN_POINT('Ctrl Pts',(-7.51719277213793,-53.179865365992,3.87102813710679));
#5176=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.179865365992,3.9357915116463));
#5177=CARTESIAN_POINT('Origin',(-11.506978132858,-53.179865365992,5.70054171640723));
#5178=CARTESIAN_POINT('',(-12.5410654734007,-53.179865365992,5.70675660061337));
#5179=CARTESIAN_POINT('',(-11.506978132858,-53.179865365992,5.70054171640723));
#5180=CARTESIAN_POINT('',(-32.8382440643149,-53.179865365992,5.82874302256084));
#5181=CARTESIAN_POINT('',(-12.5410654734007,-53.679865365992,5.70675660061337));
#5182=CARTESIAN_POINT('',(-12.5410654734007,-53.179865365992,5.70675660061337));
#5183=CARTESIAN_POINT('',(-11.506978132858,-53.679865365992,5.70054171640723));
#5184=CARTESIAN_POINT('',(-12.5410654734007,-53.679865365992,5.70675660061337));
#5185=CARTESIAN_POINT('',(-11.506978132858,-53.179865365992,5.70054171640723));
#5186=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.179865365992,5.88715590104679));
#5187=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.679865365992,5.88715590104679));
#5188=CARTESIAN_POINT('Ctrl Pts',(-10.8720718882405,-53.179865365992,5.83234472764785));
#5189=CARTESIAN_POINT('Ctrl Pts',(-10.8720718882405,-53.679865365992,5.83234472764785));
#5190=CARTESIAN_POINT('Ctrl Pts',(-11.0960788420725,-53.179865365992,5.7027490599856));
#5191=CARTESIAN_POINT('Ctrl Pts',(-11.0960788420725,-53.679865365992,5.7027490599856));
#5192=CARTESIAN_POINT('Ctrl Pts',(-11.3566264775651,-53.179865365992,5.70134940278745));
#5193=CARTESIAN_POINT('Ctrl Pts',(-11.3566264775651,-53.679865365992,5.70134940278745));
#5194=CARTESIAN_POINT('Ctrl Pts',(-11.506978132858,-53.179865365992,5.70054171640723));
#5195=CARTESIAN_POINT('Ctrl Pts',(-11.506978132858,-53.679865365992,5.70054171640723));
#5196=CARTESIAN_POINT('',(-10.7773304187876,-53.179865365992,5.88715590104679));
#5197=CARTESIAN_POINT('Ctrl Pts',(-11.506978132858,-53.179865365992,5.70054171640723));
#5198=CARTESIAN_POINT('Ctrl Pts',(-11.3566264775651,-53.179865365992,5.70134940278745));
#5199=CARTESIAN_POINT('Ctrl Pts',(-11.0960788420725,-53.179865365992,5.7027490599856));
#5200=CARTESIAN_POINT('Ctrl Pts',(-10.8720718882405,-53.179865365992,5.83234472764785));
#5201=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.179865365992,5.88715590104679));
#5202=CARTESIAN_POINT('',(-10.7773304187876,-53.679865365992,5.88715590104679));
#5203=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.679865365992,5.88715590104679));
#5204=CARTESIAN_POINT('Ctrl Pts',(-10.8720718882405,-53.679865365992,5.83234472764785));
#5205=CARTESIAN_POINT('Ctrl Pts',(-11.0960788420725,-53.679865365992,5.7027490599856));
#5206=CARTESIAN_POINT('Ctrl Pts',(-11.3566264775651,-53.679865365992,5.70134940278745));
#5207=CARTESIAN_POINT('Ctrl Pts',(-11.506978132858,-53.679865365992,5.70054171640723));
#5208=CARTESIAN_POINT('',(-10.7773304187876,-53.179865365992,5.88715590104679));
#5209=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.179865365992,6.42336751198972));
#5210=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.679865365992,6.42336751198972));
#5211=CARTESIAN_POINT('Ctrl Pts',(-10.5661232291359,-53.179865365992,6.31628811244045));
#5212=CARTESIAN_POINT('Ctrl Pts',(-10.5661232291359,-53.679865365992,6.31628811244045));
#5213=CARTESIAN_POINT('Ctrl Pts',(-10.5805093317121,-53.179865365992,6.11285760158389));
#5214=CARTESIAN_POINT('Ctrl Pts',(-10.5805093317121,-53.679865365992,6.11285760158389));
#5215=CARTESIAN_POINT('Ctrl Pts',(-10.7140712692388,-53.179865365992,5.95969740480451));
#5216=CARTESIAN_POINT('Ctrl Pts',(-10.7140712692388,-53.679865365992,5.95969740480451));
#5217=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.179865365992,5.88715590104679));
#5218=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.679865365992,5.88715590104679));
#5219=CARTESIAN_POINT('',(-10.5585508388429,-53.179865365992,6.42336751198972));
#5220=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.179865365992,5.88715590104679));
#5221=CARTESIAN_POINT('Ctrl Pts',(-10.7140712692388,-53.179865365992,5.95969740480451));
#5222=CARTESIAN_POINT('Ctrl Pts',(-10.5805093317121,-53.179865365992,6.11285760158389));
#5223=CARTESIAN_POINT('Ctrl Pts',(-10.5661232291359,-53.179865365992,6.31628811244045));
#5224=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.179865365992,6.42336751198972));
#5225=CARTESIAN_POINT('',(-10.5585508388429,-53.679865365992,6.42336751198972));
#5226=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.679865365992,6.42336751198972));
#5227=CARTESIAN_POINT('Ctrl Pts',(-10.5661232291359,-53.679865365992,6.31628811244045));
#5228=CARTESIAN_POINT('Ctrl Pts',(-10.5805093317121,-53.679865365992,6.11285760158389));
#5229=CARTESIAN_POINT('Ctrl Pts',(-10.7140712692388,-53.679865365992,5.95969740480451));
#5230=CARTESIAN_POINT('Ctrl Pts',(-10.7773304187876,-53.679865365992,5.88715590104679));
#5231=CARTESIAN_POINT('',(-10.5585508388429,-53.179865365992,6.42336751198972));
#5232=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.179865365992,6.85250728389722));
#5233=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.679865365992,6.85250728389722));
#5234=CARTESIAN_POINT('Ctrl Pts',(-10.6472081504693,-53.179865365992,6.78865664067444));
#5235=CARTESIAN_POINT('Ctrl Pts',(-10.6472081504693,-53.679865365992,6.78865664067444));
#5236=CARTESIAN_POINT('Ctrl Pts',(-10.5703647973503,-53.179865365992,6.65607171832209));
#5237=CARTESIAN_POINT('Ctrl Pts',(-10.5703647973503,-53.679865365992,6.65607171832209));
#5238=CARTESIAN_POINT('Ctrl Pts',(-10.5625843533596,-53.179865365992,6.5028172403561));
#5239=CARTESIAN_POINT('Ctrl Pts',(-10.5625843533596,-53.679865365992,6.5028172403561));
#5240=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.179865365992,6.42336751198972));
#5241=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.679865365992,6.42336751198972));
#5242=CARTESIAN_POINT('',(-10.6842146021607,-53.179865365992,6.85250728389722));
#5243=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.179865365992,6.42336751198972));
#5244=CARTESIAN_POINT('Ctrl Pts',(-10.5625843533596,-53.179865365992,6.5028172403561));
#5245=CARTESIAN_POINT('Ctrl Pts',(-10.5703647973503,-53.179865365992,6.65607171832209));
#5246=CARTESIAN_POINT('Ctrl Pts',(-10.6472081504693,-53.179865365992,6.78865664067444));
#5247=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.179865365992,6.85250728389722));
#5248=CARTESIAN_POINT('',(-10.6842146021607,-53.679865365992,6.85250728389722));
#5249=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.679865365992,6.85250728389722));
#5250=CARTESIAN_POINT('Ctrl Pts',(-10.6472081504693,-53.679865365992,6.78865664067444));
#5251=CARTESIAN_POINT('Ctrl Pts',(-10.5703647973503,-53.679865365992,6.65607171832209));
#5252=CARTESIAN_POINT('Ctrl Pts',(-10.5625843533596,-53.679865365992,6.5028172403561));
#5253=CARTESIAN_POINT('Ctrl Pts',(-10.5585508388429,-53.679865365992,6.42336751198972));
#5254=CARTESIAN_POINT('',(-10.6842146021607,-53.179865365992,6.85250728389722));
#5255=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.179865365992,7.09188413293033));
#5256=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.679865365992,7.09188413293033));
#5257=CARTESIAN_POINT('Ctrl Pts',(-10.9499830889762,-53.179865365992,7.06710989924449));
#5258=CARTESIAN_POINT('Ctrl Pts',(-10.9499830889762,-53.679865365992,7.06710989924449));
#5259=CARTESIAN_POINT('Ctrl Pts',(-10.8179014187074,-53.179865365992,7.01728747125291));
#5260=CARTESIAN_POINT('Ctrl Pts',(-10.8179014187074,-53.679865365992,7.01728747125291));
#5261=CARTESIAN_POINT('Ctrl Pts',(-10.7289399330613,-53.179865365992,6.90763499652971));
#5262=CARTESIAN_POINT('Ctrl Pts',(-10.7289399330613,-53.679865365992,6.90763499652971));
#5263=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.179865365992,6.85250728389722));
#5264=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.679865365992,6.85250728389722));
#5265=CARTESIAN_POINT('',(-11.0156607826728,-53.179865365992,7.09188413293033));
#5266=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.179865365992,6.85250728389722));
#5267=CARTESIAN_POINT('Ctrl Pts',(-10.7289399330613,-53.179865365992,6.90763499652971));
#5268=CARTESIAN_POINT('Ctrl Pts',(-10.8179014187074,-53.179865365992,7.01728747125291));
#5269=CARTESIAN_POINT('Ctrl Pts',(-10.9499830889762,-53.179865365992,7.06710989924449));
#5270=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.179865365992,7.09188413293033));
#5271=CARTESIAN_POINT('',(-11.0156607826728,-53.679865365992,7.09188413293033));
#5272=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.679865365992,7.09188413293033));
#5273=CARTESIAN_POINT('Ctrl Pts',(-10.9499830889762,-53.679865365992,7.06710989924449));
#5274=CARTESIAN_POINT('Ctrl Pts',(-10.8179014187074,-53.679865365992,7.01728747125291));
#5275=CARTESIAN_POINT('Ctrl Pts',(-10.7289399330613,-53.679865365992,6.90763499652971));
#5276=CARTESIAN_POINT('Ctrl Pts',(-10.6842146021607,-53.679865365992,6.85250728389722));
#5277=CARTESIAN_POINT('',(-11.0156607826728,-53.179865365992,7.09188413293033));
#5278=CARTESIAN_POINT('Ctrl Pts',(-11.5092989326057,-53.179865365992,7.13032227212665));
#5279=CARTESIAN_POINT('Ctrl Pts',(-11.5092989326057,-53.679865365992,7.13032227212665));
#5280=CARTESIAN_POINT('Ctrl Pts',(-11.4078304548555,-53.179865365992,7.13014271813443));
#5281=CARTESIAN_POINT('Ctrl Pts',(-11.4078304548555,-53.679865365992,7.13014271813443));
#5282=CARTESIAN_POINT('Ctrl Pts',(-11.2421538938531,-53.179865365992,7.12984954444538));
#5283=CARTESIAN_POINT('Ctrl Pts',(-11.2421538938531,-53.679865365992,7.12984954444538));
#5284=CARTESIAN_POINT('Ctrl Pts',(-11.078921591527,-53.179865365992,7.10248808741004));
#5285=CARTESIAN_POINT('Ctrl Pts',(-11.078921591527,-53.679865365992,7.10248808741004));
#5286=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.179865365992,7.09188413293033));
#5287=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.679865365992,7.09188413293033));
#5288=CARTESIAN_POINT('',(-11.5092989326057,-53.179865365992,7.13032227212665));
#5289=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.179865365992,7.09188413293033));
#5290=CARTESIAN_POINT('Ctrl Pts',(-11.078921591527,-53.179865365992,7.10248808741004));
#5291=CARTESIAN_POINT('Ctrl Pts',(-11.2421538938531,-53.179865365992,7.12984954444538));
#5292=CARTESIAN_POINT('Ctrl Pts',(-11.4078304548555,-53.179865365992,7.13014271813443));
#5293=CARTESIAN_POINT('Ctrl Pts',(-11.5092989326057,-53.179865365992,7.13032227212665));
#5294=CARTESIAN_POINT('',(-11.5092989326057,-53.679865365992,7.13032227212665));
#5295=CARTESIAN_POINT('Ctrl Pts',(-11.5092989326057,-53.679865365992,7.13032227212665));
#5296=CARTESIAN_POINT('Ctrl Pts',(-11.4078304548555,-53.679865365992,7.13014271813443));
#5297=CARTESIAN_POINT('Ctrl Pts',(-11.2421538938531,-53.679865365992,7.12984954444538));
#5298=CARTESIAN_POINT('Ctrl Pts',(-11.078921591527,-53.679865365992,7.10248808741004));
#5299=CARTESIAN_POINT('Ctrl Pts',(-11.0156607826728,-53.679865365992,7.09188413293033));
#5300=CARTESIAN_POINT('',(-11.5092989326057,-53.179865365992,7.13032227212665));
#5301=CARTESIAN_POINT('Origin',(-12.5324728597863,-53.179865365992,7.13647156651491));
#5302=CARTESIAN_POINT('',(-12.5324728597863,-53.179865365992,7.13647156651491));
#5303=CARTESIAN_POINT('',(-33.3466951209718,-53.179865365992,7.26156543056545));
#5304=CARTESIAN_POINT('',(-12.5324728597863,-53.679865365992,7.13647156651491));
#5305=CARTESIAN_POINT('',(-11.5092989326057,-53.679865365992,7.13032227212665));
#5306=CARTESIAN_POINT('',(-12.5324728597863,-53.179865365992,7.13647156651491));
#5307=CARTESIAN_POINT('Origin',(-12.5410654734007,-53.179865365992,5.70675660061337));
#5308=CARTESIAN_POINT('',(-12.5589661466678,-53.179865365992,2.72828443625616));
#5309=CARTESIAN_POINT('',(-12.5324728597863,-53.679865365992,7.13647156651491));
#5310=CARTESIAN_POINT('Origin',(-13.0589573948457,-53.179865365992,7.61167699911304));
#5311=CARTESIAN_POINT('',(-13.0829970356625,-53.679865365992,3.61174917820095));
#5312=CARTESIAN_POINT('',(-13.0829970356625,-53.179865365992,3.61174917820095));
#5313=CARTESIAN_POINT('',(-13.0589573948457,-53.679865365992,7.61167699911304));
#5314=CARTESIAN_POINT('',(-13.0829970356625,-53.679865365992,3.61174917820095));
#5315=CARTESIAN_POINT('',(-13.0589573948457,-53.179865365992,7.61167699911304));
#5316=CARTESIAN_POINT('Origin',(-11.5501180959437,-53.179865365992,7.60260884678131));
#5317=CARTESIAN_POINT('',(-11.5501180959437,-53.679865365992,7.60260884678131));
#5318=CARTESIAN_POINT('',(-13.0589573948457,-53.679865365992,7.61167699911304));
#5319=CARTESIAN_POINT('',(-11.5501180959437,-53.179865365992,7.60260884678131));
#5320=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.179865365992,7.56075333089919));
#5321=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.679865365992,7.56075333089919));
#5322=CARTESIAN_POINT('Ctrl Pts',(-11.0274473442138,-53.179865365992,7.57234542526143));
#5323=CARTESIAN_POINT('Ctrl Pts',(-11.0274473442138,-53.679865365992,7.57234542526143));
#5324=CARTESIAN_POINT('Ctrl Pts',(-11.2291799901218,-53.179865365992,7.59968125588383));
#5325=CARTESIAN_POINT('Ctrl Pts',(-11.2291799901218,-53.679865365992,7.59968125588383));
#5326=CARTESIAN_POINT('Ctrl Pts',(-11.4328289665597,-53.179865365992,7.60153893771994));
#5327=CARTESIAN_POINT('Ctrl Pts',(-11.4328289665597,-53.679865365992,7.60153893771994));
#5328=CARTESIAN_POINT('Ctrl Pts',(-11.5501180959437,-53.179865365992,7.60260884678131));
#5329=CARTESIAN_POINT('Ctrl Pts',(-11.5501180959437,-53.679865365992,7.60260884678131));
#5330=CARTESIAN_POINT('',(-10.9419001406327,-53.679865365992,7.56075333089919));
#5331=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.679865365992,7.56075333089919));
#5332=CARTESIAN_POINT('Ctrl Pts',(-11.0274473442138,-53.679865365992,7.57234542526143));
#5333=CARTESIAN_POINT('Ctrl Pts',(-11.2291799901218,-53.679865365992,7.59968125588383));
#5334=CARTESIAN_POINT('Ctrl Pts',(-11.4328289665597,-53.679865365992,7.60153893771994));
#5335=CARTESIAN_POINT('Ctrl Pts',(-11.5501180959437,-53.679865365992,7.60260884678131));
#5336=CARTESIAN_POINT('',(-10.9419001406327,-53.179865365992,7.56075333089919));
#5337=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.179865365992,7.36952143711172));
#5338=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.679865365992,7.36952143711172));
#5339=CARTESIAN_POINT('Ctrl Pts',(-10.5216707842693,-53.179865365992,7.41296342135046));
#5340=CARTESIAN_POINT('Ctrl Pts',(-10.5216707842693,-53.679865365992,7.41296342135046));
#5341=CARTESIAN_POINT('Ctrl Pts',(-10.6748340459972,-53.179865365992,7.50474933204626));
#5342=CARTESIAN_POINT('Ctrl Pts',(-10.6748340459972,-53.679865365992,7.50474933204626));
#5343=CARTESIAN_POINT('Ctrl Pts',(-10.8497639910406,-53.179865365992,7.54143229794249));
#5344=CARTESIAN_POINT('Ctrl Pts',(-10.8497639910406,-53.679865365992,7.54143229794249));
#5345=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.179865365992,7.56075333089919));
#5346=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.679865365992,7.56075333089919));
#5347=CARTESIAN_POINT('',(-10.4491790919006,-53.679865365992,7.36952143711172));
#5348=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.679865365992,7.36952143711172));
#5349=CARTESIAN_POINT('Ctrl Pts',(-10.5216707842693,-53.679865365992,7.41296342135046));
#5350=CARTESIAN_POINT('Ctrl Pts',(-10.6748340459972,-53.679865365992,7.50474933204626));
#5351=CARTESIAN_POINT('Ctrl Pts',(-10.8497639910406,-53.679865365992,7.54143229794249));
#5352=CARTESIAN_POINT('Ctrl Pts',(-10.9419001406327,-53.679865365992,7.56075333089919));
#5353=CARTESIAN_POINT('',(-10.4491790919006,-53.179865365992,7.36952143711172));
#5354=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.179865365992,6.98288954356071));
#5355=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.679865365992,6.98288954356071));
#5356=CARTESIAN_POINT('Ctrl Pts',(-10.1738756941945,-53.179865365992,7.06038053659198));
#5357=CARTESIAN_POINT('Ctrl Pts',(-10.1738756941945,-53.679865365992,7.06038053659198));
#5358=CARTESIAN_POINT('Ctrl Pts',(-10.2542927802496,-53.179865365992,7.21012811467747));
#5359=CARTESIAN_POINT('Ctrl Pts',(-10.2542927802496,-53.679865365992,7.21012811467747));
#5360=CARTESIAN_POINT('Ctrl Pts',(-10.3857486626935,-53.179865365992,7.31764305340637));
#5361=CARTESIAN_POINT('Ctrl Pts',(-10.3857486626935,-53.679865365992,7.31764305340637));
#5362=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.179865365992,7.36952143711172));
#5363=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.679865365992,7.36952143711172));
#5364=CARTESIAN_POINT('',(-10.1322616665437,-53.679865365992,6.98288954356071));
#5365=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.679865365992,6.98288954356071));
#5366=CARTESIAN_POINT('Ctrl Pts',(-10.1738756941945,-53.679865365992,7.06038053659198));
#5367=CARTESIAN_POINT('Ctrl Pts',(-10.2542927802496,-53.679865365992,7.21012811467747));
#5368=CARTESIAN_POINT('Ctrl Pts',(-10.3857486626935,-53.679865365992,7.31764305340637));
#5369=CARTESIAN_POINT('Ctrl Pts',(-10.4491790919006,-53.679865365992,7.36952143711172));
#5370=CARTESIAN_POINT('',(-10.1322616665437,-53.179865365992,6.98288954356071));
#5371=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.179865365992,6.43645890623372));
#5372=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.679865365992,6.43645890623372));
#5373=CARTESIAN_POINT('Ctrl Pts',(-10.0169087781024,-53.179865365992,6.53277984658392));
#5374=CARTESIAN_POINT('Ctrl Pts',(-10.0169087781024,-53.679865365992,6.53277984658392));
#5375=CARTESIAN_POINT('Ctrl Pts',(-10.0250550853855,-53.179865365992,6.72192496807218));
#5376=CARTESIAN_POINT('Ctrl Pts',(-10.0250550853855,-53.679865365992,6.72192496807218));
#5377=CARTESIAN_POINT('Ctrl Pts',(-10.0969693030987,-53.179865365992,6.89698011015736));
#5378=CARTESIAN_POINT('Ctrl Pts',(-10.0969693030987,-53.679865365992,6.89698011015736));
#5379=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.179865365992,6.98288954356071));
#5380=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.679865365992,6.98288954356071));
#5381=CARTESIAN_POINT('',(-10.0127603233512,-53.679865365992,6.43645890623372));
#5382=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.679865365992,6.43645890623372));
#5383=CARTESIAN_POINT('Ctrl Pts',(-10.0169087781024,-53.679865365992,6.53277984658392));
#5384=CARTESIAN_POINT('Ctrl Pts',(-10.0250550853855,-53.679865365992,6.72192496807218));
#5385=CARTESIAN_POINT('Ctrl Pts',(-10.0969693030987,-53.679865365992,6.89698011015736));
#5386=CARTESIAN_POINT('Ctrl Pts',(-10.1322616665437,-53.679865365992,6.98288954356071));
#5387=CARTESIAN_POINT('',(-10.0127603233512,-53.179865365992,6.43645890623372));
#5388=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.179865365992,5.57348740329565));
#5389=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.679865365992,5.57348740329565));
#5390=CARTESIAN_POINT('Ctrl Pts',(-10.2471945604295,-53.179865365992,5.69898678990867));
#5391=CARTESIAN_POINT('Ctrl Pts',(-10.2471945604295,-53.679865365992,5.69898678990867));
#5392=CARTESIAN_POINT('Ctrl Pts',(-10.0531051113646,-53.179865365992,5.95417689519697));
#5393=CARTESIAN_POINT('Ctrl Pts',(-10.0531051113646,-53.679865365992,5.95417689519697));
#5394=CARTESIAN_POINT('Ctrl Pts',(-10.0263550369002,-53.179865365992,6.27394756058809));
#5395=CARTESIAN_POINT('Ctrl Pts',(-10.0263550369002,-53.679865365992,6.27394756058809));
#5396=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.179865365992,6.43645890623372));
#5397=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.679865365992,6.43645890623372));
#5398=CARTESIAN_POINT('',(-10.342645388267,-53.679865365992,5.57348740329565));
#5399=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.679865365992,5.57348740329565));
#5400=CARTESIAN_POINT('Ctrl Pts',(-10.2471945604295,-53.679865365992,5.69898678990867));
#5401=CARTESIAN_POINT('Ctrl Pts',(-10.0531051113646,-53.679865365992,5.95417689519697));
#5402=CARTESIAN_POINT('Ctrl Pts',(-10.0263550369002,-53.679865365992,6.27394756058809));
#5403=CARTESIAN_POINT('Ctrl Pts',(-10.0127603233512,-53.679865365992,6.43645890623372));
#5404=CARTESIAN_POINT('',(-10.342645388267,-53.179865365992,5.57348740329565));
#5405=CARTESIAN_POINT('Ctrl Pts',(-11.5179997709062,-53.179865365992,5.22856640790731));
#5406=CARTESIAN_POINT('Ctrl Pts',(-11.5179997709062,-53.679865365992,5.22856640790731));
#5407=CARTESIAN_POINT('Ctrl Pts',(-11.2699132720111,-53.179865365992,5.22994429042418));
#5408=CARTESIAN_POINT('Ctrl Pts',(-11.2699132720111,-53.679865365992,5.22994429042418));
#5409=CARTESIAN_POINT('Ctrl Pts',(-10.8411233630871,-53.179865365992,5.23232580703854));
#5410=CARTESIAN_POINT('Ctrl Pts',(-10.8411233630871,-53.679865365992,5.23232580703854));
#5411=CARTESIAN_POINT('Ctrl Pts',(-10.4904348177378,-53.179865365992,5.47233934818574));
#5412=CARTESIAN_POINT('Ctrl Pts',(-10.4904348177378,-53.679865365992,5.47233934818574));
#5413=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.179865365992,5.57348740329565));
#5414=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.679865365992,5.57348740329565));
#5415=CARTESIAN_POINT('',(-11.5179997709062,-53.679865365992,5.22856640790731));
#5416=CARTESIAN_POINT('Ctrl Pts',(-11.5179997709062,-53.679865365992,5.22856640790731));
#5417=CARTESIAN_POINT('Ctrl Pts',(-11.2699132720111,-53.679865365992,5.22994429042418));
#5418=CARTESIAN_POINT('Ctrl Pts',(-10.8411233630871,-53.679865365992,5.23232580703854));
#5419=CARTESIAN_POINT('Ctrl Pts',(-10.4904348177378,-53.679865365992,5.47233934818574));
#5420=CARTESIAN_POINT('Ctrl Pts',(-10.342645388267,-53.679865365992,5.57348740329565));
#5421=CARTESIAN_POINT('',(-11.5179997709062,-53.179865365992,5.22856640790731));
#5422=CARTESIAN_POINT('Origin',(-12.5439023494451,-53.179865365992,5.23473210154114));
#5423=CARTESIAN_POINT('',(-12.5439023494451,-53.679865365992,5.23473210154114));
#5424=CARTESIAN_POINT('',(-11.5179997709062,-53.679865365992,5.22856640790731));
#5425=CARTESIAN_POINT('',(-12.5439023494451,-53.179865365992,5.23473210154114));
#5426=CARTESIAN_POINT('Origin',(-12.5536756263499,-53.179865365992,3.60856794665721));
#5427=CARTESIAN_POINT('',(-12.5536756263499,-53.679865365992,3.60856794665721));
#5428=CARTESIAN_POINT('',(-12.5439023494451,-53.679865365992,5.23473210154114));
#5429=CARTESIAN_POINT('',(-12.5536756263499,-53.179865365992,3.60856794665721));
#5430=CARTESIAN_POINT('Origin',(-13.0829970356625,-53.179865365992,3.61174917820095));
#5431=CARTESIAN_POINT('',(-12.5536756263499,-53.679865365992,3.60856794665721));
#5432=CARTESIAN_POINT('Origin',(-13.4911611848099,-53.679865365992,5.09371901156747));
#5433=CARTESIAN_POINT('Origin',(-54.2053113423059,-53.179865365992,0.));
#5434=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.179865365992,4.85216519940427));
#5435=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.679865365992,4.85216519940427));
#5436=CARTESIAN_POINT('Ctrl Pts',(-8.20597744906273,-53.179865365992,4.87433012269047));
#5437=CARTESIAN_POINT('Ctrl Pts',(-8.20597744906273,-53.679865365992,4.87433012269047));
#5438=CARTESIAN_POINT('Ctrl Pts',(-7.93311070709987,-53.179865365992,4.91276986321841));
#5439=CARTESIAN_POINT('Ctrl Pts',(-7.93311070709987,-53.679865365992,4.91276986321841));
#5440=CARTESIAN_POINT('Ctrl Pts',(-7.67100457310494,-53.179865365992,4.99715304012024));
#5441=CARTESIAN_POINT('Ctrl Pts',(-7.67100457310494,-53.679865365992,4.99715304012024));
#5442=CARTESIAN_POINT('Ctrl Pts',(-7.56003271507163,-53.179865365992,5.0328796283722));
#5443=CARTESIAN_POINT('Ctrl Pts',(-7.56003271507163,-53.679865365992,5.0328796283722));
#5444=CARTESIAN_POINT('',(-7.56003271507163,-53.179865365992,5.0328796283722));
#5445=CARTESIAN_POINT('',(-8.36331645083275,-53.179865365992,4.85216519940427));
#5446=CARTESIAN_POINT('Ctrl Pts',(-7.56003271507163,-53.179865365992,5.0328796283722));
#5447=CARTESIAN_POINT('Ctrl Pts',(-7.67100457310494,-53.179865365992,4.99715304012024));
#5448=CARTESIAN_POINT('Ctrl Pts',(-7.93311070709987,-53.179865365992,4.91276986321841));
#5449=CARTESIAN_POINT('Ctrl Pts',(-8.20597744906273,-53.179865365992,4.87433012269047));
#5450=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.179865365992,4.85216519940427));
#5451=CARTESIAN_POINT('',(-7.56003271507163,-53.679865365992,5.0328796283722));
#5452=CARTESIAN_POINT('',(-7.56003271507163,-53.179865365992,5.0328796283722));
#5453=CARTESIAN_POINT('',(-8.36331645083275,-53.679865365992,4.85216519940427));
#5454=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.679865365992,4.85216519940427));
#5455=CARTESIAN_POINT('Ctrl Pts',(-8.20597744906273,-53.679865365992,4.87433012269047));
#5456=CARTESIAN_POINT('Ctrl Pts',(-7.93311070709987,-53.679865365992,4.91276986321841));
#5457=CARTESIAN_POINT('Ctrl Pts',(-7.67100457310494,-53.679865365992,4.99715304012024));
#5458=CARTESIAN_POINT('Ctrl Pts',(-7.56003271507163,-53.679865365992,5.0328796283722));
#5459=CARTESIAN_POINT('',(-8.36331645083275,-53.179865365992,4.85216519940427));
#5460=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.179865365992,4.7565151073572));
#5461=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.679865365992,4.7565151073572));
#5462=CARTESIAN_POINT('Ctrl Pts',(-8.73588007506671,-53.179865365992,4.77483399195652));
#5463=CARTESIAN_POINT('Ctrl Pts',(-8.73588007506671,-53.679865365992,4.77483399195652));
#5464=CARTESIAN_POINT('Ctrl Pts',(-8.59607423587768,-53.179865365992,4.8202472265425));
#5465=CARTESIAN_POINT('Ctrl Pts',(-8.59607423587768,-53.679865365992,4.8202472265425));
#5466=CARTESIAN_POINT('Ctrl Pts',(-8.45029246758049,-53.179865365992,4.84023821641788));
#5467=CARTESIAN_POINT('Ctrl Pts',(-8.45029246758049,-53.679865365992,4.84023821641788));
#5468=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.179865365992,4.85216519940427));
#5469=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.679865365992,4.85216519940427));
#5470=CARTESIAN_POINT('',(-8.79227524395884,-53.179865365992,4.7565151073572));
#5471=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.179865365992,4.85216519940427));
#5472=CARTESIAN_POINT('Ctrl Pts',(-8.45029246758049,-53.179865365992,4.84023821641788));
#5473=CARTESIAN_POINT('Ctrl Pts',(-8.59607423587768,-53.179865365992,4.8202472265425));
#5474=CARTESIAN_POINT('Ctrl Pts',(-8.73588007506671,-53.179865365992,4.77483399195652));
#5475=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.179865365992,4.7565151073572));
#5476=CARTESIAN_POINT('',(-8.79227524395884,-53.679865365992,4.7565151073572));
#5477=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.679865365992,4.7565151073572));
#5478=CARTESIAN_POINT('Ctrl Pts',(-8.73588007506671,-53.679865365992,4.77483399195652));
#5479=CARTESIAN_POINT('Ctrl Pts',(-8.59607423587768,-53.679865365992,4.8202472265425));
#5480=CARTESIAN_POINT('Ctrl Pts',(-8.45029246758049,-53.679865365992,4.84023821641788));
#5481=CARTESIAN_POINT('Ctrl Pts',(-8.36331645083275,-53.679865365992,4.85216519940427));
#5482=CARTESIAN_POINT('',(-8.79227524395884,-53.179865365992,4.7565151073572));
#5483=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.179865365992,4.59669997150002));
#5484=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.679865365992,4.59669997150002));
#5485=CARTESIAN_POINT('Ctrl Pts',(-8.96121065363538,-53.179865365992,4.63003943141785));
#5486=CARTESIAN_POINT('Ctrl Pts',(-8.96121065363538,-53.679865365992,4.63003943141785));
#5487=CARTESIAN_POINT('Ctrl Pts',(-8.90858735308911,-53.179865365992,4.69816422833608));
#5488=CARTESIAN_POINT('Ctrl Pts',(-8.90858735308911,-53.679865365992,4.69816422833608));
#5489=CARTESIAN_POINT('Ctrl Pts',(-8.83159067149995,-53.179865365992,4.73679153967994));
#5490=CARTESIAN_POINT('Ctrl Pts',(-8.83159067149995,-53.679865365992,4.73679153967994));
#5491=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.179865365992,4.7565151073572));
#5492=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.679865365992,4.7565151073572));
#5493=CARTESIAN_POINT('',(-8.98696386683632,-53.179865365992,4.59669997150002));
#5494=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.179865365992,4.7565151073572));
#5495=CARTESIAN_POINT('Ctrl Pts',(-8.83159067149995,-53.179865365992,4.73679153967994));
#5496=CARTESIAN_POINT('Ctrl Pts',(-8.90858735308911,-53.179865365992,4.69816422833608));
#5497=CARTESIAN_POINT('Ctrl Pts',(-8.96121065363538,-53.179865365992,4.63003943141785));
#5498=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.179865365992,4.59669997150002));
#5499=CARTESIAN_POINT('',(-8.98696386683632,-53.679865365992,4.59669997150002));
#5500=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.679865365992,4.59669997150002));
#5501=CARTESIAN_POINT('Ctrl Pts',(-8.96121065363538,-53.679865365992,4.63003943141785));
#5502=CARTESIAN_POINT('Ctrl Pts',(-8.90858735308911,-53.679865365992,4.69816422833608));
#5503=CARTESIAN_POINT('Ctrl Pts',(-8.83159067149995,-53.679865365992,4.73679153967994));
#5504=CARTESIAN_POINT('Ctrl Pts',(-8.79227524395884,-53.679865365992,4.7565151073572));
#5505=CARTESIAN_POINT('',(-8.98696386683632,-53.179865365992,4.59669997150002));
#5506=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.179865365992,4.36519035346522));
#5507=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.679865365992,4.36519035346522));
#5508=CARTESIAN_POINT('Ctrl Pts',(-9.05357784872029,-53.179865365992,4.40674313754837));
#5509=CARTESIAN_POINT('Ctrl Pts',(-9.05357784872029,-53.679865365992,4.40674313754837));
#5510=CARTESIAN_POINT('Ctrl Pts',(-9.04764682376079,-53.179865365992,4.48913020749876));
#5511=CARTESIAN_POINT('Ctrl Pts',(-9.04764682376079,-53.679865365992,4.48913020749876));
#5512=CARTESIAN_POINT('Ctrl Pts',(-9.00707357260835,-53.179865365992,4.5610524616609));
#5513=CARTESIAN_POINT('Ctrl Pts',(-9.00707357260835,-53.679865365992,4.5610524616609));
#5514=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.179865365992,4.59669997150002));
#5515=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.679865365992,4.59669997150002));
#5516=CARTESIAN_POINT('',(-9.05656922345521,-53.179865365992,4.36519035346522));
#5517=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.179865365992,4.59669997150002));
#5518=CARTESIAN_POINT('Ctrl Pts',(-9.00707357260835,-53.179865365992,4.5610524616609));
#5519=CARTESIAN_POINT('Ctrl Pts',(-9.04764682376079,-53.179865365992,4.48913020749876));
#5520=CARTESIAN_POINT('Ctrl Pts',(-9.05357784872029,-53.179865365992,4.40674313754837));
#5521=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.179865365992,4.36519035346522));
#5522=CARTESIAN_POINT('',(-9.05656922345521,-53.679865365992,4.36519035346522));
#5523=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.679865365992,4.36519035346522));
#5524=CARTESIAN_POINT('Ctrl Pts',(-9.05357784872029,-53.679865365992,4.40674313754837));
#5525=CARTESIAN_POINT('Ctrl Pts',(-9.04764682376079,-53.679865365992,4.48913020749876));
#5526=CARTESIAN_POINT('Ctrl Pts',(-9.00707357260835,-53.679865365992,4.5610524616609));
#5527=CARTESIAN_POINT('Ctrl Pts',(-8.98696386683632,-53.679865365992,4.59669997150002));
#5528=CARTESIAN_POINT('',(-9.05656922345521,-53.179865365992,4.36519035346522));
#5529=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.179865365992,4.03688934503494));
#5530=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.679865365992,4.03688934503494));
#5531=CARTESIAN_POINT('Ctrl Pts',(-8.95389524594609,-53.179865365992,4.08349903758117));
#5532=CARTESIAN_POINT('Ctrl Pts',(-8.95389524594609,-53.679865365992,4.08349903758117));
#5533=CARTESIAN_POINT('Ctrl Pts',(-9.03920018483787,-53.179865365992,4.17662623010292));
#5534=CARTESIAN_POINT('Ctrl Pts',(-9.03920018483787,-53.679865365992,4.17662623010292));
#5535=CARTESIAN_POINT('Ctrl Pts',(-9.05078336622707,-53.179865365992,4.3023771419709));
#5536=CARTESIAN_POINT('Ctrl Pts',(-9.05078336622707,-53.679865365992,4.3023771419709));
#5537=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.179865365992,4.36519035346522));
#5538=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.679865365992,4.36519035346522));
#5539=CARTESIAN_POINT('',(-8.9112005520897,-53.179865365992,4.03688934503494));
#5540=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.179865365992,4.36519035346522));
#5541=CARTESIAN_POINT('Ctrl Pts',(-9.05078336622707,-53.179865365992,4.3023771419709));
#5542=CARTESIAN_POINT('Ctrl Pts',(-9.03920018483787,-53.179865365992,4.17662623010292));
#5543=CARTESIAN_POINT('Ctrl Pts',(-8.95389524594609,-53.179865365992,4.08349903758117));
#5544=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.179865365992,4.03688934503494));
#5545=CARTESIAN_POINT('',(-8.9112005520897,-53.679865365992,4.03688934503494));
#5546=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.679865365992,4.03688934503494));
#5547=CARTESIAN_POINT('Ctrl Pts',(-8.95389524594609,-53.679865365992,4.08349903758117));
#5548=CARTESIAN_POINT('Ctrl Pts',(-9.03920018483787,-53.679865365992,4.17662623010292));
#5549=CARTESIAN_POINT('Ctrl Pts',(-9.05078336622707,-53.679865365992,4.3023771419709));
#5550=CARTESIAN_POINT('Ctrl Pts',(-9.05656922345521,-53.679865365992,4.36519035346522));
#5551=CARTESIAN_POINT('',(-8.9112005520897,-53.179865365992,4.03688934503494));
#5552=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.179865365992,3.9032993201873));
#5553=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.679865365992,3.9032993201873));
#5554=CARTESIAN_POINT('Ctrl Pts',(-8.56209115931436,-53.179865365992,3.90676564909301));
#5555=CARTESIAN_POINT('Ctrl Pts',(-8.56209115931436,-53.679865365992,3.90676564909301));
#5556=CARTESIAN_POINT('Ctrl Pts',(-8.71982199375545,-53.179865365992,3.91307490503448));
#5557=CARTESIAN_POINT('Ctrl Pts',(-8.71982199375545,-53.679865365992,3.91307490503448));
#5558=CARTESIAN_POINT('Ctrl Pts',(-8.85175295593621,-53.179865365992,3.99842907607282));
#5559=CARTESIAN_POINT('Ctrl Pts',(-8.85175295593621,-53.679865365992,3.99842907607282));
#5560=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.179865365992,4.03688934503494));
#5561=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.679865365992,4.03688934503494));
#5562=CARTESIAN_POINT('',(-8.47543324658787,-53.179865365992,3.9032993201873));
#5563=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.179865365992,4.03688934503494));
#5564=CARTESIAN_POINT('Ctrl Pts',(-8.85175295593621,-53.179865365992,3.99842907607282));
#5565=CARTESIAN_POINT('Ctrl Pts',(-8.71982199375545,-53.179865365992,3.91307490503448));
#5566=CARTESIAN_POINT('Ctrl Pts',(-8.56209115931436,-53.179865365992,3.90676564909301));
#5567=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.179865365992,3.9032993201873));
#5568=CARTESIAN_POINT('',(-8.47543324658787,-53.679865365992,3.9032993201873));
#5569=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.679865365992,3.9032993201873));
#5570=CARTESIAN_POINT('Ctrl Pts',(-8.56209115931436,-53.679865365992,3.90676564909301));
#5571=CARTESIAN_POINT('Ctrl Pts',(-8.71982199375545,-53.679865365992,3.91307490503448));
#5572=CARTESIAN_POINT('Ctrl Pts',(-8.85175295593621,-53.679865365992,3.99842907607282));
#5573=CARTESIAN_POINT('Ctrl Pts',(-8.9112005520897,-53.679865365992,4.03688934503494));
#5574=CARTESIAN_POINT('',(-8.47543324658787,-53.179865365992,3.9032993201873));
#5575=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.179865365992,4.02304663415227));
#5576=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.679865365992,4.02304663415227));
#5577=CARTESIAN_POINT('Ctrl Pts',(-8.0477792503103,-53.179865365992,3.98771119521977));
#5578=CARTESIAN_POINT('Ctrl Pts',(-8.0477792503103,-53.679865365992,3.98771119521977));
#5579=CARTESIAN_POINT('Ctrl Pts',(-8.2083365355753,-53.179865365992,3.91483549813693));
#5580=CARTESIAN_POINT('Ctrl Pts',(-8.2083365355753,-53.679865365992,3.91483549813693));
#5581=CARTESIAN_POINT('Ctrl Pts',(-8.38462318692445,-53.179865365992,3.90722149880164));
#5582=CARTESIAN_POINT('Ctrl Pts',(-8.38462318692445,-53.679865365992,3.90722149880164));
#5583=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.179865365992,3.9032993201873));
#5584=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.679865365992,3.9032993201873));
#5585=CARTESIAN_POINT('',(-7.96992939945811,-53.179865365992,4.02304663415227));
#5586=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.179865365992,3.9032993201873));
#5587=CARTESIAN_POINT('Ctrl Pts',(-8.38462318692445,-53.179865365992,3.90722149880164));
#5588=CARTESIAN_POINT('Ctrl Pts',(-8.2083365355753,-53.179865365992,3.91483549813693));
#5589=CARTESIAN_POINT('Ctrl Pts',(-8.0477792503103,-53.179865365992,3.98771119521977));
#5590=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.179865365992,4.02304663415227));
#5591=CARTESIAN_POINT('',(-7.96992939945811,-53.679865365992,4.02304663415227));
#5592=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.679865365992,4.02304663415227));
#5593=CARTESIAN_POINT('Ctrl Pts',(-8.0477792503103,-53.679865365992,3.98771119521977));
#5594=CARTESIAN_POINT('Ctrl Pts',(-8.2083365355753,-53.679865365992,3.91483549813693));
#5595=CARTESIAN_POINT('Ctrl Pts',(-8.38462318692445,-53.679865365992,3.90722149880164));
#5596=CARTESIAN_POINT('Ctrl Pts',(-8.47543324658787,-53.679865365992,3.9032993201873));
#5597=CARTESIAN_POINT('',(-7.96992939945811,-53.179865365992,4.02304663415227));
#5598=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.179865365992,4.36215313644088));
#5599=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.679865365992,4.36215313644088));
#5600=CARTESIAN_POINT('Ctrl Pts',(-7.68363518062818,-53.179865365992,4.29396866649988));
#5601=CARTESIAN_POINT('Ctrl Pts',(-7.68363518062818,-53.679865365992,4.29396866649988));
#5602=CARTESIAN_POINT('Ctrl Pts',(-7.76585656197461,-53.179865365992,4.15534632353602));
#5603=CARTESIAN_POINT('Ctrl Pts',(-7.76585656197461,-53.679865365992,4.15534632353602));
#5604=CARTESIAN_POINT('Ctrl Pts',(-7.90117190513413,-53.179865365992,4.06762187137659));
#5605=CARTESIAN_POINT('Ctrl Pts',(-7.90117190513413,-53.679865365992,4.06762187137659));
#5606=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.179865365992,4.02304663415227));
#5607=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.679865365992,4.02304663415227));
#5608=CARTESIAN_POINT('',(-7.64319277296822,-53.179865365992,4.36215313644088));
#5609=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.179865365992,4.02304663415227));
#5610=CARTESIAN_POINT('Ctrl Pts',(-7.90117190513413,-53.179865365992,4.06762187137659));
#5611=CARTESIAN_POINT('Ctrl Pts',(-7.76585656197461,-53.179865365992,4.15534632353602));
#5612=CARTESIAN_POINT('Ctrl Pts',(-7.68363518062818,-53.179865365992,4.29396866649988));
#5613=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.179865365992,4.36215313644088));
#5614=CARTESIAN_POINT('',(-7.64319277296822,-53.679865365992,4.36215313644088));
#5615=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.679865365992,4.36215313644088));
#5616=CARTESIAN_POINT('Ctrl Pts',(-7.68363518062818,-53.679865365992,4.29396866649988));
#5617=CARTESIAN_POINT('Ctrl Pts',(-7.76585656197461,-53.679865365992,4.15534632353602));
#5618=CARTESIAN_POINT('Ctrl Pts',(-7.90117190513413,-53.679865365992,4.06762187137659));
#5619=CARTESIAN_POINT('Ctrl Pts',(-7.96992939945811,-53.679865365992,4.02304663415227));
#5620=CARTESIAN_POINT('',(-7.64319277296822,-53.179865365992,4.36215313644088));
#5621=CARTESIAN_POINT('Ctrl Pts',(-7.5611149900529,-53.179865365992,4.8528011554687));
#5622=CARTESIAN_POINT('Ctrl Pts',(-7.5611149900529,-53.679865365992,4.8528011554687));
#5623=CARTESIAN_POINT('Ctrl Pts',(-7.56263555899606,-53.179865365992,4.7567669712108));
#5624=CARTESIAN_POINT('Ctrl Pts',(-7.56263555899606,-53.679865365992,4.7567669712108));
#5625=CARTESIAN_POINT('Ctrl Pts',(-7.56529665696311,-53.179865365992,4.58870068678089));
#5626=CARTESIAN_POINT('Ctrl Pts',(-7.56529665696311,-53.679865365992,4.58870068678089));
#5627=CARTESIAN_POINT('Ctrl Pts',(-7.61982309957207,-53.179865365992,4.43011984045212));
#5628=CARTESIAN_POINT('Ctrl Pts',(-7.61982309957207,-53.679865365992,4.43011984045212));
#5629=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.179865365992,4.36215313644088));
#5630=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.679865365992,4.36215313644088));
#5631=CARTESIAN_POINT('',(-7.5611149900529,-53.179865365992,4.8528011554687));
#5632=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.179865365992,4.36215313644088));
#5633=CARTESIAN_POINT('Ctrl Pts',(-7.61982309957207,-53.179865365992,4.43011984045212));
#5634=CARTESIAN_POINT('Ctrl Pts',(-7.56529665696311,-53.179865365992,4.58870068678089));
#5635=CARTESIAN_POINT('Ctrl Pts',(-7.56263555899606,-53.179865365992,4.7567669712108));
#5636=CARTESIAN_POINT('Ctrl Pts',(-7.5611149900529,-53.179865365992,4.8528011554687));
#5637=CARTESIAN_POINT('',(-7.5611149900529,-53.679865365992,4.8528011554687));
#5638=CARTESIAN_POINT('Ctrl Pts',(-7.5611149900529,-53.679865365992,4.8528011554687));
#5639=CARTESIAN_POINT('Ctrl Pts',(-7.56263555899606,-53.679865365992,4.7567669712108));
#5640=CARTESIAN_POINT('Ctrl Pts',(-7.56529665696311,-53.679865365992,4.58870068678089));
#5641=CARTESIAN_POINT('Ctrl Pts',(-7.61982309957207,-53.679865365992,4.43011984045212));
#5642=CARTESIAN_POINT('Ctrl Pts',(-7.64319277296822,-53.679865365992,4.36215313644088));
#5643=CARTESIAN_POINT('',(-7.5611149900529,-53.179865365992,4.8528011554687));
#5644=CARTESIAN_POINT('Origin',(-7.56003271507163,-53.179865365992,5.0328796283722));
#5645=CARTESIAN_POINT('',(-7.57599840990316,-53.179865365992,2.37636627098306));
#5646=CARTESIAN_POINT('',(-7.5611149900529,-53.679865365992,4.8528011554687));
#5647=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.179865365992,3.61154083029525));
#5648=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.679865365992,3.61154083029525));
#5649=CARTESIAN_POINT('Ctrl Pts',(-7.96584472473011,-53.179865365992,3.65095056929209));
#5650=CARTESIAN_POINT('Ctrl Pts',(-7.96584472473011,-53.679865365992,3.65095056929209));
#5651=CARTESIAN_POINT('Ctrl Pts',(-7.7755743196687,-53.179865365992,3.73576152327227));
#5652=CARTESIAN_POINT('Ctrl Pts',(-7.7755743196687,-53.679865365992,3.73576152327227));
#5653=CARTESIAN_POINT('Ctrl Pts',(-7.61282300775472,-53.179865365992,3.86604684355902));
#5654=CARTESIAN_POINT('Ctrl Pts',(-7.61282300775472,-53.679865365992,3.86604684355902));
#5655=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.179865365992,3.9357915116463));
#5656=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.679865365992,3.9357915116463));
#5657=CARTESIAN_POINT('',(-7.52569856268888,-53.679865365992,3.9357915116463));
#5658=CARTESIAN_POINT('',(-7.52569856268888,-53.179865365992,3.9357915116463));
#5659=CARTESIAN_POINT('',(-8.0542590939877,-53.679865365992,3.61154083029525));
#5660=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.679865365992,3.61154083029525));
#5661=CARTESIAN_POINT('Ctrl Pts',(-7.96584472473011,-53.679865365992,3.65095056929209));
#5662=CARTESIAN_POINT('Ctrl Pts',(-7.7755743196687,-53.679865365992,3.73576152327227));
#5663=CARTESIAN_POINT('Ctrl Pts',(-7.61282300775472,-53.679865365992,3.86604684355902));
#5664=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.679865365992,3.9357915116463));
#5665=CARTESIAN_POINT('',(-8.0542590939877,-53.179865365992,3.61154083029525));
#5666=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.179865365992,3.51929141767058));
#5667=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.679865365992,3.51929141767058));
#5668=CARTESIAN_POINT('Ctrl Pts',(-8.50105864803172,-53.179865365992,3.5223949354539));
#5669=CARTESIAN_POINT('Ctrl Pts',(-8.50105864803172,-53.679865365992,3.5223949354539));
#5670=CARTESIAN_POINT('Ctrl Pts',(-8.31653833189115,-53.179865365992,3.52848641164841));
#5671=CARTESIAN_POINT('Ctrl Pts',(-8.31653833189115,-53.679865365992,3.52848641164841));
#5672=CARTESIAN_POINT('Ctrl Pts',(-8.14057277859556,-53.179865365992,3.584208385098));
#5673=CARTESIAN_POINT('Ctrl Pts',(-8.14057277859556,-53.679865365992,3.584208385098));
#5674=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.179865365992,3.61154083029525));
#5675=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.679865365992,3.61154083029525));
#5676=CARTESIAN_POINT('',(-8.59506904292454,-53.679865365992,3.51929141767058));
#5677=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.679865365992,3.51929141767058));
#5678=CARTESIAN_POINT('Ctrl Pts',(-8.50105864803172,-53.679865365992,3.5223949354539));
#5679=CARTESIAN_POINT('Ctrl Pts',(-8.31653833189115,-53.679865365992,3.52848641164841));
#5680=CARTESIAN_POINT('Ctrl Pts',(-8.14057277859556,-53.679865365992,3.584208385098));
#5681=CARTESIAN_POINT('Ctrl Pts',(-8.0542590939877,-53.679865365992,3.61154083029525));
#5682=CARTESIAN_POINT('',(-8.59506904292454,-53.179865365992,3.51929141767058));
#5683=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.179865365992,3.75562178632202));
#5684=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.679865365992,3.75562178632202));
#5685=CARTESIAN_POINT('Ctrl Pts',(-9.22731417201049,-53.179865365992,3.68751210681568));
#5686=CARTESIAN_POINT('Ctrl Pts',(-9.22731417201049,-53.679865365992,3.68751210681568));
#5687=CARTESIAN_POINT('Ctrl Pts',(-9.00722473754987,-53.179865365992,3.5380853446971));
#5688=CARTESIAN_POINT('Ctrl Pts',(-9.00722473754987,-53.679865365992,3.5380853446971));
#5689=CARTESIAN_POINT('Ctrl Pts',(-8.74031798210232,-53.179865365992,3.52591463797103));
#5690=CARTESIAN_POINT('Ctrl Pts',(-8.74031798210232,-53.679865365992,3.52591463797103));
#5691=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.179865365992,3.51929141767058));
#5692=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.679865365992,3.51929141767058));
#5693=CARTESIAN_POINT('',(-9.32763235217657,-53.679865365992,3.75562178632202));
#5694=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.679865365992,3.75562178632202));
#5695=CARTESIAN_POINT('Ctrl Pts',(-9.22731417201049,-53.679865365992,3.68751210681568));
#5696=CARTESIAN_POINT('Ctrl Pts',(-9.00722473754987,-53.679865365992,3.5380853446971));
#5697=CARTESIAN_POINT('Ctrl Pts',(-8.74031798210232,-53.679865365992,3.52591463797103));
#5698=CARTESIAN_POINT('Ctrl Pts',(-8.59506904292454,-53.679865365992,3.51929141767058));
#5699=CARTESIAN_POINT('',(-9.32763235217657,-53.179865365992,3.75562178632202));
#5700=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.179865365992,4.35469672896169));
#5701=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.679865365992,4.35469672896169));
#5702=CARTESIAN_POINT('Ctrl Pts',(-9.57105448710629,-53.179865365992,4.23950893215589));
#5703=CARTESIAN_POINT('Ctrl Pts',(-9.57105448710629,-53.679865365992,4.23950893215589));
#5704=CARTESIAN_POINT('Ctrl Pts',(-9.55235916430731,-53.179865365992,4.01191777135795));
#5705=CARTESIAN_POINT('Ctrl Pts',(-9.55235916430731,-53.679865365992,4.01191777135795));
#5706=CARTESIAN_POINT('Ctrl Pts',(-9.40192781231176,-53.179865365992,3.84035412403689));
#5707=CARTESIAN_POINT('Ctrl Pts',(-9.40192781231176,-53.679865365992,3.84035412403689));
#5708=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.179865365992,3.75562178632202));
#5709=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.679865365992,3.75562178632202));
#5710=CARTESIAN_POINT('',(-9.58051651118595,-53.679865365992,4.35469672896169));
#5711=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.679865365992,4.35469672896169));
#5712=CARTESIAN_POINT('Ctrl Pts',(-9.57105448710629,-53.679865365992,4.23950893215589));
#5713=CARTESIAN_POINT('Ctrl Pts',(-9.55235916430731,-53.679865365992,4.01191777135795));
#5714=CARTESIAN_POINT('Ctrl Pts',(-9.40192781231176,-53.679865365992,3.84035412403689));
#5715=CARTESIAN_POINT('Ctrl Pts',(-9.32763235217657,-53.679865365992,3.75562178632202));
#5716=CARTESIAN_POINT('',(-9.58051651118595,-53.179865365992,4.35469672896169));
#5717=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.179865365992,4.74156446963692));
#5718=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.679865365992,4.74156446963692));
#5719=CARTESIAN_POINT('Ctrl Pts',(-9.51090539074124,-53.179865365992,4.6813309230899));
#5720=CARTESIAN_POINT('Ctrl Pts',(-9.51090539074124,-53.679865365992,4.6813309230899));
#5721=CARTESIAN_POINT('Ctrl Pts',(-9.56833950463621,-53.179865365992,4.5587117948835));
#5722=CARTESIAN_POINT('Ctrl Pts',(-9.56833950463621,-53.679865365992,4.5587117948835));
#5723=CARTESIAN_POINT('Ctrl Pts',(-9.57641029336573,-53.179865365992,4.42349280752662));
#5724=CARTESIAN_POINT('Ctrl Pts',(-9.57641029336573,-53.679865365992,4.42349280752662));
#5725=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.179865365992,4.35469672896169));
#5726=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.679865365992,4.35469672896169));
#5727=CARTESIAN_POINT('',(-9.48269233439936,-53.679865365992,4.74156446963692));
#5728=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.679865365992,4.74156446963692));
#5729=CARTESIAN_POINT('Ctrl Pts',(-9.51090539074124,-53.679865365992,4.6813309230899));
#5730=CARTESIAN_POINT('Ctrl Pts',(-9.56833950463621,-53.679865365992,4.5587117948835));
#5731=CARTESIAN_POINT('Ctrl Pts',(-9.57641029336573,-53.679865365992,4.42349280752662));
#5732=CARTESIAN_POINT('Ctrl Pts',(-9.58051651118595,-53.679865365992,4.35469672896169));
#5733=CARTESIAN_POINT('',(-9.48269233439936,-53.179865365992,4.74156446963692));
#5734=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.179865365992,5.02379951219435));
#5735=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.679865365992,5.02379951219435));
#5736=CARTESIAN_POINT('Ctrl Pts',(-9.27722495093455,-53.179865365992,4.9851292565768));
#5737=CARTESIAN_POINT('Ctrl Pts',(-9.27722495093455,-53.679865365992,4.9851292565768));
#5738=CARTESIAN_POINT('Ctrl Pts',(-9.37913557876154,-53.179865365992,4.90628883435305));
#5739=CARTESIAN_POINT('Ctrl Pts',(-9.37913557876154,-53.679865365992,4.90628883435305));
#5740=CARTESIAN_POINT('Ctrl Pts',(-9.44773836796607,-53.179865365992,4.79716460774061));
#5741=CARTESIAN_POINT('Ctrl Pts',(-9.44773836796607,-53.679865365992,4.79716460774061));
#5742=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.179865365992,4.74156446963692));
#5743=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.679865365992,4.74156446963692));
#5744=CARTESIAN_POINT('',(-9.22723904374304,-53.679865365992,5.02379951219435));
#5745=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.679865365992,5.02379951219435));
#5746=CARTESIAN_POINT('Ctrl Pts',(-9.27722495093455,-53.679865365992,4.9851292565768));
#5747=CARTESIAN_POINT('Ctrl Pts',(-9.37913557876154,-53.679865365992,4.90628883435305));
#5748=CARTESIAN_POINT('Ctrl Pts',(-9.44773836796607,-53.679865365992,4.79716460774061));
#5749=CARTESIAN_POINT('Ctrl Pts',(-9.48269233439936,-53.679865365992,4.74156446963692));
#5750=CARTESIAN_POINT('',(-9.22723904374304,-53.179865365992,5.02379951219435));
#5751=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.179865365992,5.18264746222486));
#5752=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.679865365992,5.18264746222486));
#5753=CARTESIAN_POINT('Ctrl Pts',(-8.93511769640171,-53.179865365992,5.16299009928601));
#5754=CARTESIAN_POINT('Ctrl Pts',(-8.93511769640171,-53.679865365992,5.16299009928601));
#5755=CARTESIAN_POINT('Ctrl Pts',(-9.06008061134495,-53.179865365992,5.1243338563112));
#5756=CARTESIAN_POINT('Ctrl Pts',(-9.06008061134495,-53.679865365992,5.1243338563112));
#5757=CARTESIAN_POINT('Ctrl Pts',(-9.17215594204646,-53.179865365992,5.05692822380969));
#5758=CARTESIAN_POINT('Ctrl Pts',(-9.17215594204646,-53.679865365992,5.05692822380969));
#5759=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.179865365992,5.02379951219435));
#5760=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.679865365992,5.02379951219435));
#5761=CARTESIAN_POINT('',(-8.87157190964391,-53.679865365992,5.18264746222486));
#5762=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.679865365992,5.18264746222486));
#5763=CARTESIAN_POINT('Ctrl Pts',(-8.93511769640171,-53.679865365992,5.16299009928601));
#5764=CARTESIAN_POINT('Ctrl Pts',(-9.06008061134495,-53.679865365992,5.1243338563112));
#5765=CARTESIAN_POINT('Ctrl Pts',(-9.17215594204646,-53.679865365992,5.05692822380969));
#5766=CARTESIAN_POINT('Ctrl Pts',(-9.22723904374304,-53.679865365992,5.02379951219435));
#5767=CARTESIAN_POINT('',(-8.87157190964391,-53.179865365992,5.18264746222486));
#5768=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.179865365992,5.25369198439906));
#5769=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.679865365992,5.25369198439906));
#5770=CARTESIAN_POINT('Ctrl Pts',(-8.5199449747195,-53.179865365992,5.24370711931336));
#5771=CARTESIAN_POINT('Ctrl Pts',(-8.5199449747195,-53.679865365992,5.24370711931336));
#5772=CARTESIAN_POINT('Ctrl Pts',(-8.66670465412422,-53.179865365992,5.22654217044505));
#5773=CARTESIAN_POINT('Ctrl Pts',(-8.66670465412422,-53.679865365992,5.22654217044505));
#5774=CARTESIAN_POINT('Ctrl Pts',(-8.8111503821793,-53.179865365992,5.19559333447541));
#5775=CARTESIAN_POINT('Ctrl Pts',(-8.8111503821793,-53.679865365992,5.19559333447541));
#5776=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.179865365992,5.18264746222486));
#5777=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.679865365992,5.18264746222486));
#5778=CARTESIAN_POINT('',(-8.43457474669731,-53.679865365992,5.25369198439906));
#5779=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.679865365992,5.25369198439906));
#5780=CARTESIAN_POINT('Ctrl Pts',(-8.5199449747195,-53.679865365992,5.24370711931336));
#5781=CARTESIAN_POINT('Ctrl Pts',(-8.66670465412422,-53.679865365992,5.22654217044505));
#5782=CARTESIAN_POINT('Ctrl Pts',(-8.8111503821793,-53.679865365992,5.19559333447541));
#5783=CARTESIAN_POINT('Ctrl Pts',(-8.87157190964391,-53.679865365992,5.18264746222486));
#5784=CARTESIAN_POINT('',(-8.43457474669731,-53.179865365992,5.25369198439906));
#5785=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.179865365992,5.41759324433322));
#5786=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.679865365992,5.41759324433322));
#5787=CARTESIAN_POINT('Ctrl Pts',(-7.67723826543409,-53.179865365992,5.38533882489627));
#5788=CARTESIAN_POINT('Ctrl Pts',(-7.67723826543409,-53.679865365992,5.38533882489627));
#5789=CARTESIAN_POINT('Ctrl Pts',(-7.96501695024711,-53.179865365992,5.30767555440606));
#5790=CARTESIAN_POINT('Ctrl Pts',(-7.96501695024711,-53.679865365992,5.30767555440606));
#5791=CARTESIAN_POINT('Ctrl Pts',(-8.2613260875168,-53.179865365992,5.27360983304771));
#5792=CARTESIAN_POINT('Ctrl Pts',(-8.2613260875168,-53.679865365992,5.27360983304771));
#5793=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.179865365992,5.25369198439906));
#5794=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.679865365992,5.25369198439906));
#5795=CARTESIAN_POINT('',(-7.5577205790634,-53.679865365992,5.41759324433322));
#5796=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.679865365992,5.41759324433322));
#5797=CARTESIAN_POINT('Ctrl Pts',(-7.67723826543409,-53.679865365992,5.38533882489627));
#5798=CARTESIAN_POINT('Ctrl Pts',(-7.96501695024711,-53.679865365992,5.30767555440606));
#5799=CARTESIAN_POINT('Ctrl Pts',(-8.2613260875168,-53.679865365992,5.27360983304771));
#5800=CARTESIAN_POINT('Ctrl Pts',(-8.43457474669731,-53.679865365992,5.25369198439906));
#5801=CARTESIAN_POINT('',(-7.5577205790634,-53.179865365992,5.41759324433322));
#5802=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.179865365992,5.54581452556047));
#5803=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.679865365992,5.54581452556047));
#5804=CARTESIAN_POINT('Ctrl Pts',(-7.5545144957132,-53.179865365992,5.53058132214222));
#5805=CARTESIAN_POINT('Ctrl Pts',(-7.5545144957132,-53.679865365992,5.53058132214222));
#5806=CARTESIAN_POINT('Ctrl Pts',(-7.55533418731718,-53.179865365992,5.48783193643314));
#5807=CARTESIAN_POINT('Ctrl Pts',(-7.55533418731718,-53.679865365992,5.48783193643314));
#5808=CARTESIAN_POINT('Ctrl Pts',(-7.55678606174325,-53.179865365992,5.44509890195052));
#5809=CARTESIAN_POINT('Ctrl Pts',(-7.55678606174325,-53.679865365992,5.44509890195052));
#5810=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.179865365992,5.41759324433322));
#5811=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.679865365992,5.41759324433322));
#5812=CARTESIAN_POINT('',(-7.55422240896771,-53.679865365992,5.54581452556047));
#5813=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.679865365992,5.54581452556047));
#5814=CARTESIAN_POINT('Ctrl Pts',(-7.5545144957132,-53.679865365992,5.53058132214222));
#5815=CARTESIAN_POINT('Ctrl Pts',(-7.55533418731718,-53.679865365992,5.48783193643314));
#5816=CARTESIAN_POINT('Ctrl Pts',(-7.55678606174325,-53.679865365992,5.44509890195052));
#5817=CARTESIAN_POINT('Ctrl Pts',(-7.5577205790634,-53.679865365992,5.41759324433322));
#5818=CARTESIAN_POINT('',(-7.55422240896771,-53.179865365992,5.54581452556047));
#5819=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.179865365992,5.9695625845691));
#5820=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.679865365992,5.9695625845691));
#5821=CARTESIAN_POINT('Ctrl Pts',(-7.65075354685986,-53.179865365992,5.91564185445248));
#5822=CARTESIAN_POINT('Ctrl Pts',(-7.65075354685986,-53.679865365992,5.91564185445248));
#5823=CARTESIAN_POINT('Ctrl Pts',(-7.55816312091004,-53.179865365992,5.79107378541704));
#5824=CARTESIAN_POINT('Ctrl Pts',(-7.55816312091004,-53.679865365992,5.79107378541704));
#5825=CARTESIAN_POINT('Ctrl Pts',(-7.5556485300669,-53.179865365992,5.63457244808125));
#5826=CARTESIAN_POINT('Ctrl Pts',(-7.5556485300669,-53.679865365992,5.63457244808125));
#5827=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.179865365992,5.54581452556047));
#5828=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.679865365992,5.54581452556047));
#5829=CARTESIAN_POINT('',(-7.69083238413879,-53.679865365992,5.9695625845691));
#5830=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.679865365992,5.9695625845691));
#5831=CARTESIAN_POINT('Ctrl Pts',(-7.65075354685986,-53.679865365992,5.91564185445248));
#5832=CARTESIAN_POINT('Ctrl Pts',(-7.55816312091004,-53.679865365992,5.79107378541704));
#5833=CARTESIAN_POINT('Ctrl Pts',(-7.5556485300669,-53.679865365992,5.63457244808125));
#5834=CARTESIAN_POINT('Ctrl Pts',(-7.55422240896771,-53.679865365992,5.54581452556047));
#5835=CARTESIAN_POINT('',(-7.69083238413879,-53.179865365992,5.9695625845691));
#5836=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.179865365992,6.13936060166827));
#5837=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.679865365992,6.13936060166827));
#5838=CARTESIAN_POINT('Ctrl Pts',(-8.13741645827153,-53.179865365992,6.13521448225754));
#5839=CARTESIAN_POINT('Ctrl Pts',(-8.13741645827153,-53.679865365992,6.13521448225754));
#5840=CARTESIAN_POINT('Ctrl Pts',(-7.9353239259101,-53.179865365992,6.12771648632568));
#5841=CARTESIAN_POINT('Ctrl Pts',(-7.9353239259101,-53.679865365992,6.12771648632568));
#5842=CARTESIAN_POINT('Ctrl Pts',(-7.76636372904896,-53.179865365992,6.01842144003674));
#5843=CARTESIAN_POINT('Ctrl Pts',(-7.76636372904896,-53.679865365992,6.01842144003674));
#5844=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.179865365992,5.9695625845691));
#5845=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.679865365992,5.9695625845691));
#5846=CARTESIAN_POINT('',(-8.24916628833305,-53.679865365992,6.13936060166827));
#5847=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.679865365992,6.13936060166827));
#5848=CARTESIAN_POINT('Ctrl Pts',(-8.13741645827153,-53.679865365992,6.13521448225754));
#5849=CARTESIAN_POINT('Ctrl Pts',(-7.9353239259101,-53.679865365992,6.12771648632568));
#5850=CARTESIAN_POINT('Ctrl Pts',(-7.76636372904896,-53.679865365992,6.01842144003674));
#5851=CARTESIAN_POINT('Ctrl Pts',(-7.69083238413879,-53.679865365992,5.9695625845691));
#5852=CARTESIAN_POINT('',(-8.24916628833305,-53.179865365992,6.13936060166827));
#5853=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.179865365992,6.01966248543999));
#5854=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.679865365992,6.01966248543999));
#5855=CARTESIAN_POINT('Ctrl Pts',(-8.69345236449357,-53.179865365992,6.05489189650149));
#5856=CARTESIAN_POINT('Ctrl Pts',(-8.69345236449357,-53.679865365992,6.05489189650149));
#5857=CARTESIAN_POINT('Ctrl Pts',(-8.53285344012592,-53.179865365992,6.13641209615887));
#5858=CARTESIAN_POINT('Ctrl Pts',(-8.53285344012592,-53.679865365992,6.13641209615887));
#5859=CARTESIAN_POINT('Ctrl Pts',(-8.35191255569615,-53.179865365992,6.13829270721667));
#5860=CARTESIAN_POINT('Ctrl Pts',(-8.35191255569615,-53.679865365992,6.13829270721667));
#5861=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.179865365992,6.13936060166827));
#5862=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.679865365992,6.13936060166827));
#5863=CARTESIAN_POINT('',(-8.76285608953794,-53.679865365992,6.01966248543999));
#5864=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.679865365992,6.01966248543999));
#5865=CARTESIAN_POINT('Ctrl Pts',(-8.69345236449357,-53.679865365992,6.05489189650149));
#5866=CARTESIAN_POINT('Ctrl Pts',(-8.53285344012592,-53.679865365992,6.13641209615887));
#5867=CARTESIAN_POINT('Ctrl Pts',(-8.35191255569615,-53.679865365992,6.13829270721667));
#5868=CARTESIAN_POINT('Ctrl Pts',(-8.24916628833305,-53.679865365992,6.13936060166827));
#5869=CARTESIAN_POINT('',(-8.76285608953794,-53.179865365992,6.01966248543999));
#5870=CARTESIAN_POINT('Ctrl Pts',(-9.00826387566953,-53.179865365992,5.59275315849512));
#5871=CARTESIAN_POINT('Ctrl Pts',(-9.00826387566953,-53.679865365992,5.59275315849512));
#5872=CARTESIAN_POINT('Ctrl Pts',(-8.98188449923743,-53.179865365992,5.68419660643888));
#5873=CARTESIAN_POINT('Ctrl Pts',(-8.98188449923743,-53.679865365992,5.68419660643888));
#5874=CARTESIAN_POINT('Ctrl Pts',(-8.93479767341532,-53.179865365992,5.8474219092425));
#5875=CARTESIAN_POINT('Ctrl Pts',(-8.93479767341532,-53.679865365992,5.8474219092425));
#5876=CARTESIAN_POINT('Ctrl Pts',(-8.81537485509798,-53.179865365992,5.96705239405166));
#5877=CARTESIAN_POINT('Ctrl Pts',(-8.81537485509798,-53.679865365992,5.96705239405166));
#5878=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.179865365992,6.01966248543999));
#5879=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.679865365992,6.01966248543999));
#5880=CARTESIAN_POINT('',(-9.00826387566953,-53.679865365992,5.59275315849512));
#5881=CARTESIAN_POINT('Ctrl Pts',(-9.00826387566953,-53.679865365992,5.59275315849512));
#5882=CARTESIAN_POINT('Ctrl Pts',(-8.98188449923743,-53.679865365992,5.68419660643888));
#5883=CARTESIAN_POINT('Ctrl Pts',(-8.93479767341532,-53.679865365992,5.8474219092425));
#5884=CARTESIAN_POINT('Ctrl Pts',(-8.81537485509798,-53.679865365992,5.96705239405166));
#5885=CARTESIAN_POINT('Ctrl Pts',(-8.76285608953794,-53.679865365992,6.01966248543999));
#5886=CARTESIAN_POINT('',(-9.00826387566953,-53.179865365992,5.59275315849512));
#5887=CARTESIAN_POINT('Origin',(-9.48807958171841,-53.179865365992,5.66112238744525));
#5888=CARTESIAN_POINT('',(-9.48807958171841,-53.679865365992,5.66112238744525));
#5889=CARTESIAN_POINT('',(-9.00826387566953,-53.679865365992,5.59275315849512));
#5890=CARTESIAN_POINT('',(-9.48807958171841,-53.179865365992,5.66112238744525));
#5891=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.179865365992,6.15640720819095));
#5892=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.679865365992,6.15640720819095));
#5893=CARTESIAN_POINT('Ctrl Pts',(-9.31709914515962,-53.179865365992,6.08577447495683));
#5894=CARTESIAN_POINT('Ctrl Pts',(-9.31709914515962,-53.679865365992,6.08577447495683));
#5895=CARTESIAN_POINT('Ctrl Pts',(-9.41932761412119,-53.179865365992,5.93393021767033));
#5896=CARTESIAN_POINT('Ctrl Pts',(-9.41932761412119,-53.679865365992,5.93393021767033));
#5897=CARTESIAN_POINT('Ctrl Pts',(-9.46412200367381,-53.179865365992,5.75618606507093));
#5898=CARTESIAN_POINT('Ctrl Pts',(-9.46412200367381,-53.679865365992,5.75618606507093));
#5899=CARTESIAN_POINT('Ctrl Pts',(-9.48807958171841,-53.179865365992,5.66112238744525));
#5900=CARTESIAN_POINT('Ctrl Pts',(-9.48807958171841,-53.679865365992,5.66112238744525));
#5901=CARTESIAN_POINT('',(-9.26954597254625,-53.679865365992,6.15640720819095));
#5902=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.679865365992,6.15640720819095));
#5903=CARTESIAN_POINT('Ctrl Pts',(-9.31709914515962,-53.679865365992,6.08577447495683));
#5904=CARTESIAN_POINT('Ctrl Pts',(-9.41932761412119,-53.679865365992,5.93393021767033));
#5905=CARTESIAN_POINT('Ctrl Pts',(-9.46412200367381,-53.679865365992,5.75618606507093));
#5906=CARTESIAN_POINT('Ctrl Pts',(-9.48807958171841,-53.679865365992,5.66112238744525));
#5907=CARTESIAN_POINT('',(-9.26954597254625,-53.179865365992,6.15640720819095));
#5908=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.179865365992,6.44574563540659));
#5909=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.679865365992,6.44574563540659));
#5910=CARTESIAN_POINT('Ctrl Pts',(-8.92156409420348,-53.179865365992,6.41086942493555));
#5911=CARTESIAN_POINT('Ctrl Pts',(-8.92156409420348,-53.679865365992,6.41086942493555));
#5912=CARTESIAN_POINT('Ctrl Pts',(-9.08733990039489,-53.179865365992,6.34486791932323));
#5913=CARTESIAN_POINT('Ctrl Pts',(-9.08733990039489,-53.679865365992,6.34486791932323));
#5914=CARTESIAN_POINT('Ctrl Pts',(-9.21115613855355,-53.179865365992,6.21680140640416));
#5915=CARTESIAN_POINT('Ctrl Pts',(-9.21115613855355,-53.679865365992,6.21680140640416));
#5916=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.179865365992,6.15640720819095));
#5917=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.679865365992,6.15640720819095));
#5918=CARTESIAN_POINT('',(-8.83396560907652,-53.679865365992,6.44574563540659));
#5919=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.679865365992,6.44574563540659));
#5920=CARTESIAN_POINT('Ctrl Pts',(-8.92156409420348,-53.679865365992,6.41086942493555));
#5921=CARTESIAN_POINT('Ctrl Pts',(-9.08733990039489,-53.679865365992,6.34486791932323));
#5922=CARTESIAN_POINT('Ctrl Pts',(-9.21115613855355,-53.679865365992,6.21680140640416));
#5923=CARTESIAN_POINT('Ctrl Pts',(-9.26954597254625,-53.679865365992,6.15640720819095));
#5924=CARTESIAN_POINT('',(-8.83396560907652,-53.179865365992,6.44574563540659));
#5925=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.179865365992,6.54547528866209));
#5926=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.679865365992,6.54547528866209));
#5927=CARTESIAN_POINT('Ctrl Pts',(-8.29376721657436,-53.179865365992,6.54288251402918));
#5928=CARTESIAN_POINT('Ctrl Pts',(-8.29376721657436,-53.679865365992,6.54288251402918));
#5929=CARTESIAN_POINT('Ctrl Pts',(-8.51759131014624,-53.179865365992,6.53796385223384));
#5930=CARTESIAN_POINT('Ctrl Pts',(-8.51759131014624,-53.679865365992,6.53796385223384));
#5931=CARTESIAN_POINT('Ctrl Pts',(-8.73239256088601,-53.179865365992,6.47535260858206));
#5932=CARTESIAN_POINT('Ctrl Pts',(-8.73239256088601,-53.679865365992,6.47535260858206));
#5933=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.179865365992,6.44574563540659));
#5934=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.679865365992,6.44574563540659));
#5935=CARTESIAN_POINT('',(-8.17578280207455,-53.679865365992,6.54547528866209));
#5936=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.679865365992,6.54547528866209));
#5937=CARTESIAN_POINT('Ctrl Pts',(-8.29376721657436,-53.679865365992,6.54288251402918));
#5938=CARTESIAN_POINT('Ctrl Pts',(-8.51759131014624,-53.679865365992,6.53796385223384));
#5939=CARTESIAN_POINT('Ctrl Pts',(-8.73239256088601,-53.679865365992,6.47535260858206));
#5940=CARTESIAN_POINT('Ctrl Pts',(-8.83396560907652,-53.679865365992,6.44574563540659));
#5941=CARTESIAN_POINT('',(-8.17578280207455,-53.179865365992,6.54547528866209));
#5942=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.179865365992,6.45454042303442));
#5943=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.679865365992,6.45454042303442));
#5944=CARTESIAN_POINT('Ctrl Pts',(-7.66151733762561,-53.179865365992,6.48046845326743));
#5945=CARTESIAN_POINT('Ctrl Pts',(-7.66151733762561,-53.679865365992,6.48046845326743));
#5946=CARTESIAN_POINT('Ctrl Pts',(-7.85794594197565,-53.179865365992,6.53821227924818));
#5947=CARTESIAN_POINT('Ctrl Pts',(-7.85794594197565,-53.679865365992,6.53821227924818));
#5948=CARTESIAN_POINT('Ctrl Pts',(-8.06287217977795,-53.179865365992,6.54289512564256));
#5949=CARTESIAN_POINT('Ctrl Pts',(-8.06287217977795,-53.679865365992,6.54289512564256));
#5950=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.179865365992,6.54547528866209));
#5951=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.679865365992,6.54547528866209));
#5952=CARTESIAN_POINT('',(-7.57331731440734,-53.679865365992,6.45454042303442));
#5953=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.679865365992,6.45454042303442));
#5954=CARTESIAN_POINT('Ctrl Pts',(-7.66151733762561,-53.679865365992,6.48046845326743));
#5955=CARTESIAN_POINT('Ctrl Pts',(-7.85794594197565,-53.679865365992,6.53821227924818));
#5956=CARTESIAN_POINT('Ctrl Pts',(-8.06287217977795,-53.679865365992,6.54289512564256));
#5957=CARTESIAN_POINT('Ctrl Pts',(-8.17578280207455,-53.679865365992,6.54547528866209));
#5958=CARTESIAN_POINT('',(-7.57331731440734,-53.179865365992,6.45454042303442));
#5959=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.179865365992,6.23148509950789));
#5960=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.679865365992,6.23148509950789));
#5961=CARTESIAN_POINT('Ctrl Pts',(-7.27791568390916,-53.179865365992,6.2754881316564));
#5962=CARTESIAN_POINT('Ctrl Pts',(-7.27791568390916,-53.679865365992,6.2754881316564));
#5963=CARTESIAN_POINT('Ctrl Pts',(-7.37578335225521,-53.179865365992,6.37263899048271));
#5964=CARTESIAN_POINT('Ctrl Pts',(-7.37578335225521,-53.679865365992,6.37263899048271));
#5965=CARTESIAN_POINT('Ctrl Pts',(-7.5034663988905,-53.179865365992,6.42557887139263));
#5966=CARTESIAN_POINT('Ctrl Pts',(-7.5034663988905,-53.679865365992,6.42557887139263));
#5967=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.179865365992,6.45454042303442));
#5968=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.679865365992,6.45454042303442));
#5969=CARTESIAN_POINT('',(-7.23358798358248,-53.679865365992,6.23148509950789));
#5970=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.679865365992,6.23148509950789));
#5971=CARTESIAN_POINT('Ctrl Pts',(-7.27791568390916,-53.679865365992,6.2754881316564));
#5972=CARTESIAN_POINT('Ctrl Pts',(-7.37578335225521,-53.679865365992,6.37263899048271));
#5973=CARTESIAN_POINT('Ctrl Pts',(-7.5034663988905,-53.679865365992,6.42557887139263));
#5974=CARTESIAN_POINT('Ctrl Pts',(-7.57331731440734,-53.679865365992,6.45454042303442));
#5975=CARTESIAN_POINT('',(-7.23358798358248,-53.179865365992,6.23148509950789));
#5976=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.179865365992,5.89769428788757));
#5977=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.679865365992,5.89769428788757));
#5978=CARTESIAN_POINT('Ctrl Pts',(-7.0987049892125,-53.179865365992,5.96091715184107));
#5979=CARTESIAN_POINT('Ctrl Pts',(-7.0987049892125,-53.679865365992,5.96091715184107));
#5980=CARTESIAN_POINT('Ctrl Pts',(-7.12901072727269,-53.179865365992,6.08134414836999));
#5981=CARTESIAN_POINT('Ctrl Pts',(-7.12901072727269,-53.679865365992,6.08134414836999));
#5982=CARTESIAN_POINT('Ctrl Pts',(-7.19991003949418,-53.179865365992,6.18313387347449));
#5983=CARTESIAN_POINT('Ctrl Pts',(-7.19991003949418,-53.679865365992,6.18313387347449));
#5984=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.179865365992,6.23148509950789));
#5985=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.679865365992,6.23148509950789));
#5986=CARTESIAN_POINT('',(-7.08279480620025,-53.679865365992,5.89769428788757));
#5987=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.679865365992,5.89769428788757));
#5988=CARTESIAN_POINT('Ctrl Pts',(-7.0987049892125,-53.679865365992,5.96091715184107));
#5989=CARTESIAN_POINT('Ctrl Pts',(-7.12901072727269,-53.679865365992,6.08134414836999));
#5990=CARTESIAN_POINT('Ctrl Pts',(-7.19991003949418,-53.679865365992,6.18313387347449));
#5991=CARTESIAN_POINT('Ctrl Pts',(-7.23358798358248,-53.679865365992,6.23148509950789));
#5992=CARTESIAN_POINT('',(-7.08279480620025,-53.179865365992,5.89769428788757));
#5993=CARTESIAN_POINT('Ctrl Pts',(-7.06096022453606,-53.179865365992,5.44462157020691));
#5994=CARTESIAN_POINT('Ctrl Pts',(-7.06096022453606,-53.679865365992,5.44462157020691));
#5995=CARTESIAN_POINT('Ctrl Pts',(-7.06014301548669,-53.179865365992,5.53707911046172));
#5996=CARTESIAN_POINT('Ctrl Pts',(-7.06014301548669,-53.679865365992,5.53707911046172));
#5997=CARTESIAN_POINT('Ctrl Pts',(-7.05880378783699,-53.179865365992,5.68859688235872));
#5998=CARTESIAN_POINT('Ctrl Pts',(-7.05880378783699,-53.679865365992,5.68859688235872));
#5999=CARTESIAN_POINT('Ctrl Pts',(-7.07606611147406,-53.179865365992,5.83904931548419));
#6000=CARTESIAN_POINT('Ctrl Pts',(-7.07606611147406,-53.679865365992,5.83904931548419));
#6001=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.179865365992,5.89769428788757));
#6002=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.679865365992,5.89769428788757));
#6003=CARTESIAN_POINT('',(-7.06096022453606,-53.679865365992,5.44462157020691));
#6004=CARTESIAN_POINT('Ctrl Pts',(-7.06096022453606,-53.679865365992,5.44462157020691));
#6005=CARTESIAN_POINT('Ctrl Pts',(-7.06014301548669,-53.679865365992,5.53707911046172));
#6006=CARTESIAN_POINT('Ctrl Pts',(-7.05880378783699,-53.679865365992,5.68859688235872));
#6007=CARTESIAN_POINT('Ctrl Pts',(-7.07606611147406,-53.679865365992,5.83904931548419));
#6008=CARTESIAN_POINT('Ctrl Pts',(-7.08279480620025,-53.679865365992,5.89769428788757));
#6009=CARTESIAN_POINT('',(-7.06096022453606,-53.179865365992,5.44462157020691));
#6010=CARTESIAN_POINT('Origin',(-7.06489577122509,-53.179865365992,4.78979054290848));
#6011=CARTESIAN_POINT('',(-7.06489577122509,-53.679865365992,4.78979054290848));
#6012=CARTESIAN_POINT('',(-7.06096022453606,-53.679865365992,5.44462157020691));
#6013=CARTESIAN_POINT('',(-7.06489577122509,-53.179865365992,4.78979054290848));
#6014=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.179865365992,3.92195863714063));
#6015=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.679865365992,3.92195863714063));
#6016=CARTESIAN_POINT('Ctrl Pts',(-7.04807236697943,-53.179865365992,4.02473539840139));
#6017=CARTESIAN_POINT('Ctrl Pts',(-7.04807236697943,-53.679865365992,4.02473539840139));
#6018=CARTESIAN_POINT('Ctrl Pts',(-7.07047754955563,-53.179865365992,4.31348739354906));
#6019=CARTESIAN_POINT('Ctrl Pts',(-7.07047754955563,-53.679865365992,4.31348739354906));
#6020=CARTESIAN_POINT('Ctrl Pts',(-7.06708244278517,-53.179865365992,4.60319793943947));
#6021=CARTESIAN_POINT('Ctrl Pts',(-7.06708244278517,-53.679865365992,4.60319793943947));
#6022=CARTESIAN_POINT('Ctrl Pts',(-7.06489577122509,-53.179865365992,4.78979054290848));
#6023=CARTESIAN_POINT('Ctrl Pts',(-7.06489577122509,-53.679865365992,4.78979054290848));
#6024=CARTESIAN_POINT('',(-7.0400975922962,-53.679865365992,3.92195863714063));
#6025=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.679865365992,3.92195863714063));
#6026=CARTESIAN_POINT('Ctrl Pts',(-7.04807236697943,-53.679865365992,4.02473539840139));
#6027=CARTESIAN_POINT('Ctrl Pts',(-7.07047754955563,-53.679865365992,4.31348739354906));
#6028=CARTESIAN_POINT('Ctrl Pts',(-7.06708244278517,-53.679865365992,4.60319793943947));
#6029=CARTESIAN_POINT('Ctrl Pts',(-7.06489577122509,-53.679865365992,4.78979054290848));
#6030=CARTESIAN_POINT('',(-7.0400975922962,-53.179865365992,3.92195863714063));
#6031=CARTESIAN_POINT('Ctrl Pts',(-6.91667053520953,-53.179865365992,3.57468944090885));
#6032=CARTESIAN_POINT('Ctrl Pts',(-6.91667053520953,-53.679865365992,3.57468944090885));
#6033=CARTESIAN_POINT('Ctrl Pts',(-6.94469935510409,-53.179865365992,3.63059289451729));
#6034=CARTESIAN_POINT('Ctrl Pts',(-6.94469935510409,-53.679865365992,3.63059289451729));
#6035=CARTESIAN_POINT('Ctrl Pts',(-7.00018761831887,-53.179865365992,3.74126418071879));
#6036=CARTESIAN_POINT('Ctrl Pts',(-7.00018761831887,-53.679865365992,3.74126418071879));
#6037=CARTESIAN_POINT('Ctrl Pts',(-7.0268855854292,-53.179865365992,3.86214059765933));
#6038=CARTESIAN_POINT('Ctrl Pts',(-7.0268855854292,-53.679865365992,3.86214059765933));
#6039=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.179865365992,3.92195863714063));
#6040=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.679865365992,3.92195863714063));
#6041=CARTESIAN_POINT('',(-6.91667053520953,-53.679865365992,3.57468944090885));
#6042=CARTESIAN_POINT('Ctrl Pts',(-6.91667053520953,-53.679865365992,3.57468944090885));
#6043=CARTESIAN_POINT('Ctrl Pts',(-6.94469935510409,-53.679865365992,3.63059289451729));
#6044=CARTESIAN_POINT('Ctrl Pts',(-7.00018761831887,-53.679865365992,3.74126418071879));
#6045=CARTESIAN_POINT('Ctrl Pts',(-7.0268855854292,-53.679865365992,3.86214059765933));
#6046=CARTESIAN_POINT('Ctrl Pts',(-7.0400975922962,-53.679865365992,3.92195863714063));
#6047=CARTESIAN_POINT('',(-6.91667053520953,-53.179865365992,3.57468944090885));
#6048=CARTESIAN_POINT('Origin',(-7.42962242051468,-53.179865365992,3.57777229130795));
#6049=CARTESIAN_POINT('',(-7.42962242051468,-53.679865365992,3.57777229130795));
#6050=CARTESIAN_POINT('',(-6.91667053520953,-53.679865365992,3.57468944090885));
#6051=CARTESIAN_POINT('',(-7.42962242051468,-53.179865365992,3.57777229130795));
#6052=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.179865365992,3.9357915116463));
#6053=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.679865365992,3.9357915116463));
#6054=CARTESIAN_POINT('Ctrl Pts',(-7.51719277213793,-53.179865365992,3.87102813710679));
#6055=CARTESIAN_POINT('Ctrl Pts',(-7.51719277213793,-53.679865365992,3.87102813710679));
#6056=CARTESIAN_POINT('Ctrl Pts',(-7.50095325751147,-53.179865365992,3.74737992806695));
#6057=CARTESIAN_POINT('Ctrl Pts',(-7.50095325751147,-53.679865365992,3.74737992806695));
#6058=CARTESIAN_POINT('Ctrl Pts',(-7.45263362139022,-53.179865365992,3.63248741261884));
#6059=CARTESIAN_POINT('Ctrl Pts',(-7.45263362139022,-53.679865365992,3.63248741261884));
#6060=CARTESIAN_POINT('Ctrl Pts',(-7.42962242051468,-53.179865365992,3.57777229130795));
#6061=CARTESIAN_POINT('Ctrl Pts',(-7.42962242051468,-53.679865365992,3.57777229130795));
#6062=CARTESIAN_POINT('Ctrl Pts',(-7.52569856268888,-53.679865365992,3.9357915116463));
#6063=CARTESIAN_POINT('Ctrl Pts',(-7.51719277213793,-53.679865365992,3.87102813710679));
#6064=CARTESIAN_POINT('Ctrl Pts',(-7.50095325751147,-53.679865365992,3.74737992806695));
#6065=CARTESIAN_POINT('Ctrl Pts',(-7.45263362139022,-53.679865365992,3.63248741261884));
#6066=CARTESIAN_POINT('Ctrl Pts',(-7.42962242051468,-53.679865365992,3.57777229130795));
#6067=CARTESIAN_POINT('Origin',(-13.4911611848099,-53.679865365992,5.09371901156747));
#6068=CARTESIAN_POINT('Origin',(-54.2053113423059,-53.179865365992,0.));
#6069=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.179865365992,4.1815752809219));
#6070=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.679865365992,4.1815752809219));
#6071=CARTESIAN_POINT('Ctrl Pts',(-5.81272563570367,-53.179865365992,4.29435966016429));
#6072=CARTESIAN_POINT('Ctrl Pts',(-5.81272563570367,-53.679865365992,4.29435966016429));
#6073=CARTESIAN_POINT('Ctrl Pts',(-5.96550316412344,-53.179865365992,4.54952823580198));
#6074=CARTESIAN_POINT('Ctrl Pts',(-5.96550316412344,-53.679865365992,4.54952823580198));
#6075=CARTESIAN_POINT('Ctrl Pts',(-5.97149929593916,-53.179865365992,4.84839766989917));
#6076=CARTESIAN_POINT('Ctrl Pts',(-5.97149929593916,-53.679865365992,4.84839766989917));
#6077=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.179865365992,5.01516696778274));
#6078=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.679865365992,5.01516696778274));
#6079=CARTESIAN_POINT('',(-5.97484514057512,-53.179865365992,5.01516696778274));
#6080=CARTESIAN_POINT('',(-5.74519804668943,-53.179865365992,4.1815752809219));
#6081=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.179865365992,5.01516696778274));
#6082=CARTESIAN_POINT('Ctrl Pts',(-5.97149929593916,-53.179865365992,4.84839766989917));
#6083=CARTESIAN_POINT('Ctrl Pts',(-5.96550316412344,-53.179865365992,4.54952823580198));
#6084=CARTESIAN_POINT('Ctrl Pts',(-5.81272563570367,-53.179865365992,4.29435966016429));
#6085=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.179865365992,4.1815752809219));
#6086=CARTESIAN_POINT('',(-5.97484514057512,-53.679865365992,5.01516696778274));
#6087=CARTESIAN_POINT('',(-5.97484514057512,-53.179865365992,5.01516696778274));
#6088=CARTESIAN_POINT('',(-5.74519804668943,-53.679865365992,4.1815752809219));
#6089=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.679865365992,4.1815752809219));
#6090=CARTESIAN_POINT('Ctrl Pts',(-5.81272563570367,-53.679865365992,4.29435966016429));
#6091=CARTESIAN_POINT('Ctrl Pts',(-5.96550316412344,-53.679865365992,4.54952823580198));
#6092=CARTESIAN_POINT('Ctrl Pts',(-5.97149929593916,-53.679865365992,4.84839766989917));
#6093=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.679865365992,5.01516696778274));
#6094=CARTESIAN_POINT('',(-5.74519804668943,-53.179865365992,4.1815752809219));
#6095=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.179865365992,3.90267174927831));
#6096=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.679865365992,3.90267174927831));
#6097=CARTESIAN_POINT('Ctrl Pts',(-5.29708802730796,-53.179865365992,3.91635140350598));
#6098=CARTESIAN_POINT('Ctrl Pts',(-5.29708802730796,-53.679865365992,3.91635140350598));
#6099=CARTESIAN_POINT('Ctrl Pts',(-5.51310984340115,-53.179865365992,3.94473533601573));
#6100=CARTESIAN_POINT('Ctrl Pts',(-5.51310984340115,-53.679865365992,3.94473533601573));
#6101=CARTESIAN_POINT('Ctrl Pts',(-5.66599565085019,-53.179865365992,4.10075130623801));
#6102=CARTESIAN_POINT('Ctrl Pts',(-5.66599565085019,-53.679865365992,4.10075130623801));
#6103=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.179865365992,4.1815752809219));
#6104=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.679865365992,4.1815752809219));
#6105=CARTESIAN_POINT('',(-5.19297617639467,-53.179865365992,3.90267174927831));
#6106=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.179865365992,4.1815752809219));
#6107=CARTESIAN_POINT('Ctrl Pts',(-5.66599565085019,-53.179865365992,4.10075130623801));
#6108=CARTESIAN_POINT('Ctrl Pts',(-5.51310984340115,-53.179865365992,3.94473533601573));
#6109=CARTESIAN_POINT('Ctrl Pts',(-5.29708802730796,-53.179865365992,3.91635140350598));
#6110=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.179865365992,3.90267174927831));
#6111=CARTESIAN_POINT('',(-5.19297617639467,-53.679865365992,3.90267174927831));
#6112=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.679865365992,3.90267174927831));
#6113=CARTESIAN_POINT('Ctrl Pts',(-5.29708802730796,-53.679865365992,3.91635140350598));
#6114=CARTESIAN_POINT('Ctrl Pts',(-5.51310984340115,-53.679865365992,3.94473533601573));
#6115=CARTESIAN_POINT('Ctrl Pts',(-5.66599565085019,-53.679865365992,4.10075130623801));
#6116=CARTESIAN_POINT('Ctrl Pts',(-5.74519804668943,-53.679865365992,4.1815752809219));
#6117=CARTESIAN_POINT('',(-5.19297617639467,-53.179865365992,3.90267174927831));
#6118=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.179865365992,4.16132448074137));
#6119=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.679865365992,4.16132448074137));
#6120=CARTESIAN_POINT('Ctrl Pts',(-4.72375741909285,-53.179865365992,4.08540231117127));
#6121=CARTESIAN_POINT('Ctrl Pts',(-4.72375741909285,-53.679865365992,4.08540231117127));
#6122=CARTESIAN_POINT('Ctrl Pts',(-4.87687464631777,-53.179865365992,3.93645482125363));
#6123=CARTESIAN_POINT('Ctrl Pts',(-4.87687464631777,-53.679865365992,3.93645482125363));
#6124=CARTESIAN_POINT('Ctrl Pts',(-5.08898409074852,-53.179865365992,3.91378581162845));
#6125=CARTESIAN_POINT('Ctrl Pts',(-5.08898409074852,-53.679865365992,3.91378581162845));
#6126=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.179865365992,3.90267174927831));
#6127=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.679865365992,3.90267174927831));
#6128=CARTESIAN_POINT('',(-4.64570983268571,-53.179865365992,4.16132448074137));
#6129=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.179865365992,3.90267174927831));
#6130=CARTESIAN_POINT('Ctrl Pts',(-5.08898409074852,-53.179865365992,3.91378581162845));
#6131=CARTESIAN_POINT('Ctrl Pts',(-4.87687464631777,-53.179865365992,3.93645482125363));
#6132=CARTESIAN_POINT('Ctrl Pts',(-4.72375741909285,-53.179865365992,4.08540231117127));
#6133=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.179865365992,4.16132448074137));
#6134=CARTESIAN_POINT('',(-4.64570983268571,-53.679865365992,4.16132448074137));
#6135=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.679865365992,4.16132448074137));
#6136=CARTESIAN_POINT('Ctrl Pts',(-4.72375741909285,-53.679865365992,4.08540231117127));
#6137=CARTESIAN_POINT('Ctrl Pts',(-4.87687464631777,-53.679865365992,3.93645482125363));
#6138=CARTESIAN_POINT('Ctrl Pts',(-5.08898409074852,-53.679865365992,3.91378581162845));
#6139=CARTESIAN_POINT('Ctrl Pts',(-5.19297617639467,-53.679865365992,3.90267174927831));
#6140=CARTESIAN_POINT('',(-4.64570983268571,-53.179865365992,4.16132448074137));
#6141=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.179865365992,4.96486045391682));
#6142=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.679865365992,4.96486045391682));
#6143=CARTESIAN_POINT('Ctrl Pts',(-4.419454264107,-53.179865365992,4.80326979235334));
#6144=CARTESIAN_POINT('Ctrl Pts',(-4.419454264107,-53.679865365992,4.80326979235334));
#6145=CARTESIAN_POINT('Ctrl Pts',(-4.42847607806648,-53.179865365992,4.51427043375099));
#6146=CARTESIAN_POINT('Ctrl Pts',(-4.42847607806648,-53.679865365992,4.51427043375099));
#6147=CARTESIAN_POINT('Ctrl Pts',(-4.57924265162631,-53.179865365992,4.26931562882051));
#6148=CARTESIAN_POINT('Ctrl Pts',(-4.57924265162631,-53.679865365992,4.26931562882051));
#6149=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.179865365992,4.16132448074137));
#6150=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.679865365992,4.16132448074137));
#6151=CARTESIAN_POINT('',(-4.41440982077928,-53.179865365992,4.96486045391682));
#6152=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.179865365992,4.16132448074137));
#6153=CARTESIAN_POINT('Ctrl Pts',(-4.57924265162631,-53.179865365992,4.26931562882051));
#6154=CARTESIAN_POINT('Ctrl Pts',(-4.42847607806648,-53.179865365992,4.51427043375099));
#6155=CARTESIAN_POINT('Ctrl Pts',(-4.419454264107,-53.179865365992,4.80326979235334));
#6156=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.179865365992,4.96486045391682));
#6157=CARTESIAN_POINT('',(-4.41440982077928,-53.679865365992,4.96486045391682));
#6158=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.679865365992,4.96486045391682));
#6159=CARTESIAN_POINT('Ctrl Pts',(-4.419454264107,-53.679865365992,4.80326979235334));
#6160=CARTESIAN_POINT('Ctrl Pts',(-4.42847607806648,-53.679865365992,4.51427043375099));
#6161=CARTESIAN_POINT('Ctrl Pts',(-4.57924265162631,-53.679865365992,4.26931562882051));
#6162=CARTESIAN_POINT('Ctrl Pts',(-4.64570983268571,-53.679865365992,4.16132448074137));
#6163=CARTESIAN_POINT('',(-4.41440982077928,-53.179865365992,4.96486045391682));
#6164=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.179865365992,5.83934612531939));
#6165=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.679865365992,5.83934612531939));
#6166=CARTESIAN_POINT('Ctrl Pts',(-4.57250614658664,-53.179865365992,5.72245742267791));
#6167=CARTESIAN_POINT('Ctrl Pts',(-4.57250614658664,-53.679865365992,5.72245742267791));
#6168=CARTESIAN_POINT('Ctrl Pts',(-4.42051919846684,-53.179865365992,5.45265400920018));
#6169=CARTESIAN_POINT('Ctrl Pts',(-4.42051919846684,-53.679865365992,5.45265400920018));
#6170=CARTESIAN_POINT('Ctrl Pts',(-4.41661983655279,-53.179865365992,5.14131565222419));
#6171=CARTESIAN_POINT('Ctrl Pts',(-4.41661983655279,-53.679865365992,5.14131565222419));
#6172=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.179865365992,4.96486045391682));
#6173=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.679865365992,4.96486045391682));
#6174=CARTESIAN_POINT('',(-4.63835244910432,-53.179865365992,5.83934612531939));
#6175=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.179865365992,4.96486045391682));
#6176=CARTESIAN_POINT('Ctrl Pts',(-4.41661983655279,-53.179865365992,5.14131565222419));
#6177=CARTESIAN_POINT('Ctrl Pts',(-4.42051919846684,-53.179865365992,5.45265400920018));
#6178=CARTESIAN_POINT('Ctrl Pts',(-4.57250614658664,-53.179865365992,5.72245742267791));
#6179=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.179865365992,5.83934612531939));
#6180=CARTESIAN_POINT('',(-4.63835244910432,-53.679865365992,5.83934612531939));
#6181=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.679865365992,5.83934612531939));
#6182=CARTESIAN_POINT('Ctrl Pts',(-4.57250614658664,-53.679865365992,5.72245742267791));
#6183=CARTESIAN_POINT('Ctrl Pts',(-4.42051919846684,-53.679865365992,5.45265400920018));
#6184=CARTESIAN_POINT('Ctrl Pts',(-4.41661983655279,-53.679865365992,5.14131565222419));
#6185=CARTESIAN_POINT('Ctrl Pts',(-4.41440982077928,-53.679865365992,4.96486045391682));
#6186=CARTESIAN_POINT('',(-4.63835244910432,-53.179865365992,5.83934612531939));
#6187=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.179865365992,6.12104390530361));
#6188=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.679865365992,6.12104390530361));
#6189=CARTESIAN_POINT('Ctrl Pts',(-5.09335819122246,-53.179865365992,6.10786530212848));
#6190=CARTESIAN_POINT('Ctrl Pts',(-5.09335819122246,-53.679865365992,6.10786530212848));
#6191=CARTESIAN_POINT('Ctrl Pts',(-4.87255137859632,-53.179865365992,6.08095004265739));
#6192=CARTESIAN_POINT('Ctrl Pts',(-4.87255137859632,-53.679865365992,6.08095004265739));
#6193=CARTESIAN_POINT('Ctrl Pts',(-4.71749042178309,-53.179865365992,5.92098631151004));
#6194=CARTESIAN_POINT('Ctrl Pts',(-4.71749042178309,-53.679865365992,5.92098631151004));
#6195=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.179865365992,5.83934612531939));
#6196=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.679865365992,5.83934612531939));
#6197=CARTESIAN_POINT('',(-5.20147252558703,-53.179865365992,6.12104390530361));
#6198=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.179865365992,5.83934612531939));
#6199=CARTESIAN_POINT('Ctrl Pts',(-4.71749042178309,-53.179865365992,5.92098631151004));
#6200=CARTESIAN_POINT('Ctrl Pts',(-4.87255137859632,-53.179865365992,6.08095004265739));
#6201=CARTESIAN_POINT('Ctrl Pts',(-5.09335819122246,-53.179865365992,6.10786530212848));
#6202=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.179865365992,6.12104390530361));
#6203=CARTESIAN_POINT('',(-5.20147252558703,-53.679865365992,6.12104390530361));
#6204=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.679865365992,6.12104390530361));
#6205=CARTESIAN_POINT('Ctrl Pts',(-5.09335819122246,-53.679865365992,6.10786530212848));
#6206=CARTESIAN_POINT('Ctrl Pts',(-4.87255137859632,-53.679865365992,6.08095004265739));
#6207=CARTESIAN_POINT('Ctrl Pts',(-4.71749042178309,-53.679865365992,5.92098631151004));
#6208=CARTESIAN_POINT('Ctrl Pts',(-4.63835244910432,-53.679865365992,5.83934612531939));
#6209=CARTESIAN_POINT('',(-5.20147252558703,-53.179865365992,6.12104390530361));
#6210=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.179865365992,5.8569505683872));
#6211=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.679865365992,5.8569505683872));
#6212=CARTESIAN_POINT('Ctrl Pts',(-5.67387065185365,-53.179865365992,5.93446912997831));
#6213=CARTESIAN_POINT('Ctrl Pts',(-5.67387065185365,-53.679865365992,5.93446912997831));
#6214=CARTESIAN_POINT('Ctrl Pts',(-5.52116491407392,-53.179865365992,6.08695631465231));
#6215=CARTESIAN_POINT('Ctrl Pts',(-5.52116491407392,-53.679865365992,6.08695631465231));
#6216=CARTESIAN_POINT('Ctrl Pts',(-5.30684199674407,-53.179865365992,6.10980875698824));
#6217=CARTESIAN_POINT('Ctrl Pts',(-5.30684199674407,-53.679865365992,6.10980875698824));
#6218=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.179865365992,6.12104390530361));
#6219=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.679865365992,6.12104390530361));
#6220=CARTESIAN_POINT('',(-5.7515003173544,-53.179865365992,5.8569505683872));
#6221=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.179865365992,6.12104390530361));
#6222=CARTESIAN_POINT('Ctrl Pts',(-5.30684199674407,-53.179865365992,6.10980875698824));
#6223=CARTESIAN_POINT('Ctrl Pts',(-5.52116491407392,-53.179865365992,6.08695631465231));
#6224=CARTESIAN_POINT('Ctrl Pts',(-5.67387065185365,-53.179865365992,5.93446912997831));
#6225=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.179865365992,5.8569505683872));
#6226=CARTESIAN_POINT('',(-5.7515003173544,-53.679865365992,5.8569505683872));
#6227=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.679865365992,5.8569505683872));
#6228=CARTESIAN_POINT('Ctrl Pts',(-5.67387065185365,-53.679865365992,5.93446912997831));
#6229=CARTESIAN_POINT('Ctrl Pts',(-5.52116491407392,-53.679865365992,6.08695631465231));
#6230=CARTESIAN_POINT('Ctrl Pts',(-5.30684199674407,-53.679865365992,6.10980875698824));
#6231=CARTESIAN_POINT('Ctrl Pts',(-5.20147252558703,-53.679865365992,6.12104390530361));
#6232=CARTESIAN_POINT('',(-5.7515003173544,-53.179865365992,5.8569505683872));
#6233=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.179865365992,5.01516696778274));
#6234=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.679865365992,5.01516696778274));
#6235=CARTESIAN_POINT('Ctrl Pts',(-5.97084962290964,-53.179865365992,5.18567382687644));
#6236=CARTESIAN_POINT('Ctrl Pts',(-5.97084962290964,-53.679865365992,5.18567382687644));
#6237=CARTESIAN_POINT('Ctrl Pts',(-5.96381613652803,-53.179865365992,5.48582458875861));
#6238=CARTESIAN_POINT('Ctrl Pts',(-5.96381613652803,-53.679865365992,5.48582458875861));
#6239=CARTESIAN_POINT('Ctrl Pts',(-5.81554357727363,-53.179865365992,5.74500357513609));
#6240=CARTESIAN_POINT('Ctrl Pts',(-5.81554357727363,-53.679865365992,5.74500357513609));
#6241=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.179865365992,5.8569505683872));
#6242=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.679865365992,5.8569505683872));
#6243=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.179865365992,5.8569505683872));
#6244=CARTESIAN_POINT('Ctrl Pts',(-5.81554357727363,-53.179865365992,5.74500357513609));
#6245=CARTESIAN_POINT('Ctrl Pts',(-5.96381613652803,-53.179865365992,5.48582458875861));
#6246=CARTESIAN_POINT('Ctrl Pts',(-5.97084962290964,-53.179865365992,5.18567382687644));
#6247=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.179865365992,5.01516696778274));
#6248=CARTESIAN_POINT('Ctrl Pts',(-5.97484514057512,-53.679865365992,5.01516696778274));
#6249=CARTESIAN_POINT('Ctrl Pts',(-5.97084962290964,-53.679865365992,5.18567382687644));
#6250=CARTESIAN_POINT('Ctrl Pts',(-5.96381613652803,-53.679865365992,5.48582458875861));
#6251=CARTESIAN_POINT('Ctrl Pts',(-5.81554357727363,-53.679865365992,5.74500357513609));
#6252=CARTESIAN_POINT('Ctrl Pts',(-5.7515003173544,-53.679865365992,5.8569505683872));
#6253=CARTESIAN_POINT('Origin',(-4.42884226548031,-53.179865365992,3.92536500122969));
#6254=CARTESIAN_POINT('',(-4.43103961393397,-53.679865365992,3.55975075270594));
#6255=CARTESIAN_POINT('',(-4.43103961393397,-53.179865365992,3.55975075270594));
#6256=CARTESIAN_POINT('',(-4.42884226548031,-53.679865365992,3.92536500122969));
#6257=CARTESIAN_POINT('',(-4.43103961393397,-53.679865365992,3.55975075270594));
#6258=CARTESIAN_POINT('',(-4.42884226548031,-53.179865365992,3.92536500122969));
#6259=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.179865365992,3.49913812759717));
#6260=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.679865365992,3.49913812759717));
#6261=CARTESIAN_POINT('Ctrl Pts',(-5.07485935915977,-53.179865365992,3.51511817879132));
#6262=CARTESIAN_POINT('Ctrl Pts',(-5.07485935915977,-53.679865365992,3.51511817879132));
#6263=CARTESIAN_POINT('Ctrl Pts',(-4.74540703106753,-53.179865365992,3.54665686148458));
#6264=CARTESIAN_POINT('Ctrl Pts',(-4.74540703106753,-53.679865365992,3.54665686148458));
#6265=CARTESIAN_POINT('Ctrl Pts',(-4.53341966268081,-53.179865365992,3.80025849583665));
#6266=CARTESIAN_POINT('Ctrl Pts',(-4.53341966268081,-53.679865365992,3.80025849583665));
#6267=CARTESIAN_POINT('Ctrl Pts',(-4.42884226548031,-53.179865365992,3.92536500122969));
#6268=CARTESIAN_POINT('Ctrl Pts',(-4.42884226548031,-53.679865365992,3.92536500122969));
#6269=CARTESIAN_POINT('',(-5.24178659325359,-53.679865365992,3.49913812759717));
#6270=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.679865365992,3.49913812759717));
#6271=CARTESIAN_POINT('Ctrl Pts',(-5.07485935915977,-53.679865365992,3.51511817879132));
#6272=CARTESIAN_POINT('Ctrl Pts',(-4.74540703106753,-53.679865365992,3.54665686148458));
#6273=CARTESIAN_POINT('Ctrl Pts',(-4.53341966268081,-53.679865365992,3.80025849583665));
#6274=CARTESIAN_POINT('Ctrl Pts',(-4.42884226548031,-53.679865365992,3.92536500122969));
#6275=CARTESIAN_POINT('',(-5.24178659325359,-53.179865365992,3.49913812759717));
#6276=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.179865365992,3.69396747742505));
#6277=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.679865365992,3.69396747742505));
#6278=CARTESIAN_POINT('Ctrl Pts',(-5.77995078854183,-53.179865365992,3.63766346139997));
#6279=CARTESIAN_POINT('Ctrl Pts',(-5.77995078854183,-53.679865365992,3.63766346139997));
#6280=CARTESIAN_POINT('Ctrl Pts',(-5.58200844233149,-53.179865365992,3.5252578958788));
#6281=CARTESIAN_POINT('Ctrl Pts',(-5.58200844233149,-53.679865365992,3.5252578958788));
#6282=CARTESIAN_POINT('Ctrl Pts',(-5.35505761390894,-53.179865365992,3.50783425578694));
#6283=CARTESIAN_POINT('Ctrl Pts',(-5.35505761390894,-53.679865365992,3.50783425578694));
#6284=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.179865365992,3.49913812759717));
#6285=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.679865365992,3.49913812759717));
#6286=CARTESIAN_POINT('',(-5.87910022995425,-53.679865365992,3.69396747742505));
#6287=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.679865365992,3.69396747742505));
#6288=CARTESIAN_POINT('Ctrl Pts',(-5.77995078854183,-53.679865365992,3.63766346139997));
#6289=CARTESIAN_POINT('Ctrl Pts',(-5.58200844233149,-53.679865365992,3.5252578958788));
#6290=CARTESIAN_POINT('Ctrl Pts',(-5.35505761390894,-53.679865365992,3.50783425578694));
#6291=CARTESIAN_POINT('Ctrl Pts',(-5.24178659325359,-53.679865365992,3.49913812759717));
#6292=CARTESIAN_POINT('',(-5.87910022995425,-53.179865365992,3.69396747742505));
#6293=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.179865365992,4.2287235253582));
#6294=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.679865365992,4.2287235253582));
#6295=CARTESIAN_POINT('Ctrl Pts',(-6.26885672300346,-53.179865365992,4.1216917934364));
#6296=CARTESIAN_POINT('Ctrl Pts',(-6.26885672300346,-53.679865365992,4.1216917934364));
#6297=CARTESIAN_POINT('Ctrl Pts',(-6.15687188381731,-53.179865365992,3.91230258572484));
#6298=CARTESIAN_POINT('Ctrl Pts',(-6.15687188381731,-53.679865365992,3.91230258572484));
#6299=CARTESIAN_POINT('Ctrl Pts',(-5.97030250038754,-53.179865365992,3.76565462517864));
#6300=CARTESIAN_POINT('Ctrl Pts',(-5.97030250038754,-53.679865365992,3.76565462517864));
#6301=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.179865365992,3.69396747742505));
#6302=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.679865365992,3.69396747742505));
#6303=CARTESIAN_POINT('',(-6.32609907765045,-53.679865365992,4.2287235253582));
#6304=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.679865365992,4.2287235253582));
#6305=CARTESIAN_POINT('Ctrl Pts',(-6.26885672300346,-53.679865365992,4.1216917934364));
#6306=CARTESIAN_POINT('Ctrl Pts',(-6.15687188381731,-53.679865365992,3.91230258572484));
#6307=CARTESIAN_POINT('Ctrl Pts',(-5.97030250038754,-53.679865365992,3.76565462517864));
#6308=CARTESIAN_POINT('Ctrl Pts',(-5.87910022995425,-53.679865365992,3.69396747742505));
#6309=CARTESIAN_POINT('',(-6.32609907765045,-53.179865365992,4.2287235253582));
#6310=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.179865365992,5.01820061328078));
#6311=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.679865365992,5.01820061328078));
#6312=CARTESIAN_POINT('Ctrl Pts',(-6.47503721878306,-53.179865365992,4.87673921261257));
#6313=CARTESIAN_POINT('Ctrl Pts',(-6.47503721878306,-53.679865365992,4.87673921261257));
#6314=CARTESIAN_POINT('Ctrl Pts',(-6.4662578408353,-53.179865365992,4.60513737839161));
#6315=CARTESIAN_POINT('Ctrl Pts',(-6.4662578408353,-53.679865365992,4.60513737839161));
#6316=CARTESIAN_POINT('Ctrl Pts',(-6.37150212251345,-53.179865365992,4.35065906950817));
#6317=CARTESIAN_POINT('Ctrl Pts',(-6.37150212251345,-53.679865365992,4.35065906950817));
#6318=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.179865365992,4.2287235253582));
#6319=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.679865365992,4.2287235253582));
#6320=CARTESIAN_POINT('',(-6.47960987973377,-53.679865365992,5.01820061328078));
#6321=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.679865365992,5.01820061328078));
#6322=CARTESIAN_POINT('Ctrl Pts',(-6.47503721878306,-53.679865365992,4.87673921261257));
#6323=CARTESIAN_POINT('Ctrl Pts',(-6.4662578408353,-53.679865365992,4.60513737839161));
#6324=CARTESIAN_POINT('Ctrl Pts',(-6.37150212251345,-53.679865365992,4.35065906950817));
#6325=CARTESIAN_POINT('Ctrl Pts',(-6.32609907765045,-53.679865365992,4.2287235253582));
#6326=CARTESIAN_POINT('',(-6.47960987973377,-53.179865365992,5.01820061328078));
#6327=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.179865365992,5.80312916772836));
#6328=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.679865365992,5.80312916772836));
#6329=CARTESIAN_POINT('Ctrl Pts',(-6.37318282275842,-53.179865365992,5.68036447373134));
#6330=CARTESIAN_POINT('Ctrl Pts',(-6.37318282275842,-53.679865365992,5.68036447373134));
#6331=CARTESIAN_POINT('Ctrl Pts',(-6.46197972408242,-53.179865365992,5.42627911657908));
#6332=CARTESIAN_POINT('Ctrl Pts',(-6.46197972408242,-53.679865365992,5.42627911657908));
#6333=CARTESIAN_POINT('Ctrl Pts',(-6.47360269870993,-53.179865365992,5.15724654626795));
#6334=CARTESIAN_POINT('Ctrl Pts',(-6.47360269870993,-53.679865365992,5.15724654626795));
#6335=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.179865365992,5.01820061328078));
#6336=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.679865365992,5.01820061328078));
#6337=CARTESIAN_POINT('',(-6.33027942784774,-53.679865365992,5.80312916772836));
#6338=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.679865365992,5.80312916772836));
#6339=CARTESIAN_POINT('Ctrl Pts',(-6.37318282275842,-53.679865365992,5.68036447373134));
#6340=CARTESIAN_POINT('Ctrl Pts',(-6.46197972408242,-53.679865365992,5.42627911657908));
#6341=CARTESIAN_POINT('Ctrl Pts',(-6.47360269870993,-53.679865365992,5.15724654626795));
#6342=CARTESIAN_POINT('Ctrl Pts',(-6.47960987973377,-53.679865365992,5.01820061328078));
#6343=CARTESIAN_POINT('',(-6.33027942784774,-53.179865365992,5.80312916772836));
#6344=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.179865365992,6.34348593686198));
#6345=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.679865365992,6.34348593686198));
#6346=CARTESIAN_POINT('Ctrl Pts',(-5.98462633710445,-53.179865365992,6.27277178326018));
#6347=CARTESIAN_POINT('Ctrl Pts',(-5.98462633710445,-53.679865365992,6.27277178326018));
#6348=CARTESIAN_POINT('Ctrl Pts',(-6.17275175277424,-53.179865365992,6.1272804622048));
#6349=CARTESIAN_POINT('Ctrl Pts',(-6.17275175277424,-53.679865365992,6.1272804622048));
#6350=CARTESIAN_POINT('Ctrl Pts',(-6.27680163164874,-53.179865365992,5.91317266869139));
#6351=CARTESIAN_POINT('Ctrl Pts',(-6.27680163164874,-53.679865365992,5.91317266869139));
#6352=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.179865365992,5.80312916772836));
#6353=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.679865365992,5.80312916772836));
#6354=CARTESIAN_POINT('',(-5.89319043946068,-53.679865365992,6.34348593686198));
#6355=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.679865365992,6.34348593686198));
#6356=CARTESIAN_POINT('Ctrl Pts',(-5.98462633710445,-53.679865365992,6.27277178326018));
#6357=CARTESIAN_POINT('Ctrl Pts',(-6.17275175277424,-53.679865365992,6.1272804622048));
#6358=CARTESIAN_POINT('Ctrl Pts',(-6.27680163164874,-53.679865365992,5.91317266869139));
#6359=CARTESIAN_POINT('Ctrl Pts',(-6.33027942784774,-53.679865365992,5.80312916772836));
#6360=CARTESIAN_POINT('',(-5.89319043946068,-53.179865365992,6.34348593686198));
#6361=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.179865365992,6.52786370970605));
#6362=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.679865365992,6.52786370970605));
#6363=CARTESIAN_POINT('Ctrl Pts',(-5.36129969174987,-53.179865365992,6.52101310460599));
#6364=CARTESIAN_POINT('Ctrl Pts',(-5.36129969174987,-53.679865365992,6.52101310460599));
#6365=CARTESIAN_POINT('Ctrl Pts',(-5.5909269808181,-53.179865365992,6.50743879485976));
#6366=CARTESIAN_POINT('Ctrl Pts',(-5.5909269808181,-53.679865365992,6.50743879485976));
#6367=CARTESIAN_POINT('Ctrl Pts',(-5.79306585673279,-53.179865365992,6.39779521912784));
#6368=CARTESIAN_POINT('Ctrl Pts',(-5.79306585673279,-53.679865365992,6.39779521912784));
#6369=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.179865365992,6.34348593686198));
#6370=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.679865365992,6.34348593686198));
#6371=CARTESIAN_POINT('',(-5.24541270323915,-53.679865365992,6.52786370970605));
#6372=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.679865365992,6.52786370970605));
#6373=CARTESIAN_POINT('Ctrl Pts',(-5.36129969174987,-53.679865365992,6.52101310460599));
#6374=CARTESIAN_POINT('Ctrl Pts',(-5.5909269808181,-53.679865365992,6.50743879485976));
#6375=CARTESIAN_POINT('Ctrl Pts',(-5.79306585673279,-53.679865365992,6.39779521912784));
#6376=CARTESIAN_POINT('Ctrl Pts',(-5.89319043946068,-53.679865365992,6.34348593686198));
#6377=CARTESIAN_POINT('',(-5.24541270323915,-53.179865365992,6.52786370970605));
#6378=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.179865365992,6.41319267492323));
#6379=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.679865365992,6.41319267492323));
#6380=CARTESIAN_POINT('Ctrl Pts',(-4.85117369815383,-53.179865365992,6.44624401350633));
#6381=CARTESIAN_POINT('Ctrl Pts',(-4.85117369815383,-53.679865365992,6.44624401350633));
#6382=CARTESIAN_POINT('Ctrl Pts',(-4.99897707819382,-53.179865365992,6.51441846510743));
#6383=CARTESIAN_POINT('Ctrl Pts',(-4.99897707819382,-53.679865365992,6.51441846510743));
#6384=CARTESIAN_POINT('Ctrl Pts',(-5.16161995999254,-53.179865365992,6.52329207389514));
#6385=CARTESIAN_POINT('Ctrl Pts',(-5.16161995999254,-53.679865365992,6.52329207389514));
#6386=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.179865365992,6.52786370970605));
#6387=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.679865365992,6.52786370970605));
#6388=CARTESIAN_POINT('',(-4.77951782938334,-53.679865365992,6.41319267492323));
#6389=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.679865365992,6.41319267492323));
#6390=CARTESIAN_POINT('Ctrl Pts',(-4.85117369815383,-53.679865365992,6.44624401350633));
#6391=CARTESIAN_POINT('Ctrl Pts',(-4.99897707819382,-53.679865365992,6.51441846510743));
#6392=CARTESIAN_POINT('Ctrl Pts',(-5.16161995999254,-53.679865365992,6.52329207389514));
#6393=CARTESIAN_POINT('Ctrl Pts',(-5.24541270323915,-53.679865365992,6.52786370970605));
#6394=CARTESIAN_POINT('',(-4.77951782938334,-53.179865365992,6.41319267492323));
#6395=CARTESIAN_POINT('Ctrl Pts',(-4.44836681558938,-53.179865365992,6.1247033796356));
#6396=CARTESIAN_POINT('Ctrl Pts',(-4.44836681558938,-53.679865365992,6.1247033796356));
#6397=CARTESIAN_POINT('Ctrl Pts',(-4.49445631188157,-53.179865365992,6.18152155546646));
#6398=CARTESIAN_POINT('Ctrl Pts',(-4.49445631188157,-53.679865365992,6.18152155546646));
#6399=CARTESIAN_POINT('Ctrl Pts',(-4.58804494606992,-53.179865365992,6.29689568408293));
#6400=CARTESIAN_POINT('Ctrl Pts',(-4.58804494606992,-53.679865365992,6.29689568408293));
#6401=CARTESIAN_POINT('Ctrl Pts',(-4.71505585118702,-53.179865365992,6.37403969431253));
#6402=CARTESIAN_POINT('Ctrl Pts',(-4.71505585118702,-53.679865365992,6.37403969431253));
#6403=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.179865365992,6.41319267492323));
#6404=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.679865365992,6.41319267492323));
#6405=CARTESIAN_POINT('',(-4.44836681558938,-53.679865365992,6.1247033796356));
#6406=CARTESIAN_POINT('Ctrl Pts',(-4.44836681558938,-53.679865365992,6.1247033796356));
#6407=CARTESIAN_POINT('Ctrl Pts',(-4.49445631188157,-53.679865365992,6.18152155546646));
#6408=CARTESIAN_POINT('Ctrl Pts',(-4.58804494606992,-53.679865365992,6.29689568408293));
#6409=CARTESIAN_POINT('Ctrl Pts',(-4.71505585118702,-53.679865365992,6.37403969431253));
#6410=CARTESIAN_POINT('Ctrl Pts',(-4.77951782938334,-53.679865365992,6.41319267492323));
#6411=CARTESIAN_POINT('',(-4.44836681558938,-53.179865365992,6.1247033796356));
#6412=CARTESIAN_POINT('Origin',(-4.4397414052749,-53.179865365992,7.55987535023605));
#6413=CARTESIAN_POINT('',(-4.4397414052749,-53.679865365992,7.55987535023605));
#6414=CARTESIAN_POINT('',(-4.44836681558938,-53.679865365992,6.1247033796356));
#6415=CARTESIAN_POINT('',(-4.4397414052749,-53.179865365992,7.55987535023605));
#6416=CARTESIAN_POINT('Origin',(-3.95134619012377,-53.179865365992,7.55694008588264));
#6417=CARTESIAN_POINT('',(-3.95134619012377,-53.679865365992,7.55694008588264));
#6418=CARTESIAN_POINT('',(-4.4397414052749,-53.679865365992,7.55987535023605));
#6419=CARTESIAN_POINT('',(-3.95134619012377,-53.179865365992,7.55694008588264));
#6420=CARTESIAN_POINT('Origin',(-3.97538583094063,-53.179865365992,3.55701226497055));
#6421=CARTESIAN_POINT('',(-3.97538583094063,-53.679865365992,3.55701226497055));
#6422=CARTESIAN_POINT('',(-3.95134619012377,-53.679865365992,7.55694008588264));
#6423=CARTESIAN_POINT('',(-3.97538583094063,-53.179865365992,3.55701226497055));
#6424=CARTESIAN_POINT('Origin',(-4.43103961393397,-53.179865365992,3.55975075270594));
#6425=CARTESIAN_POINT('',(-3.97538583094063,-53.679865365992,3.55701226497055));
#6426=CARTESIAN_POINT('Origin',(-13.4911611848099,-53.679865365992,5.09371901156747));
#6427=CARTESIAN_POINT('Origin',(-54.2053113423059,-53.179865365992,0.));
#6428=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.179865365992,4.91191993609606));
#6429=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.679865365992,4.91191993609606));
#6430=CARTESIAN_POINT('Ctrl Pts',(-18.1484981337832,-53.179865365992,4.93408477881581));
#6431=CARTESIAN_POINT('Ctrl Pts',(-18.1484981337832,-53.679865365992,4.93408477881581));
#6432=CARTESIAN_POINT('Ctrl Pts',(-17.8756314312791,-53.179865365992,4.97252429631722));
#6433=CARTESIAN_POINT('Ctrl Pts',(-17.8756314312791,-53.679865365992,4.97252429631722));
#6434=CARTESIAN_POINT('Ctrl Pts',(-17.6135254723971,-53.179865365992,5.05690776184751));
#6435=CARTESIAN_POINT('Ctrl Pts',(-17.6135254723971,-53.679865365992,5.05690776184751));
#6436=CARTESIAN_POINT('Ctrl Pts',(-17.5025540160311,-53.179865365992,5.09263436685509));
#6437=CARTESIAN_POINT('Ctrl Pts',(-17.5025540160311,-53.679865365992,5.09263436685509));
#6438=CARTESIAN_POINT('',(-17.5025540160311,-53.179865365992,5.09263436685509));
#6439=CARTESIAN_POINT('',(-18.3058374537744,-53.179865365992,4.91191993609606));
#6440=CARTESIAN_POINT('Ctrl Pts',(-17.5025540160311,-53.179865365992,5.09263436685509));
#6441=CARTESIAN_POINT('Ctrl Pts',(-17.6135254723971,-53.179865365992,5.05690776184751));
#6442=CARTESIAN_POINT('Ctrl Pts',(-17.8756314312791,-53.179865365992,4.97252429631722));
#6443=CARTESIAN_POINT('Ctrl Pts',(-18.1484981337832,-53.179865365992,4.93408477881581));
#6444=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.179865365992,4.91191993609606));
#6445=CARTESIAN_POINT('',(-17.5025540160311,-53.679865365992,5.09263436685509));
#6446=CARTESIAN_POINT('',(-17.5025540160311,-53.179865365992,5.09263436685509));
#6447=CARTESIAN_POINT('',(-18.3058374537744,-53.679865365992,4.91191993609606));
#6448=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.679865365992,4.91191993609606));
#6449=CARTESIAN_POINT('Ctrl Pts',(-18.1484981337832,-53.679865365992,4.93408477881581));
#6450=CARTESIAN_POINT('Ctrl Pts',(-17.8756314312791,-53.679865365992,4.97252429631722));
#6451=CARTESIAN_POINT('Ctrl Pts',(-17.6135254723971,-53.679865365992,5.05690776184751));
#6452=CARTESIAN_POINT('Ctrl Pts',(-17.5025540160311,-53.679865365992,5.09263436685509));
#6453=CARTESIAN_POINT('',(-18.3058374537744,-53.179865365992,4.91191993609606));
#6454=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.179865365992,4.81626984584009));
#6455=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.679865365992,4.81626984584009));
#6456=CARTESIAN_POINT('Ctrl Pts',(-18.6784011754689,-53.179865365992,4.83458872366526));
#6457=CARTESIAN_POINT('Ctrl Pts',(-18.6784011754689,-53.679865365992,4.83458872366526));
#6458=CARTESIAN_POINT('Ctrl Pts',(-18.5385952000105,-53.179865365992,4.88000182422039));
#6459=CARTESIAN_POINT('Ctrl Pts',(-18.5385952000105,-53.679865365992,4.88000182422039));
#6460=CARTESIAN_POINT('Ctrl Pts',(-18.3928133609369,-53.179865365992,4.8999929142023));
#6461=CARTESIAN_POINT('Ctrl Pts',(-18.3928133609369,-53.679865365992,4.8999929142023));
#6462=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.179865365992,4.91191993609606));
#6463=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.679865365992,4.91191993609606));
#6464=CARTESIAN_POINT('',(-18.7347965449183,-53.179865365992,4.81626984584009));
#6465=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.179865365992,4.91191993609606));
#6466=CARTESIAN_POINT('Ctrl Pts',(-18.3928133609369,-53.179865365992,4.8999929142023));
#6467=CARTESIAN_POINT('Ctrl Pts',(-18.5385952000105,-53.179865365992,4.88000182422039));
#6468=CARTESIAN_POINT('Ctrl Pts',(-18.6784011754689,-53.179865365992,4.83458872366526));
#6469=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.179865365992,4.81626984584009));
#6470=CARTESIAN_POINT('',(-18.7347965449183,-53.679865365992,4.81626984584009));
#6471=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.679865365992,4.81626984584009));
#6472=CARTESIAN_POINT('Ctrl Pts',(-18.6784011754689,-53.679865365992,4.83458872366526));
#6473=CARTESIAN_POINT('Ctrl Pts',(-18.5385952000105,-53.679865365992,4.88000182422039));
#6474=CARTESIAN_POINT('Ctrl Pts',(-18.3928133609369,-53.679865365992,4.8999929142023));
#6475=CARTESIAN_POINT('Ctrl Pts',(-18.3058374537744,-53.679865365992,4.91191993609606));
#6476=CARTESIAN_POINT('',(-18.7347965449183,-53.179865365992,4.81626984584009));
#6477=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.179865365992,4.65645470819182));
#6478=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.679865365992,4.65645470819182));
#6479=CARTESIAN_POINT('Ctrl Pts',(-18.9037314272222,-53.179865365992,4.68979412982691));
#6480=CARTESIAN_POINT('Ctrl Pts',(-18.9037314272222,-53.679865365992,4.68979412982691));
#6481=CARTESIAN_POINT('Ctrl Pts',(-18.8511079008947,-53.179865365992,4.75791853410245));
#6482=CARTESIAN_POINT('Ctrl Pts',(-18.8511079008947,-53.679865365992,4.75791853410245));
#6483=CARTESIAN_POINT('Ctrl Pts',(-18.7741116027549,-53.179865365992,4.7965461896541));
#6484=CARTESIAN_POINT('Ctrl Pts',(-18.7741116027549,-53.679865365992,4.7965461896541));
#6485=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.179865365992,4.81626984584009));
#6486=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.679865365992,4.81626984584009));
#6487=CARTESIAN_POINT('',(-18.929484869778,-53.179865365992,4.65645470819182));
#6488=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.179865365992,4.81626984584009));
#6489=CARTESIAN_POINT('Ctrl Pts',(-18.7741116027549,-53.179865365992,4.7965461896541));
#6490=CARTESIAN_POINT('Ctrl Pts',(-18.8511079008947,-53.179865365992,4.75791853410245));
#6491=CARTESIAN_POINT('Ctrl Pts',(-18.9037314272222,-53.179865365992,4.68979412982691));
#6492=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.179865365992,4.65645470819182));
#6493=CARTESIAN_POINT('',(-18.929484869778,-53.679865365992,4.65645470819182));
#6494=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.679865365992,4.65645470819182));
#6495=CARTESIAN_POINT('Ctrl Pts',(-18.9037314272222,-53.679865365992,4.68979412982691));
#6496=CARTESIAN_POINT('Ctrl Pts',(-18.8511079008947,-53.679865365992,4.75791853410245));
#6497=CARTESIAN_POINT('Ctrl Pts',(-18.7741116027549,-53.679865365992,4.7965461896541));
#6498=CARTESIAN_POINT('Ctrl Pts',(-18.7347965449183,-53.679865365992,4.81626984584009));
#6499=CARTESIAN_POINT('',(-18.929484869778,-53.179865365992,4.65645470819182));
#6500=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.179865365992,4.42494509015702));
#6501=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.679865365992,4.42494509015702));
#6502=CARTESIAN_POINT('Ctrl Pts',(-18.9960987098644,-53.179865365992,4.46649786108055));
#6503=CARTESIAN_POINT('Ctrl Pts',(-18.9960987098644,-53.679865365992,4.46649786108055));
#6504=CARTESIAN_POINT('Ctrl Pts',(-18.9901674077983,-53.179865365992,4.54888484887004));
#6505=CARTESIAN_POINT('Ctrl Pts',(-18.9901674077983,-53.679865365992,4.54888484887004));
#6506=CARTESIAN_POINT('Ctrl Pts',(-18.949594427418,-53.179865365992,4.62080718327054));
#6507=CARTESIAN_POINT('Ctrl Pts',(-18.949594427418,-53.679865365992,4.62080718327054));
#6508=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.179865365992,4.65645470819182));
#6509=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.679865365992,4.65645470819182));
#6510=CARTESIAN_POINT('',(-18.9990902263969,-53.179865365992,4.42494509015702));
#6511=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.179865365992,4.65645470819182));
#6512=CARTESIAN_POINT('Ctrl Pts',(-18.949594427418,-53.179865365992,4.62080718327054));
#6513=CARTESIAN_POINT('Ctrl Pts',(-18.9901674077983,-53.179865365992,4.54888484887004));
#6514=CARTESIAN_POINT('Ctrl Pts',(-18.9960987098644,-53.179865365992,4.46649786108055));
#6515=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.179865365992,4.42494509015702));
#6516=CARTESIAN_POINT('',(-18.9990902263969,-53.679865365992,4.42494509015702));
#6517=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.679865365992,4.42494509015702));
#6518=CARTESIAN_POINT('Ctrl Pts',(-18.9960987098644,-53.679865365992,4.46649786108055));
#6519=CARTESIAN_POINT('Ctrl Pts',(-18.9901674077983,-53.679865365992,4.54888484887004));
#6520=CARTESIAN_POINT('Ctrl Pts',(-18.949594427418,-53.679865365992,4.62080718327054));
#6521=CARTESIAN_POINT('Ctrl Pts',(-18.929484869778,-53.679865365992,4.65645470819182));
#6522=CARTESIAN_POINT('',(-18.9990902263969,-53.179865365992,4.42494509015702));
#6523=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.179865365992,4.09664407814455));
#6524=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.679865365992,4.09664407814455));
#6525=CARTESIAN_POINT('Ctrl Pts',(-18.8964156029785,-53.179865365992,4.14325378106826));
#6526=CARTESIAN_POINT('Ctrl Pts',(-18.8964156029785,-53.679865365992,4.14325378106826));
#6527=CARTESIAN_POINT('Ctrl Pts',(-18.981720469911,-53.179865365992,4.23638102455284));
#6528=CARTESIAN_POINT('Ctrl Pts',(-18.981720469911,-53.679865365992,4.23638102455284));
#6529=CARTESIAN_POINT('Ctrl Pts',(-18.9933041287822,-53.179865365992,4.36213188427881));
#6530=CARTESIAN_POINT('Ctrl Pts',(-18.9933041287822,-53.679865365992,4.36213188427881));
#6531=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.179865365992,4.42494509015702));
#6532=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.679865365992,4.42494509015702));
#6533=CARTESIAN_POINT('',(-18.8537209589957,-53.179865365992,4.09664407814455));
#6534=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.179865365992,4.42494509015702));
#6535=CARTESIAN_POINT('Ctrl Pts',(-18.9933041287822,-53.179865365992,4.36213188427881));
#6536=CARTESIAN_POINT('Ctrl Pts',(-18.981720469911,-53.179865365992,4.23638102455284));
#6537=CARTESIAN_POINT('Ctrl Pts',(-18.8964156029785,-53.179865365992,4.14325378106826));
#6538=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.179865365992,4.09664407814455));
#6539=CARTESIAN_POINT('',(-18.8537209589957,-53.679865365992,4.09664407814455));
#6540=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.679865365992,4.09664407814455));
#6541=CARTESIAN_POINT('Ctrl Pts',(-18.8964156029785,-53.679865365992,4.14325378106826));
#6542=CARTESIAN_POINT('Ctrl Pts',(-18.981720469911,-53.679865365992,4.23638102455284));
#6543=CARTESIAN_POINT('Ctrl Pts',(-18.9933041287822,-53.679865365992,4.36213188427881));
#6544=CARTESIAN_POINT('Ctrl Pts',(-18.9990902263969,-53.679865365992,4.42494509015702));
#6545=CARTESIAN_POINT('',(-18.8537209589957,-53.179865365992,4.09664407814455));
#6546=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.179865365992,3.963054055088));
#6547=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.679865365992,3.963054055088));
#6548=CARTESIAN_POINT('Ctrl Pts',(-18.5046118731631,-53.179865365992,3.96652036570766));
#6549=CARTESIAN_POINT('Ctrl Pts',(-18.5046118731631,-53.679865365992,3.96652036570766));
#6550=CARTESIAN_POINT('Ctrl Pts',(-18.6623426343108,-53.179865365992,3.97282958478416));
#6551=CARTESIAN_POINT('Ctrl Pts',(-18.6623426343108,-53.679865365992,3.97282958478416));
#6552=CARTESIAN_POINT('Ctrl Pts',(-18.7942734637853,-53.179865365992,4.05818381095843));
#6553=CARTESIAN_POINT('Ctrl Pts',(-18.7942734637853,-53.679865365992,4.05818381095843));
#6554=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.179865365992,4.09664407814455));
#6555=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.679865365992,4.09664407814455));
#6556=CARTESIAN_POINT('',(-18.4179539515117,-53.179865365992,3.963054055088));
#6557=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.179865365992,4.09664407814455));
#6558=CARTESIAN_POINT('Ctrl Pts',(-18.7942734637853,-53.179865365992,4.05818381095843));
#6559=CARTESIAN_POINT('Ctrl Pts',(-18.6623426343108,-53.179865365992,3.97282958478416));
#6560=CARTESIAN_POINT('Ctrl Pts',(-18.5046118731631,-53.179865365992,3.96652036570766));
#6561=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.179865365992,3.963054055088));
#6562=CARTESIAN_POINT('',(-18.4179539515117,-53.679865365992,3.963054055088));
#6563=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.679865365992,3.963054055088));
#6564=CARTESIAN_POINT('Ctrl Pts',(-18.5046118731631,-53.679865365992,3.96652036570766));
#6565=CARTESIAN_POINT('Ctrl Pts',(-18.6623426343108,-53.679865365992,3.97282958478416));
#6566=CARTESIAN_POINT('Ctrl Pts',(-18.7942734637853,-53.679865365992,4.05818381095843));
#6567=CARTESIAN_POINT('Ctrl Pts',(-18.8537209589957,-53.679865365992,4.09664407814455));
#6568=CARTESIAN_POINT('',(-18.4179539515117,-53.179865365992,3.963054055088));
#6569=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.179865365992,4.08280136905297));
#6570=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.679865365992,4.08280136905297));
#6571=CARTESIAN_POINT('Ctrl Pts',(-17.9903001552246,-53.179865365992,4.04746593002637));
#6572=CARTESIAN_POINT('Ctrl Pts',(-17.9903001552246,-53.679865365992,4.04746593002637));
#6573=CARTESIAN_POINT('Ctrl Pts',(-18.1508574905441,-53.179865365992,3.97459039724186));
#6574=CARTESIAN_POINT('Ctrl Pts',(-18.1508574905441,-53.679865365992,3.97459039724186));
#6575=CARTESIAN_POINT('Ctrl Pts',(-18.3271441042001,-53.179865365992,3.96697628403005));
#6576=CARTESIAN_POINT('Ctrl Pts',(-18.3271441042001,-53.679865365992,3.96697628403005));
#6577=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.179865365992,3.963054055088));
#6578=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.679865365992,3.963054055088));
#6579=CARTESIAN_POINT('',(-17.9124501043819,-53.179865365992,4.08280136905297));
#6580=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.179865365992,3.963054055088));
#6581=CARTESIAN_POINT('Ctrl Pts',(-18.3271441042001,-53.179865365992,3.96697628403005));
#6582=CARTESIAN_POINT('Ctrl Pts',(-18.1508574905441,-53.179865365992,3.97459039724186));
#6583=CARTESIAN_POINT('Ctrl Pts',(-17.9903001552246,-53.179865365992,4.04746593002637));
#6584=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.179865365992,4.08280136905297));
#6585=CARTESIAN_POINT('',(-17.9124501043819,-53.679865365992,4.08280136905297));
#6586=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.679865365992,4.08280136905297));
#6587=CARTESIAN_POINT('Ctrl Pts',(-17.9903001552246,-53.679865365992,4.04746593002637));
#6588=CARTESIAN_POINT('Ctrl Pts',(-18.1508574905441,-53.679865365992,3.97459039724186));
#6589=CARTESIAN_POINT('Ctrl Pts',(-18.3271441042001,-53.679865365992,3.96697628403005));
#6590=CARTESIAN_POINT('Ctrl Pts',(-18.4179539515117,-53.679865365992,3.963054055088));
#6591=CARTESIAN_POINT('',(-17.9124501043819,-53.179865365992,4.08280136905297));
#6592=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.179865365992,4.42190787134159));
#6593=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.679865365992,4.42190787134159));
#6594=CARTESIAN_POINT('Ctrl Pts',(-17.626155885552,-53.179865365992,4.35372340140058));
#6595=CARTESIAN_POINT('Ctrl Pts',(-17.626155885552,-53.679865365992,4.35372340140058));
#6596=CARTESIAN_POINT('Ctrl Pts',(-17.7083772668984,-53.179865365992,4.21510105843672));
#6597=CARTESIAN_POINT('Ctrl Pts',(-17.7083772668984,-53.679865365992,4.21510105843672));
#6598=CARTESIAN_POINT('Ctrl Pts',(-17.843692610058,-53.179865365992,4.12737660627729));
#6599=CARTESIAN_POINT('Ctrl Pts',(-17.843692610058,-53.679865365992,4.12737660627729));
#6600=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.179865365992,4.08280136905297));
#6601=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.679865365992,4.08280136905297));
#6602=CARTESIAN_POINT('',(-17.585713477892,-53.179865365992,4.42190787134159));
#6603=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.179865365992,4.08280136905297));
#6604=CARTESIAN_POINT('Ctrl Pts',(-17.843692610058,-53.179865365992,4.12737660627729));
#6605=CARTESIAN_POINT('Ctrl Pts',(-17.7083772668984,-53.179865365992,4.21510105843672));
#6606=CARTESIAN_POINT('Ctrl Pts',(-17.626155885552,-53.179865365992,4.35372340140058));
#6607=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.179865365992,4.42190787134159));
#6608=CARTESIAN_POINT('',(-17.585713477892,-53.679865365992,4.42190787134159));
#6609=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.679865365992,4.42190787134159));
#6610=CARTESIAN_POINT('Ctrl Pts',(-17.626155885552,-53.679865365992,4.35372340140058));
#6611=CARTESIAN_POINT('Ctrl Pts',(-17.7083772668984,-53.679865365992,4.21510105843672));
#6612=CARTESIAN_POINT('Ctrl Pts',(-17.843692610058,-53.679865365992,4.12737660627729));
#6613=CARTESIAN_POINT('Ctrl Pts',(-17.9124501043819,-53.679865365992,4.08280136905297));
#6614=CARTESIAN_POINT('',(-17.585713477892,-53.179865365992,4.42190787134159));
#6615=CARTESIAN_POINT('Ctrl Pts',(-17.5036362910124,-53.179865365992,4.91255589395159));
#6616=CARTESIAN_POINT('Ctrl Pts',(-17.5036362910124,-53.679865365992,4.91255589395159));
#6617=CARTESIAN_POINT('Ctrl Pts',(-17.505156284096,-53.179865365992,4.81652167900377));
#6618=CARTESIAN_POINT('Ctrl Pts',(-17.505156284096,-53.679865365992,4.81652167900377));
#6619=CARTESIAN_POINT('Ctrl Pts',(-17.5078163754767,-53.179865365992,4.64845526463067));
#6620=CARTESIAN_POINT('Ctrl Pts',(-17.5078163754767,-53.679865365992,4.64845526463067));
#6621=CARTESIAN_POINT('Ctrl Pts',(-17.5623434986695,-53.179865365992,4.48987455700675));
#6622=CARTESIAN_POINT('Ctrl Pts',(-17.5623434986695,-53.679865365992,4.48987455700675));
#6623=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.179865365992,4.42190787134159));
#6624=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.679865365992,4.42190787134159));
#6625=CARTESIAN_POINT('',(-17.5036362910124,-53.179865365992,4.91255589395159));
#6626=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.179865365992,4.42190787134159));
#6627=CARTESIAN_POINT('Ctrl Pts',(-17.5623434986695,-53.179865365992,4.48987455700675));
#6628=CARTESIAN_POINT('Ctrl Pts',(-17.5078163754767,-53.179865365992,4.64845526463067));
#6629=CARTESIAN_POINT('Ctrl Pts',(-17.505156284096,-53.179865365992,4.81652167900377));
#6630=CARTESIAN_POINT('Ctrl Pts',(-17.5036362910124,-53.179865365992,4.91255589395159));
#6631=CARTESIAN_POINT('',(-17.5036362910124,-53.679865365992,4.91255589395159));
#6632=CARTESIAN_POINT('Ctrl Pts',(-17.5036362910124,-53.679865365992,4.91255589395159));
#6633=CARTESIAN_POINT('Ctrl Pts',(-17.505156284096,-53.679865365992,4.81652167900377));
#6634=CARTESIAN_POINT('Ctrl Pts',(-17.5078163754767,-53.679865365992,4.64845526463067));
#6635=CARTESIAN_POINT('Ctrl Pts',(-17.5623434986695,-53.679865365992,4.48987455700675));
#6636=CARTESIAN_POINT('Ctrl Pts',(-17.585713477892,-53.679865365992,4.42190787134159));
#6637=CARTESIAN_POINT('',(-17.5036362910124,-53.179865365992,4.91255589395159));
#6638=CARTESIAN_POINT('Origin',(-17.5025540160311,-53.179865365992,5.09263436685509));
#6639=CARTESIAN_POINT('',(-17.5185197108627,-53.179865365992,2.43612100946596));
#6640=CARTESIAN_POINT('',(-17.5036362910124,-53.679865365992,4.91255589395159));
#6641=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.179865365992,3.67129556877814));
#6642=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.679865365992,3.67129556877814));
#6643=CARTESIAN_POINT('Ctrl Pts',(-17.9083660412272,-53.179865365992,3.71070532952565));
#6644=CARTESIAN_POINT('Ctrl Pts',(-17.9083660412272,-53.679865365992,3.71070532952565));
#6645=CARTESIAN_POINT('Ctrl Pts',(-17.7180955229753,-53.179865365992,3.79551639567177));
#6646=CARTESIAN_POINT('Ctrl Pts',(-17.7180955229753,-53.679865365992,3.79551639567177));
#6647=CARTESIAN_POINT('Ctrl Pts',(-17.5553439244099,-53.179865365992,3.92580159601428));
#6648=CARTESIAN_POINT('Ctrl Pts',(-17.5553439244099,-53.679865365992,3.92580159601428));
#6649=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.179865365992,3.995546246547));
#6650=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.679865365992,3.995546246547));
#6651=CARTESIAN_POINT('',(-17.4682192676127,-53.679865365992,3.995546246547));
#6652=CARTESIAN_POINT('',(-17.4682192676127,-53.179865365992,3.995546246547));
#6653=CARTESIAN_POINT('',(-17.9967803949472,-53.679865365992,3.67129556877814));
#6654=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.679865365992,3.67129556877814));
#6655=CARTESIAN_POINT('Ctrl Pts',(-17.9083660412272,-53.679865365992,3.71070532952565));
#6656=CARTESIAN_POINT('Ctrl Pts',(-17.7180955229753,-53.679865365992,3.79551639567177));
#6657=CARTESIAN_POINT('Ctrl Pts',(-17.5553439244099,-53.679865365992,3.92580159601428));
#6658=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.679865365992,3.995546246547));
#6659=CARTESIAN_POINT('',(-17.9967803949472,-53.179865365992,3.67129556877814));
#6660=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.179865365992,3.57904615436238));
#6661=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.679865365992,3.57904615436238));
#6662=CARTESIAN_POINT('Ctrl Pts',(-18.4435795459125,-53.179865365992,3.58214964469488));
#6663=CARTESIAN_POINT('Ctrl Pts',(-18.4435795459125,-53.679865365992,3.58214964469488));
#6664=CARTESIAN_POINT('Ctrl Pts',(-18.259059311806,-53.179865365992,3.58824105749433));
#6665=CARTESIAN_POINT('Ctrl Pts',(-18.259059311806,-53.679865365992,3.58824105749433));
#6666=CARTESIAN_POINT('Ctrl Pts',(-18.0830938799431,-53.179865365992,3.64396312284845));
#6667=CARTESIAN_POINT('Ctrl Pts',(-18.0830938799431,-53.679865365992,3.64396312284845));
#6668=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.179865365992,3.67129556877814));
#6669=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.679865365992,3.67129556877814));
#6670=CARTESIAN_POINT('',(-18.5375900458662,-53.679865365992,3.57904615436238));
#6671=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.679865365992,3.57904615436238));
#6672=CARTESIAN_POINT('Ctrl Pts',(-18.4435795459125,-53.679865365992,3.58214964469488));
#6673=CARTESIAN_POINT('Ctrl Pts',(-18.259059311806,-53.679865365992,3.58824105749433));
#6674=CARTESIAN_POINT('Ctrl Pts',(-18.0830938799431,-53.679865365992,3.64396312284845));
#6675=CARTESIAN_POINT('Ctrl Pts',(-17.9967803949472,-53.679865365992,3.67129556877814));
#6676=CARTESIAN_POINT('',(-18.5375900458662,-53.179865365992,3.57904615436238));
#6677=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.179865365992,3.81537652122272));
#6678=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.679865365992,3.81537652122272));
#6679=CARTESIAN_POINT('Ctrl Pts',(-19.1698346772575,-53.179865365992,3.74726683523292));
#6680=CARTESIAN_POINT('Ctrl Pts',(-19.1698346772575,-53.679865365992,3.74726683523292));
#6681=CARTESIAN_POINT('Ctrl Pts',(-18.9497452282753,-53.179865365992,3.59784034645476));
#6682=CARTESIAN_POINT('Ctrl Pts',(-18.9497452282753,-53.679865365992,3.59784034645476));
#6683=CARTESIAN_POINT('Ctrl Pts',(-18.6828386529162,-53.179865365992,3.58566946116175));
#6684=CARTESIAN_POINT('Ctrl Pts',(-18.6828386529162,-53.679865365992,3.58566946116175));
#6685=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.179865365992,3.57904615436238));
#6686=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.679865365992,3.57904615436238));
#6687=CARTESIAN_POINT('',(-19.2701530571004,-53.679865365992,3.81537652122272));
#6688=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.679865365992,3.81537652122272));
#6689=CARTESIAN_POINT('Ctrl Pts',(-19.1698346772575,-53.679865365992,3.74726683523292));
#6690=CARTESIAN_POINT('Ctrl Pts',(-18.9497452282753,-53.679865365992,3.59784034645476));
#6691=CARTESIAN_POINT('Ctrl Pts',(-18.6828386529162,-53.679865365992,3.58566946116175));
#6692=CARTESIAN_POINT('Ctrl Pts',(-18.5375900458662,-53.679865365992,3.57904615436238));
#6693=CARTESIAN_POINT('',(-19.2701530571004,-53.179865365992,3.81537652122272));
#6694=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.179865365992,4.41445146565348));
#6695=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.679865365992,4.41445146565348));
#6696=CARTESIAN_POINT('Ctrl Pts',(-19.5135756389659,-53.179865365992,4.29926364436147));
#6697=CARTESIAN_POINT('Ctrl Pts',(-19.5135756389659,-53.679865365992,4.29926364436147));
#6698=CARTESIAN_POINT('Ctrl Pts',(-19.4948805990596,-53.179865365992,4.07167229709018));
#6699=CARTESIAN_POINT('Ctrl Pts',(-19.4948805990596,-53.679865365992,4.07167229709018));
#6700=CARTESIAN_POINT('Ctrl Pts',(-19.344448789443,-53.179865365992,3.90010882505328));
#6701=CARTESIAN_POINT('Ctrl Pts',(-19.344448789443,-53.679865365992,3.90010882505328));
#6702=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.179865365992,3.81537652122272));
#6703=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.679865365992,3.81537652122272));
#6704=CARTESIAN_POINT('',(-19.5230375141276,-53.679865365992,4.41445146565348));
#6705=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.679865365992,4.41445146565348));
#6706=CARTESIAN_POINT('Ctrl Pts',(-19.5135756389659,-53.679865365992,4.29926364436147));
#6707=CARTESIAN_POINT('Ctrl Pts',(-19.4948805990596,-53.679865365992,4.07167229709018));
#6708=CARTESIAN_POINT('Ctrl Pts',(-19.344448789443,-53.679865365992,3.90010882505328));
#6709=CARTESIAN_POINT('Ctrl Pts',(-19.2701530571004,-53.679865365992,3.81537652122272));
#6710=CARTESIAN_POINT('',(-19.5230375141276,-53.179865365992,4.41445146565348));
#6711=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.179865365992,4.80131920274653));
#6712=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.679865365992,4.80131920274653));
#6713=CARTESIAN_POINT('Ctrl Pts',(-19.4534263398067,-53.179865365992,4.74108570132111));
#6714=CARTESIAN_POINT('Ctrl Pts',(-19.4534263398067,-53.679865365992,4.74108570132111));
#6715=CARTESIAN_POINT('Ctrl Pts',(-19.5108614835729,-53.179865365992,4.61846682256525));
#6716=CARTESIAN_POINT('Ctrl Pts',(-19.5108614835729,-53.679865365992,4.61846682256525));
#6717=CARTESIAN_POINT('Ctrl Pts',(-19.5189316288004,-53.179865365992,4.48324758576116));
#6718=CARTESIAN_POINT('Ctrl Pts',(-19.5189316288004,-53.679865365992,4.48324758576116));
#6719=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.179865365992,4.41445146565348));
#6720=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.679865365992,4.41445146565348));
#6721=CARTESIAN_POINT('',(-19.4252127413053,-53.679865365992,4.80131920274653));
#6722=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.679865365992,4.80131920274653));
#6723=CARTESIAN_POINT('Ctrl Pts',(-19.4534263398067,-53.679865365992,4.74108570132111));
#6724=CARTESIAN_POINT('Ctrl Pts',(-19.5108614835729,-53.679865365992,4.61846682256525));
#6725=CARTESIAN_POINT('Ctrl Pts',(-19.5189316288004,-53.679865365992,4.48324758576116));
#6726=CARTESIAN_POINT('Ctrl Pts',(-19.5230375141276,-53.679865365992,4.41445146565348));
#6727=CARTESIAN_POINT('',(-19.4252127413053,-53.179865365992,4.80131920274653));
#6728=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.179865365992,5.08355424888615));
#6729=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.679865365992,5.08355424888615));
#6730=CARTESIAN_POINT('Ctrl Pts',(-19.2197458559826,-53.179865365992,5.04488398839039));
#6731=CARTESIAN_POINT('Ctrl Pts',(-19.2197458559826,-53.679865365992,5.04488398839039));
#6732=CARTESIAN_POINT('Ctrl Pts',(-19.3216563003775,-53.179865365992,4.96604354372531));
#6733=CARTESIAN_POINT('Ctrl Pts',(-19.3216563003775,-53.679865365992,4.96604354372531));
#6734=CARTESIAN_POINT('Ctrl Pts',(-19.3902588775645,-53.179865365992,4.8569193384579));
#6735=CARTESIAN_POINT('Ctrl Pts',(-19.3902588775645,-53.679865365992,4.8569193384579));
#6736=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.179865365992,4.80131920274653));
#6737=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.679865365992,4.80131920274653));
#6738=CARTESIAN_POINT('',(-19.1697600466847,-53.679865365992,5.08355424888615));
#6739=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.679865365992,5.08355424888615));
#6740=CARTESIAN_POINT('Ctrl Pts',(-19.2197458559826,-53.679865365992,5.04488398839039));
#6741=CARTESIAN_POINT('Ctrl Pts',(-19.3216563003775,-53.679865365992,4.96604354372531));
#6742=CARTESIAN_POINT('Ctrl Pts',(-19.3902588775645,-53.679865365992,4.8569193384579));
#6743=CARTESIAN_POINT('Ctrl Pts',(-19.4252127413053,-53.679865365992,4.80131920274653));
#6744=CARTESIAN_POINT('',(-19.1697600466847,-53.179865365992,5.08355424888615));
#6745=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.179865365992,5.24240219533447));
#6746=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.679865365992,5.24240219533447));
#6747=CARTESIAN_POINT('Ctrl Pts',(-18.8776384238003,-53.179865365992,5.22274488689186));
#6748=CARTESIAN_POINT('Ctrl Pts',(-18.8776384238003,-53.679865365992,5.22274488689186));
#6749=CARTESIAN_POINT('Ctrl Pts',(-19.0026016258659,-53.179865365992,5.18408885722665));
#6750=CARTESIAN_POINT('Ctrl Pts',(-19.0026016258659,-53.679865365992,5.18408885722665));
#6751=CARTESIAN_POINT('Ctrl Pts',(-19.1146770537267,-53.179865365992,5.11668298446601));
#6752=CARTESIAN_POINT('Ctrl Pts',(-19.1146770537267,-53.679865365992,5.11668298446601));
#6753=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.179865365992,5.08355424888615));
#6754=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.679865365992,5.08355424888615));
#6755=CARTESIAN_POINT('',(-18.8140923165499,-53.679865365992,5.24240219533447));
#6756=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.679865365992,5.24240219533447));
#6757=CARTESIAN_POINT('Ctrl Pts',(-18.8776384238003,-53.679865365992,5.22274488689186));
#6758=CARTESIAN_POINT('Ctrl Pts',(-19.0026016258659,-53.679865365992,5.18408885722665));
#6759=CARTESIAN_POINT('Ctrl Pts',(-19.1146770537267,-53.679865365992,5.11668298446601));
#6760=CARTESIAN_POINT('Ctrl Pts',(-19.1697600466847,-53.679865365992,5.08355424888615));
#6761=CARTESIAN_POINT('',(-18.8140923165499,-53.179865365992,5.24240219533447));
#6762=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.179865365992,5.31344671929976));
#6763=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.679865365992,5.31344671929976));
#6764=CARTESIAN_POINT('Ctrl Pts',(-18.4624658855733,-53.179865365992,5.30346189549737));
#6765=CARTESIAN_POINT('Ctrl Pts',(-18.4624658855733,-53.679865365992,5.30346189549737));
#6766=CARTESIAN_POINT('Ctrl Pts',(-18.6092254882953,-53.179865365992,5.28629706797279));
#6767=CARTESIAN_POINT('Ctrl Pts',(-18.6092254882953,-53.679865365992,5.28629706797279));
#6768=CARTESIAN_POINT('Ctrl Pts',(-18.7536710889591,-53.179865365992,5.25534807882544));
#6769=CARTESIAN_POINT('Ctrl Pts',(-18.7536710889591,-53.679865365992,5.25534807882544));
#6770=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.179865365992,5.24240219533447));
#6771=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.679865365992,5.24240219533447));
#6772=CARTESIAN_POINT('',(-18.3770954516211,-53.679865365992,5.31344671929976));
#6773=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.679865365992,5.31344671929976));
#6774=CARTESIAN_POINT('Ctrl Pts',(-18.4624658855733,-53.679865365992,5.30346189549737));
#6775=CARTESIAN_POINT('Ctrl Pts',(-18.6092254882953,-53.679865365992,5.28629706797279));
#6776=CARTESIAN_POINT('Ctrl Pts',(-18.7536710889591,-53.679865365992,5.25534807882544));
#6777=CARTESIAN_POINT('Ctrl Pts',(-18.8140923165499,-53.679865365992,5.24240219533447));
#6778=CARTESIAN_POINT('',(-18.3770954516211,-53.179865365992,5.31344671929976));
#6779=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.179865365992,5.47734798281611));
#6780=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.679865365992,5.47734798281611));
#6781=CARTESIAN_POINT('Ctrl Pts',(-17.6197595666939,-53.179865365992,5.44509356604418));
#6782=CARTESIAN_POINT('Ctrl Pts',(-17.6197595666939,-53.679865365992,5.44509356604418));
#6783=CARTESIAN_POINT('Ctrl Pts',(-17.9075380623137,-53.179865365992,5.36743035322383));
#6784=CARTESIAN_POINT('Ctrl Pts',(-17.9075380623137,-53.679865365992,5.36743035322383));
#6785=CARTESIAN_POINT('Ctrl Pts',(-18.2038469939087,-53.179865365992,5.33336458563949));
#6786=CARTESIAN_POINT('Ctrl Pts',(-18.2038469939087,-53.679865365992,5.33336458563949));
#6787=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.179865365992,5.31344671929976));
#6788=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.679865365992,5.31344671929976));
#6789=CARTESIAN_POINT('',(-17.5002418800229,-53.679865365992,5.47734798281611));
#6790=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.679865365992,5.47734798281611));
#6791=CARTESIAN_POINT('Ctrl Pts',(-17.6197595666939,-53.679865365992,5.44509356604418));
#6792=CARTESIAN_POINT('Ctrl Pts',(-17.9075380623137,-53.679865365992,5.36743035322383));
#6793=CARTESIAN_POINT('Ctrl Pts',(-18.2038469939087,-53.679865365992,5.33336458563949));
#6794=CARTESIAN_POINT('Ctrl Pts',(-18.3770954516211,-53.679865365992,5.31344671929976));
#6795=CARTESIAN_POINT('',(-17.5002418800229,-53.179865365992,5.47734798281611));
#6796=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.179865365992,5.60556925687899));
#6797=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.679865365992,5.60556925687899));
#6798=CARTESIAN_POINT('Ctrl Pts',(-17.4970345654438,-53.179865365992,5.59033605225441));
#6799=CARTESIAN_POINT('Ctrl Pts',(-17.4970345654438,-53.679865365992,5.59033605225441));
#6800=CARTESIAN_POINT('Ctrl Pts',(-17.4978541473476,-53.179865365992,5.54758665331348));
#6801=CARTESIAN_POINT('Ctrl Pts',(-17.4978541473476,-53.679865365992,5.54758665331348));
#6802=CARTESIAN_POINT('Ctrl Pts',(-17.4993068375188,-53.179865365992,5.50485363410765));
#6803=CARTESIAN_POINT('Ctrl Pts',(-17.4993068375188,-53.679865365992,5.50485363410765));
#6804=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.179865365992,5.47734798281611));
#6805=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.679865365992,5.47734798281611));
#6806=CARTESIAN_POINT('',(-17.4967425178558,-53.679865365992,5.60556925687899));
#6807=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.679865365992,5.60556925687899));
#6808=CARTESIAN_POINT('Ctrl Pts',(-17.4970345654438,-53.679865365992,5.59033605225441));
#6809=CARTESIAN_POINT('Ctrl Pts',(-17.4978541473476,-53.679865365992,5.54758665331348));
#6810=CARTESIAN_POINT('Ctrl Pts',(-17.4993068375188,-53.679865365992,5.50485363410765));
#6811=CARTESIAN_POINT('Ctrl Pts',(-17.5002418800229,-53.679865365992,5.47734798281611));
#6812=CARTESIAN_POINT('',(-17.4967425178558,-53.179865365992,5.60556925687899));
#6813=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.179865365992,6.02931731588761));
#6814=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.679865365992,6.02931731588761));
#6815=CARTESIAN_POINT('Ctrl Pts',(-17.593273655748,-53.179865365992,5.975396585771));
#6816=CARTESIAN_POINT('Ctrl Pts',(-17.593273655748,-53.679865365992,5.975396585771));
#6817=CARTESIAN_POINT('Ctrl Pts',(-17.5006832297982,-53.179865365992,5.85082851673556));
#6818=CARTESIAN_POINT('Ctrl Pts',(-17.5006832297982,-53.679865365992,5.85082851673556));
#6819=CARTESIAN_POINT('Ctrl Pts',(-17.498168638955,-53.179865365992,5.69432717939977));
#6820=CARTESIAN_POINT('Ctrl Pts',(-17.498168638955,-53.679865365992,5.69432717939977));
#6821=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.179865365992,5.60556925687899));
#6822=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.679865365992,5.60556925687899));
#6823=CARTESIAN_POINT('',(-17.6333524930269,-53.679865365992,6.02931731588761));
#6824=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.679865365992,6.02931731588761));
#6825=CARTESIAN_POINT('Ctrl Pts',(-17.593273655748,-53.679865365992,5.975396585771));
#6826=CARTESIAN_POINT('Ctrl Pts',(-17.5006832297982,-53.679865365992,5.85082851673556));
#6827=CARTESIAN_POINT('Ctrl Pts',(-17.498168638955,-53.679865365992,5.69432717939977));
#6828=CARTESIAN_POINT('Ctrl Pts',(-17.4967425178558,-53.679865365992,5.60556925687899));
#6829=CARTESIAN_POINT('',(-17.6333524930269,-53.179865365992,6.02931731588761));
#6830=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.179865365992,6.19911533836006));
#6831=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.679865365992,6.19911533836006));
#6832=CARTESIAN_POINT('Ctrl Pts',(-18.0799373752263,-53.179865365992,6.19496919800943));
#6833=CARTESIAN_POINT('Ctrl Pts',(-18.0799373752263,-53.679865365992,6.19496919800943));
#6834=CARTESIAN_POINT('Ctrl Pts',(-17.8778445896253,-53.179865365992,6.18747116058281));
#6835=CARTESIAN_POINT('Ctrl Pts',(-17.8778445896253,-53.679865365992,6.18747116058281));
#6836=CARTESIAN_POINT('Ctrl Pts',(-17.7088840405669,-53.179865365992,6.07817617392611));
#6837=CARTESIAN_POINT('Ctrl Pts',(-17.7088840405669,-53.679865365992,6.07817617392611));
#6838=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.179865365992,6.02931731588761));
#6839=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.679865365992,6.02931731588761));
#6840=CARTESIAN_POINT('',(-18.1916872912747,-53.679865365992,6.19911533836006));
#6841=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.679865365992,6.19911533836006));
#6842=CARTESIAN_POINT('Ctrl Pts',(-18.0799373752263,-53.679865365992,6.19496919800943));
#6843=CARTESIAN_POINT('Ctrl Pts',(-17.8778445896253,-53.679865365992,6.18747116058281));
#6844=CARTESIAN_POINT('Ctrl Pts',(-17.7088840405669,-53.679865365992,6.07817617392611));
#6845=CARTESIAN_POINT('Ctrl Pts',(-17.6333524930269,-53.679865365992,6.02931731588761));
#6846=CARTESIAN_POINT('',(-18.1916872912747,-53.179865365992,6.19911533836006));
#6847=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.179865365992,6.0794172185496));
#6848=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.679865365992,6.0794172185496));
#6849=CARTESIAN_POINT('Ctrl Pts',(-18.635972972123,-53.179865365992,6.11464662814405));
#6850=CARTESIAN_POINT('Ctrl Pts',(-18.635972972123,-53.679865365992,6.11464662814405));
#6851=CARTESIAN_POINT('Ctrl Pts',(-18.4753742961506,-53.179865365992,6.19616693408705));
#6852=CARTESIAN_POINT('Ctrl Pts',(-18.4753742961506,-53.679865365992,6.19616693408705));
#6853=CARTESIAN_POINT('Ctrl Pts',(-18.2944335725293,-53.179865365992,6.19804747987701));
#6854=CARTESIAN_POINT('Ctrl Pts',(-18.2944335725293,-53.679865365992,6.19804747987701));
#6855=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.179865365992,6.19911533836006));
#6856=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.679865365992,6.19911533836006));
#6857=CARTESIAN_POINT('',(-18.7053764964439,-53.679865365992,6.0794172185496));
#6858=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.679865365992,6.0794172185496));
#6859=CARTESIAN_POINT('Ctrl Pts',(-18.635972972123,-53.679865365992,6.11464662814405));
#6860=CARTESIAN_POINT('Ctrl Pts',(-18.4753742961506,-53.679865365992,6.19616693408705));
#6861=CARTESIAN_POINT('Ctrl Pts',(-18.2944335725293,-53.679865365992,6.19804747987701));
#6862=CARTESIAN_POINT('Ctrl Pts',(-18.1916872912747,-53.679865365992,6.19911533836006));
#6863=CARTESIAN_POINT('',(-18.7053764964439,-53.179865365992,6.0794172185496));
#6864=CARTESIAN_POINT('Ctrl Pts',(-18.9507842825755,-53.179865365992,5.65250789160473));
#6865=CARTESIAN_POINT('Ctrl Pts',(-18.9507842825755,-53.679865365992,5.65250789160473));
#6866=CARTESIAN_POINT('Ctrl Pts',(-18.9244049061434,-53.179865365992,5.74395133954849));
#6867=CARTESIAN_POINT('Ctrl Pts',(-18.9244049061434,-53.679865365992,5.74395133954849));
#6868=CARTESIAN_POINT('Ctrl Pts',(-18.8773180803213,-53.179865365992,5.90717664235211));
#6869=CARTESIAN_POINT('Ctrl Pts',(-18.8773180803213,-53.679865365992,5.90717664235211));
#6870=CARTESIAN_POINT('Ctrl Pts',(-18.757895262004,-53.179865365992,6.02680712716127));
#6871=CARTESIAN_POINT('Ctrl Pts',(-18.757895262004,-53.679865365992,6.02680712716127));
#6872=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.179865365992,6.0794172185496));
#6873=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.679865365992,6.0794172185496));
#6874=CARTESIAN_POINT('',(-18.9507842825755,-53.679865365992,5.65250789160473));
#6875=CARTESIAN_POINT('Ctrl Pts',(-18.9507842825755,-53.679865365992,5.65250789160473));
#6876=CARTESIAN_POINT('Ctrl Pts',(-18.9244049061434,-53.679865365992,5.74395133954849));
#6877=CARTESIAN_POINT('Ctrl Pts',(-18.8773180803213,-53.679865365992,5.90717664235211));
#6878=CARTESIAN_POINT('Ctrl Pts',(-18.757895262004,-53.679865365992,6.02680712716127));
#6879=CARTESIAN_POINT('Ctrl Pts',(-18.7053764964439,-53.679865365992,6.0794172185496));
#6880=CARTESIAN_POINT('',(-18.9507842825755,-53.179865365992,5.65250789160473));
#6881=CARTESIAN_POINT('Origin',(-19.4306002866422,-53.179865365992,5.72087712234595));
#6882=CARTESIAN_POINT('',(-19.4306002866422,-53.679865365992,5.72087712234595));
#6883=CARTESIAN_POINT('',(-18.9507842825755,-53.679865365992,5.65250789160473));
#6884=CARTESIAN_POINT('',(-19.4306002866422,-53.179865365992,5.72087712234595));
#6885=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.179865365992,6.21616194667383));
#6886=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.679865365992,6.21616194667383));
#6887=CARTESIAN_POINT('Ctrl Pts',(-19.2596203448692,-53.179865365992,6.14552920702489));
#6888=CARTESIAN_POINT('Ctrl Pts',(-19.2596203448692,-53.679865365992,6.14552920702489));
#6889=CARTESIAN_POINT('Ctrl Pts',(-19.3618486122387,-53.179865365992,5.99368491207509));
#6890=CARTESIAN_POINT('Ctrl Pts',(-19.3618486122387,-53.679865365992,5.99368491207509));
#6891=CARTESIAN_POINT('Ctrl Pts',(-19.4066428086306,-53.179865365992,5.81594079432957));
#6892=CARTESIAN_POINT('Ctrl Pts',(-19.4066428086306,-53.679865365992,5.81594079432957));
#6893=CARTESIAN_POINT('Ctrl Pts',(-19.4306002866422,-53.179865365992,5.72087712234595));
#6894=CARTESIAN_POINT('Ctrl Pts',(-19.4306002866422,-53.679865365992,5.72087712234595));
#6895=CARTESIAN_POINT('',(-19.2120672735058,-53.679865365992,6.21616194667383));
#6896=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.679865365992,6.21616194667383));
#6897=CARTESIAN_POINT('Ctrl Pts',(-19.2596203448692,-53.679865365992,6.14552920702489));
#6898=CARTESIAN_POINT('Ctrl Pts',(-19.3618486122387,-53.679865365992,5.99368491207509));
#6899=CARTESIAN_POINT('Ctrl Pts',(-19.4066428086306,-53.679865365992,5.81594079432957));
#6900=CARTESIAN_POINT('Ctrl Pts',(-19.4306002866422,-53.679865365992,5.72087712234595));
#6901=CARTESIAN_POINT('',(-19.2120672735058,-53.179865365992,6.21616194667383));
#6902=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.179865365992,6.50550037388947));
#6903=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.679865365992,6.50550037388947));
#6904=CARTESIAN_POINT('Ctrl Pts',(-18.8640850204553,-53.179865365992,6.47062405448186));
#6905=CARTESIAN_POINT('Ctrl Pts',(-18.8640850204553,-53.679865365992,6.47062405448186));
#6906=CARTESIAN_POINT('Ctrl Pts',(-19.0298605487794,-53.179865365992,6.4046221710152));
#6907=CARTESIAN_POINT('Ctrl Pts',(-19.0298605487794,-53.679865365992,6.4046221710152));
#6908=CARTESIAN_POINT('Ctrl Pts',(-19.1536771147305,-53.179865365992,6.2765561085174));
#6909=CARTESIAN_POINT('Ctrl Pts',(-19.1536771147305,-53.679865365992,6.2765561085174));
#6910=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.179865365992,6.21616194667383));
#6911=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.679865365992,6.21616194667383));
#6912=CARTESIAN_POINT('',(-18.776486910036,-53.679865365992,6.50550037388947));
#6913=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.679865365992,6.50550037388947));
#6914=CARTESIAN_POINT('Ctrl Pts',(-18.8640850204553,-53.679865365992,6.47062405448186));
#6915=CARTESIAN_POINT('Ctrl Pts',(-19.0298605487794,-53.679865365992,6.4046221710152));
#6916=CARTESIAN_POINT('Ctrl Pts',(-19.1536771147305,-53.679865365992,6.2765561085174));
#6917=CARTESIAN_POINT('Ctrl Pts',(-19.2120672735058,-53.679865365992,6.21616194667383));
#6918=CARTESIAN_POINT('',(-18.776486910036,-53.179865365992,6.50550037388947));
#6919=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.179865365992,6.60523002714498));
#6920=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.679865365992,6.60523002714498));
#6921=CARTESIAN_POINT('Ctrl Pts',(-18.2362883123438,-53.179865365992,6.60263721562776));
#6922=CARTESIAN_POINT('Ctrl Pts',(-18.2362883123438,-53.679865365992,6.60263721562776));
#6923=CARTESIAN_POINT('Ctrl Pts',(-18.4601123887653,-53.179865365992,6.59771847568304));
#6924=CARTESIAN_POINT('Ctrl Pts',(-18.4601123887653,-53.679865365992,6.59771847568304));
#6925=CARTESIAN_POINT('Ctrl Pts',(-18.6749136626558,-53.179865365992,6.53510734738655));
#6926=CARTESIAN_POINT('Ctrl Pts',(-18.6749136626558,-53.679865365992,6.53510734738655));
#6927=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.179865365992,6.50550037388947));
#6928=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.679865365992,6.50550037388947));
#6929=CARTESIAN_POINT('',(-18.118304103034,-53.679865365992,6.60523002714498));
#6930=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.679865365992,6.60523002714498));
#6931=CARTESIAN_POINT('Ctrl Pts',(-18.2362883123438,-53.679865365992,6.60263721562776));
#6932=CARTESIAN_POINT('Ctrl Pts',(-18.4601123887653,-53.679865365992,6.59771847568304));
#6933=CARTESIAN_POINT('Ctrl Pts',(-18.6749136626558,-53.679865365992,6.53510734738655));
#6934=CARTESIAN_POINT('Ctrl Pts',(-18.776486910036,-53.679865365992,6.50550037388947));
#6935=CARTESIAN_POINT('',(-18.118304103034,-53.179865365992,6.60523002714498));
#6936=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.179865365992,6.51429515793513));
#6937=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.679865365992,6.51429515793513));
#6938=CARTESIAN_POINT('Ctrl Pts',(-17.6040382419592,-53.179865365992,6.5402231907059));
#6939=CARTESIAN_POINT('Ctrl Pts',(-17.6040382419592,-53.679865365992,6.5402231907059));
#6940=CARTESIAN_POINT('Ctrl Pts',(-17.8004670622934,-53.179865365992,6.59796695527928));
#6941=CARTESIAN_POINT('Ctrl Pts',(-17.8004670622934,-53.679865365992,6.59796695527928));
#6942=CARTESIAN_POINT('Ctrl Pts',(-18.0053934854633,-53.179865365992,6.60264984351407));
#6943=CARTESIAN_POINT('Ctrl Pts',(-18.0053934854633,-53.679865365992,6.60264984351407));
#6944=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.179865365992,6.60523002714498));
#6945=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.679865365992,6.60523002714498));
#6946=CARTESIAN_POINT('',(-17.5158380193312,-53.679865365992,6.51429515793513));
#6947=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.679865365992,6.51429515793513));
#6948=CARTESIAN_POINT('Ctrl Pts',(-17.6040382419592,-53.679865365992,6.5402231907059));
#6949=CARTESIAN_POINT('Ctrl Pts',(-17.8004670622934,-53.679865365992,6.59796695527928));
#6950=CARTESIAN_POINT('Ctrl Pts',(-18.0053934854633,-53.679865365992,6.60264984351407));
#6951=CARTESIAN_POINT('Ctrl Pts',(-18.118304103034,-53.679865365992,6.60523002714498));
#6952=CARTESIAN_POINT('',(-17.5158380193312,-53.179865365992,6.51429515793513));
#6953=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.179865365992,6.29123983440859));
#6954=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.679865365992,6.29123983440859));
#6955=CARTESIAN_POINT('Ctrl Pts',(-17.220436388833,-53.179865365992,6.33524286655711));
#6956=CARTESIAN_POINT('Ctrl Pts',(-17.220436388833,-53.679865365992,6.33524286655711));
#6957=CARTESIAN_POINT('Ctrl Pts',(-17.318304057179,-53.179865365992,6.43239372538342));
#6958=CARTESIAN_POINT('Ctrl Pts',(-17.318304057179,-53.679865365992,6.43239372538342));
#6959=CARTESIAN_POINT('Ctrl Pts',(-17.4459871038143,-53.179865365992,6.48533360629333));
#6960=CARTESIAN_POINT('Ctrl Pts',(-17.4459871038143,-53.679865365992,6.48533360629333));
#6961=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.179865365992,6.51429515793513));
#6962=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.679865365992,6.51429515793513));
#6963=CARTESIAN_POINT('',(-17.1761086885063,-53.679865365992,6.29123983440859));
#6964=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.679865365992,6.29123983440859));
#6965=CARTESIAN_POINT('Ctrl Pts',(-17.220436388833,-53.679865365992,6.33524286655711));
#6966=CARTESIAN_POINT('Ctrl Pts',(-17.318304057179,-53.679865365992,6.43239372538342));
#6967=CARTESIAN_POINT('Ctrl Pts',(-17.4459871038143,-53.679865365992,6.48533360629333));
#6968=CARTESIAN_POINT('Ctrl Pts',(-17.5158380193312,-53.679865365992,6.51429515793513));
#6969=CARTESIAN_POINT('',(-17.1761086885063,-53.179865365992,6.29123983440859));
#6970=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.179865365992,5.95744901920609));
#6971=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.679865365992,5.95744901920609));
#6972=CARTESIAN_POINT('Ctrl Pts',(-17.0412256178987,-53.179865365992,6.02067183602851));
#6973=CARTESIAN_POINT('Ctrl Pts',(-17.0412256178987,-53.679865365992,6.02067183602851));
#6974=CARTESIAN_POINT('Ctrl Pts',(-17.0715322927043,-53.179865365992,6.1410985307247));
#6975=CARTESIAN_POINT('Ctrl Pts',(-17.0715322927043,-53.679865365992,6.1410985307247));
#6976=CARTESIAN_POINT('Ctrl Pts',(-17.1424310659699,-53.179865365992,6.24288855863731));
#6977=CARTESIAN_POINT('Ctrl Pts',(-17.1424310659699,-53.679865365992,6.24288855863731));
#6978=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.179865365992,6.29123983440859));
#6979=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.679865365992,6.29123983440859));
#6980=CARTESIAN_POINT('',(-17.0253149150884,-53.679865365992,5.95744901920609));
#6981=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.679865365992,5.95744901920609));
#6982=CARTESIAN_POINT('Ctrl Pts',(-17.0412256178987,-53.679865365992,6.02067183602851));
#6983=CARTESIAN_POINT('Ctrl Pts',(-17.0715322927043,-53.679865365992,6.1410985307247));
#6984=CARTESIAN_POINT('Ctrl Pts',(-17.1424310659699,-53.679865365992,6.24288855863731));
#6985=CARTESIAN_POINT('Ctrl Pts',(-17.1761086885063,-53.679865365992,6.29123983440859));
#6986=CARTESIAN_POINT('',(-17.0253149150884,-53.179865365992,5.95744901920609));
#6987=CARTESIAN_POINT('Ctrl Pts',(-17.0034809294599,-53.179865365992,5.50437630510761));
#6988=CARTESIAN_POINT('Ctrl Pts',(-17.0034809294599,-53.679865365992,5.50437630510761));
#6989=CARTESIAN_POINT('Ctrl Pts',(-17.0026638140218,-53.179865365992,5.59683383612124));
#6990=CARTESIAN_POINT('Ctrl Pts',(-17.0026638140218,-53.679865365992,5.59683383612124));
#6991=CARTESIAN_POINT('Ctrl Pts',(-17.0013247399637,-53.179865365992,5.74835157216759));
#6992=CARTESIAN_POINT('Ctrl Pts',(-17.0013247399637,-53.679865365992,5.74835157216759));
#6993=CARTESIAN_POINT('Ctrl Pts',(-17.0185864578987,-53.179865365992,5.89880404418814));
#6994=CARTESIAN_POINT('Ctrl Pts',(-17.0185864578987,-53.679865365992,5.89880404418814));
#6995=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.179865365992,5.95744901920609));
#6996=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.679865365992,5.95744901920609));
#6997=CARTESIAN_POINT('',(-17.0034809294599,-53.679865365992,5.50437630510761));
#6998=CARTESIAN_POINT('Ctrl Pts',(-17.0034809294599,-53.679865365992,5.50437630510761));
#6999=CARTESIAN_POINT('Ctrl Pts',(-17.0026638140218,-53.679865365992,5.59683383612124));
#7000=CARTESIAN_POINT('Ctrl Pts',(-17.0013247399637,-53.679865365992,5.74835157216759));
#7001=CARTESIAN_POINT('Ctrl Pts',(-17.0185864578987,-53.679865365992,5.89880404418814));
#7002=CARTESIAN_POINT('Ctrl Pts',(-17.0253149150884,-53.679865365992,5.95744901920609));
#7003=CARTESIAN_POINT('',(-17.0034809294599,-53.179865365992,5.50437630510761));
#7004=CARTESIAN_POINT('Origin',(-17.0074164761489,-53.179865365992,4.84954527780918));
#7005=CARTESIAN_POINT('',(-17.0074164761489,-53.679865365992,4.84954527780918));
#7006=CARTESIAN_POINT('',(-17.0034809294599,-53.679865365992,5.50437630510761));
#7007=CARTESIAN_POINT('',(-17.0074164761489,-53.179865365992,4.84954527780918));
#7008=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.179865365992,3.98171337204134));
#7009=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.679865365992,3.98171337204134));
#7010=CARTESIAN_POINT('Ctrl Pts',(-16.9905933255679,-53.179865365992,4.08449013198545));
#7011=CARTESIAN_POINT('Ctrl Pts',(-16.9905933255679,-53.679865365992,4.08449013198545));
#7012=CARTESIAN_POINT('Ctrl Pts',(-17.0129992185845,-53.179865365992,4.37324209466735));
#7013=CARTESIAN_POINT('Ctrl Pts',(-17.0129992185845,-53.679865365992,4.37324209466735));
#7014=CARTESIAN_POINT('Ctrl Pts',(-17.0096035253256,-53.179865365992,4.66295266735441));
#7015=CARTESIAN_POINT('Ctrl Pts',(-17.0096035253256,-53.679865365992,4.66295266735441));
#7016=CARTESIAN_POINT('Ctrl Pts',(-17.0074164761489,-53.179865365992,4.84954527780918));
#7017=CARTESIAN_POINT('Ctrl Pts',(-17.0074164761489,-53.679865365992,4.84954527780918));
#7018=CARTESIAN_POINT('',(-16.98261829722,-53.679865365992,3.98171337204134));
#7019=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.679865365992,3.98171337204134));
#7020=CARTESIAN_POINT('Ctrl Pts',(-16.9905933255679,-53.679865365992,4.08449013198545));
#7021=CARTESIAN_POINT('Ctrl Pts',(-17.0129992185845,-53.679865365992,4.37324209466735));
#7022=CARTESIAN_POINT('Ctrl Pts',(-17.0096035253256,-53.679865365992,4.66295266735441));
#7023=CARTESIAN_POINT('Ctrl Pts',(-17.0074164761489,-53.679865365992,4.84954527780918));
#7024=CARTESIAN_POINT('',(-16.98261829722,-53.179865365992,3.98171337204134));
#7025=CARTESIAN_POINT('Ctrl Pts',(-16.8591912401334,-53.179865365992,3.63444417580955));
#7026=CARTESIAN_POINT('Ctrl Pts',(-16.8591912401334,-53.679865365992,3.63444417580955));
#7027=CARTESIAN_POINT('Ctrl Pts',(-16.887220354007,-53.179865365992,3.69034759716629));
#7028=CARTESIAN_POINT('Ctrl Pts',(-16.887220354007,-53.679865365992,3.69034759716629));
#7029=CARTESIAN_POINT('Ctrl Pts',(-16.9427091394009,-53.179865365992,3.80101870023571));
#7030=CARTESIAN_POINT('Ctrl Pts',(-16.9427091394009,-53.679865365992,3.80101870023571));
#7031=CARTESIAN_POINT('Ctrl Pts',(-16.9694065702617,-53.179865365992,3.92189530528231));
#7032=CARTESIAN_POINT('Ctrl Pts',(-16.9694065702617,-53.679865365992,3.92189530528231));
#7033=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.179865365992,3.98171337204134));
#7034=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.679865365992,3.98171337204134));
#7035=CARTESIAN_POINT('',(-16.8591912401334,-53.679865365992,3.63444417580955));
#7036=CARTESIAN_POINT('Ctrl Pts',(-16.8591912401334,-53.679865365992,3.63444417580955));
#7037=CARTESIAN_POINT('Ctrl Pts',(-16.887220354007,-53.679865365992,3.69034759716629));
#7038=CARTESIAN_POINT('Ctrl Pts',(-16.9427091394009,-53.679865365992,3.80101870023571));
#7039=CARTESIAN_POINT('Ctrl Pts',(-16.9694065702617,-53.679865365992,3.92189530528231));
#7040=CARTESIAN_POINT('Ctrl Pts',(-16.98261829722,-53.679865365992,3.98171337204134));
#7041=CARTESIAN_POINT('',(-16.8591912401334,-53.179865365992,3.63444417580955));
#7042=CARTESIAN_POINT('Origin',(-17.3721425294028,-53.179865365992,3.63752702262646));
#7043=CARTESIAN_POINT('',(-17.3721425294028,-53.679865365992,3.63752702262646));
#7044=CARTESIAN_POINT('',(-16.8591912401334,-53.679865365992,3.63444417580955));
#7045=CARTESIAN_POINT('',(-17.3721425294028,-53.179865365992,3.63752702262646));
#7046=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.179865365992,3.995546246547));
#7047=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.679865365992,3.995546246547));
#7048=CARTESIAN_POINT('Ctrl Pts',(-17.4597135255632,-53.179865365992,3.93078285381297));
#7049=CARTESIAN_POINT('Ctrl Pts',(-17.4597135255632,-53.679865365992,3.93078285381297));
#7050=CARTESIAN_POINT('Ctrl Pts',(-17.4434740943284,-53.179865365992,3.80713453991937));
#7051=CARTESIAN_POINT('Ctrl Pts',(-17.4434740943284,-53.679865365992,3.80713453991937));
#7052=CARTESIAN_POINT('Ctrl Pts',(-17.3951539748287,-53.179865365992,3.69224212851364));
#7053=CARTESIAN_POINT('Ctrl Pts',(-17.3951539748287,-53.679865365992,3.69224212851364));
#7054=CARTESIAN_POINT('Ctrl Pts',(-17.3721425294028,-53.179865365992,3.63752702262646));
#7055=CARTESIAN_POINT('Ctrl Pts',(-17.3721425294028,-53.679865365992,3.63752702262646));
#7056=CARTESIAN_POINT('Ctrl Pts',(-17.4682192676127,-53.679865365992,3.995546246547));
#7057=CARTESIAN_POINT('Ctrl Pts',(-17.4597135255632,-53.679865365992,3.93078285381297));
#7058=CARTESIAN_POINT('Ctrl Pts',(-17.4434740943284,-53.679865365992,3.80713453991937));
#7059=CARTESIAN_POINT('Ctrl Pts',(-17.3951539748287,-53.679865365992,3.69224212851364));
#7060=CARTESIAN_POINT('Ctrl Pts',(-17.3721425294028,-53.679865365992,3.63752702262646));
#7061=CARTESIAN_POINT('Origin',(-13.4911611848099,-53.679865365992,5.09371901156747));
#7062=CARTESIAN_POINT('Origin',(-54.2053113423059,-53.179865365992,0.));
#7063=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,10.));
#7064=CARTESIAN_POINT('',(-3.48031134230594,60.070134634008,10.));
#7065=CARTESIAN_POINT('',(-1.48031134230594,62.070134634008,10.));
#7066=CARTESIAN_POINT('Origin',(-1.48031134230594,60.070134634008,10.));
#7067=CARTESIAN_POINT('',(-3.48031134230594,52.570134634008,10.));
#7068=CARTESIAN_POINT('',(-3.48031134230594,33.2576346340079,10.));
#7069=CARTESIAN_POINT('',(18.2076886576941,52.570134634008,10.));
#7070=CARTESIAN_POINT('',(-46.9553113423059,52.570134634008,10.));
#7071=CARTESIAN_POINT('',(18.2076886576941,-43.679865365992,10.));
#7072=CARTESIAN_POINT('',(18.2076886576941,54.820134634008,10.));
#7073=CARTESIAN_POINT('',(-44.7053113423059,-43.679865365992,10.));
#7074=CARTESIAN_POINT('',(-15.4988113423059,-43.679865365992,10.));
#7075=CARTESIAN_POINT('',(-44.7053113423059,52.570134634008,10.));
#7076=CARTESIAN_POINT('',(-44.7053113423059,6.69513463400796,10.));
#7077=CARTESIAN_POINT('',(-21.9803113423059,52.570134634008,10.));
#7078=CARTESIAN_POINT('',(-46.9553113423059,52.570134634008,10.));
#7079=CARTESIAN_POINT('',(-21.9803113423059,60.070134634008,10.));
#7080=CARTESIAN_POINT('',(-21.9803113423059,28.507634634008,10.));
#7081=CARTESIAN_POINT('',(-23.9803113423059,62.070134634008,10.));
#7082=CARTESIAN_POINT('Origin',(-23.9803113423059,60.070134634008,10.));
#7083=CARTESIAN_POINT('',(-49.2053113423059,62.070134634008,10.));
#7084=CARTESIAN_POINT('',(-10.7488113423059,62.070134634008,10.));
#7085=CARTESIAN_POINT('',(-54.2053113423059,57.070134634008,10.));
#7086=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,10.));
#7087=CARTESIAN_POINT('',(-54.2053113423059,-48.179865365992,10.));
#7088=CARTESIAN_POINT('',(-54.2053113423059,59.570134634008,10.));
#7089=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,10.));
#7090=CARTESIAN_POINT('',(27.7076886576941,-48.179865365992,10.));
#7091=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,10.));
#7092=CARTESIAN_POINT('',(27.7076886576941,57.070134634008,10.));
#7093=CARTESIAN_POINT('',(27.7076886576941,1.94513463400796,10.));
#7094=CARTESIAN_POINT('',(22.7076886576941,62.070134634008,10.));
#7095=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,10.));
#7096=CARTESIAN_POINT('',(-10.7488113423059,62.070134634008,10.));
#7097=CARTESIAN_POINT('',(-50.9053113423059,57.070134634008,10.));
#7098=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,10.));
#7099=CARTESIAN_POINT('',(21.0076886576941,-48.179865365992,10.));
#7100=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,10.));
#7101=CARTESIAN_POINT('',(-50.9053113423059,-48.179865365992,10.));
#7102=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,10.));
#7103=CARTESIAN_POINT('',(21.0076886576941,57.070134634008,10.));
#7104=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,10.));
#7105=CARTESIAN_POINT('Origin',(-44.7053113423059,52.570134634008,0.));
#7106=CARTESIAN_POINT('',(-44.7053113423059,52.570134634008,8.));
#7107=CARTESIAN_POINT('',(-44.7053113423059,52.570134634008,0.));
#7108=CARTESIAN_POINT('',(-19.9803113423059,52.570134634008,8.));
#7109=CARTESIAN_POINT('',(-28.9770613423059,52.570134634008,8.));
#7110=CARTESIAN_POINT('Origin',(-19.9803113423059,52.570134634008,10.));
#7111=CARTESIAN_POINT('Origin',(-44.7053113423059,-43.679865365992,0.));
#7112=CARTESIAN_POINT('',(-44.7053113423059,-43.679865365992,8.));
#7113=CARTESIAN_POINT('',(-44.7053113423059,-43.679865365992,0.));
#7114=CARTESIAN_POINT('',(-44.7053113423059,-19.617365365992,8.));
#7115=CARTESIAN_POINT('Origin',(18.2076886576941,-43.679865365992,0.));
#7116=CARTESIAN_POINT('',(18.2076886576941,-43.679865365992,8.));
#7117=CARTESIAN_POINT('',(18.2076886576941,-43.679865365992,0.));
#7118=CARTESIAN_POINT('',(2.47943865769406,-43.679865365992,8.));
#7119=CARTESIAN_POINT('Origin',(18.2076886576941,52.570134634008,0.));
#7120=CARTESIAN_POINT('',(18.2076886576941,52.570134634008,8.));
#7121=CARTESIAN_POINT('',(18.2076886576941,52.570134634008,0.));
#7122=CARTESIAN_POINT('',(18.2076886576941,28.507634634008,8.));
#7123=CARTESIAN_POINT('Origin',(-44.7053113423059,52.570134634008,0.));
#7124=CARTESIAN_POINT('',(-5.48031134230594,52.570134634008,8.));
#7125=CARTESIAN_POINT('Origin',(-5.48031134230594,52.570134634008,10.));
#7126=CARTESIAN_POINT('',(-28.9770613423059,52.570134634008,8.));
#7127=CARTESIAN_POINT('Origin',(-19.9803113423059,60.070134634008,10.));
#7128=CARTESIAN_POINT('',(-19.9803113423059,60.070134634008,8.));
#7129=CARTESIAN_POINT('',(-19.9803113423059,62.070134634008,6.));
#7130=CARTESIAN_POINT('Origin',(-19.9803113423059,60.070134634008,6.));
#7131=CARTESIAN_POINT('Origin',(-19.9803113423059,62.070134634008,10.));
#7132=CARTESIAN_POINT('Origin',(-19.9803113423059,60.070134634008,10.));
#7133=CARTESIAN_POINT('Origin',(7.22943865769406,60.070134634008,6.));
#7134=CARTESIAN_POINT('',(-5.48031134230594,60.070134634008,8.));
#7135=CARTESIAN_POINT('',(-5.48031134230594,62.070134634008,6.));
#7136=CARTESIAN_POINT('Origin',(-5.48031134230594,60.070134634008,6.));
#7137=CARTESIAN_POINT('',(7.22943865769406,62.070134634008,6.));
#7138=CARTESIAN_POINT('',(7.22943865769406,60.070134634008,8.));
#7139=CARTESIAN_POINT('Origin',(-5.48031134230594,60.070134634008,10.));
#7140=CARTESIAN_POINT('Origin',(-5.48031134230594,62.070134634008,10.));
#7141=CARTESIAN_POINT('Origin',(-5.48031134230594,60.070134634008,10.));
#7142=CARTESIAN_POINT('Origin',(-5.48031134230594,33.2576346340079,10.));
#7143=CARTESIAN_POINT('',(-5.48031134230594,33.2576346340079,8.));
#7144=CARTESIAN_POINT('Origin',(-13.2488113423059,4.44513463400796,8.));
#7145=CARTESIAN_POINT('',(-19.9803113423059,28.507634634008,8.));
#7146=CARTESIAN_POINT('Origin',(-19.9803113423059,28.507634634008,10.));
#7147=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,0.));
#7148=CARTESIAN_POINT('',(-54.2053113423059,-48.179865365992,0.));
#7149=CARTESIAN_POINT('',(-54.2053113423059,-48.179865365992,0.));
#7150=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,0.));
#7151=CARTESIAN_POINT('Origin',(-54.2053113423059,-53.179865365992,0.));
#7152=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,0.));
#7153=CARTESIAN_POINT('',(27.7076886576941,-48.179865365992,0.));
#7154=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,0.));
#7155=CARTESIAN_POINT('',(27.7076886576941,-48.179865365992,0.));
#7156=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,0.));
#7157=CARTESIAN_POINT('',(27.7076886576941,57.070134634008,0.));
#7158=CARTESIAN_POINT('',(27.7076886576941,57.070134634008,0.));
#7159=CARTESIAN_POINT('',(22.7076886576941,62.070134634008,0.));
#7160=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,0.));
#7161=CARTESIAN_POINT('',(22.7076886576941,62.070134634008,0.));
#7162=CARTESIAN_POINT('Origin',(27.7076886576941,62.070134634008,0.));
#7163=CARTESIAN_POINT('',(-49.2053113423059,62.070134634008,0.));
#7164=CARTESIAN_POINT('',(-54.2053113423059,62.070134634008,0.));
#7165=CARTESIAN_POINT('',(-49.2053113423059,62.070134634008,0.));
#7166=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,0.));
#7167=CARTESIAN_POINT('',(-54.2053113423059,57.070134634008,0.));
#7168=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,0.));
#7169=CARTESIAN_POINT('',(-54.2053113423059,57.070134634008,0.));
#7170=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,0.));
#7171=CARTESIAN_POINT('',(-52.2053113423059,57.070134634008,3.1));
#7172=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,3.1));
#7173=CARTESIAN_POINT('',(-52.2053113423059,57.070134634008,0.));
#7174=CARTESIAN_POINT('',(-52.2053113423059,57.070134634008,0.));
#7175=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,0.));
#7176=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,0.));
#7177=CARTESIAN_POINT('',(19.7076886576941,57.070134634008,3.1));
#7178=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,3.1));
#7179=CARTESIAN_POINT('',(19.7076886576941,57.070134634008,0.));
#7180=CARTESIAN_POINT('',(19.7076886576941,57.070134634008,0.));
#7181=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,0.));
#7182=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,0.));
#7183=CARTESIAN_POINT('',(19.7076886576941,-48.179865365992,3.1));
#7184=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,3.1));
#7185=CARTESIAN_POINT('',(19.7076886576941,-48.179865365992,0.));
#7186=CARTESIAN_POINT('',(19.7076886576941,-48.179865365992,0.));
#7187=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,0.));
#7188=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,0.));
#7189=CARTESIAN_POINT('',(-52.2053113423059,-48.179865365992,3.1));
#7190=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,3.1));
#7191=CARTESIAN_POINT('',(-52.2053113423059,-48.179865365992,0.));
#7192=CARTESIAN_POINT('',(-52.2053113423059,-48.179865365992,0.));
#7193=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,0.));
#7194=CARTESIAN_POINT('Origin',(-54.2053113423059,62.070134634008,0.));
#7195=CARTESIAN_POINT('',(-54.2053113423059,-53.179865365992,0.));
#7196=CARTESIAN_POINT('Origin',(27.7076886576941,-53.179865365992,0.));
#7197=CARTESIAN_POINT('',(27.7076886576941,62.070134634008,0.));
#7198=CARTESIAN_POINT('Origin',(-13.2488113423059,4.44513463400796,0.));
#7199=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,3.1));
#7200=CARTESIAN_POINT('',(-50.9053113423059,-48.179865365992,3.1));
#7201=CARTESIAN_POINT('',(-50.9053113423059,-48.179865365992,3.1));
#7202=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,3.1));
#7203=CARTESIAN_POINT('Origin',(-49.2053113423059,-48.179865365992,3.1));
#7204=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,3.1));
#7205=CARTESIAN_POINT('',(21.0076886576941,-48.179865365992,3.1));
#7206=CARTESIAN_POINT('',(21.0076886576941,-48.179865365992,3.1));
#7207=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,3.1));
#7208=CARTESIAN_POINT('Origin',(22.7076886576941,-48.179865365992,3.1));
#7209=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,3.1));
#7210=CARTESIAN_POINT('',(21.0076886576941,57.070134634008,3.1));
#7211=CARTESIAN_POINT('',(21.0076886576941,57.070134634008,3.1));
#7212=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,3.1));
#7213=CARTESIAN_POINT('Origin',(22.7076886576941,57.070134634008,3.1));
#7214=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,3.1));
#7215=CARTESIAN_POINT('',(-50.9053113423059,57.070134634008,3.1));
#7216=CARTESIAN_POINT('',(-50.9053113423059,57.070134634008,3.1));
#7217=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,3.1));
#7218=CARTESIAN_POINT('Origin',(-49.2053113423059,57.070134634008,3.1));
#7219=CARTESIAN_POINT('',(0.,0.,0.));
#7220=CARTESIAN_POINT('Origin',(0.,0.,0.));
#7221=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#7227,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#7222=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#7227,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#7223=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#7227,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#7224=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#7221))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#7227,#7229,#7230))
REPRESENTATION_CONTEXT('','3D')
);
#7225=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#7222))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#7227,#7229,#7230))
REPRESENTATION_CONTEXT('','3D')
);
#7226=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#7223))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#7227,#7229,#7230))
REPRESENTATION_CONTEXT('','3D')
);
#7227=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#7228=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#7229=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#7230=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#7231=SHAPE_DEFINITION_REPRESENTATION(#7233,#7236);
#7232=SHAPE_DEFINITION_REPRESENTATION(#7234,#7237);
#7233=PRODUCT_DEFINITION_SHAPE('',$,#7239);
#7234=PRODUCT_DEFINITION_SHAPE('',$,#7240);
#7235=PRODUCT_DEFINITION_SHAPE($,$,#17);
#7236=SHAPE_REPRESENTATION('',(#3595,#3712),#7224);
#7237=SHAPE_REPRESENTATION('',(#3596),#7225);
#7238=PRODUCT_DEFINITION_CONTEXT('part definition',#7246,'design');
#7239=PRODUCT_DEFINITION('2025-05-31-10-49-38-677','Base Plate (1)',#7241,
#7238);
#7240=PRODUCT_DEFINITION('2025-05-31-10-51-18-276','Base Plate',#7242,#7238);
#7241=PRODUCT_DEFINITION_FORMATION('',$,#7248);
#7242=PRODUCT_DEFINITION_FORMATION('',$,#7249);
#7243=PRODUCT_RELATED_PRODUCT_CATEGORY('Base Plate (1)',
'Base Plate (1)',(#7248));
#7244=PRODUCT_RELATED_PRODUCT_CATEGORY('Base Plate','Base Plate',(#7249));
#7245=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#7246);
#7246=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#7247=PRODUCT_CONTEXT('part definition',#7246,'mechanical');
#7248=PRODUCT('2025-05-31-10-49-38-677','Base Plate (1)',$,(#7247));
#7249=PRODUCT('2025-05-31-10-51-18-276','Base Plate',$,(#7247));
#7250=PRESENTATION_STYLE_ASSIGNMENT((#7254));
#7251=PRESENTATION_STYLE_ASSIGNMENT((#7255));
#7252=PRESENTATION_STYLE_ASSIGNMENT((#7256));
#7253=PRESENTATION_STYLE_ASSIGNMENT((NULL_STYLE(.NULL.)));
#7254=SURFACE_STYLE_USAGE(.BOTH.,#7257);
#7255=SURFACE_STYLE_USAGE(.BOTH.,#7258);
#7256=SURFACE_STYLE_USAGE(.BOTH.,#7259);
#7257=SURFACE_SIDE_STYLE('',(#7260));
#7258=SURFACE_SIDE_STYLE('',(#7261));
#7259=SURFACE_SIDE_STYLE('',(#7262));
#7260=SURFACE_STYLE_FILL_AREA(#7263);
#7261=SURFACE_STYLE_FILL_AREA(#7264);
#7262=SURFACE_STYLE_FILL_AREA(#7265);
#7263=FILL_AREA_STYLE('ABS (White)',(#7266));
#7264=FILL_AREA_STYLE('Steel - Satin',(#7267));
#7265=FILL_AREA_STYLE('Plastic - Translucent Glossy (White)',(#7268));
#7266=FILL_AREA_STYLE_COLOUR('ABS (White)',#7269);
#7267=FILL_AREA_STYLE_COLOUR('Steel - Satin',#7270);
#7268=FILL_AREA_STYLE_COLOUR('Plastic - Translucent Glossy (White)',#7271);
#7269=COLOUR_RGB('ABS (White)',0.964705882352941,0.964705882352941,0.952941176470588);
#7270=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
#7271=COLOUR_RGB('Plastic - Translucent Glossy (White)',0.964705882352941,
0.964705882352941,0.952941176470588);
ENDSEC;
END-ISO-10303-21;
