(kicad_sch
	(version 20231120)
	(generator "eeschema")
	(generator_version "8.0")
	(uuid "713cf004-a426-41d5-ab9c-805d2b9459f5")
	(paper "A4")
	(lib_symbols
		(symbol "Connector:Conn_01x04_Pin"
			(pin_names
				(offset 1.016) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J"
				(at 0 5.08 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Conn_01x04_Pin"
				(at 0 -7.62 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connector, single row, 01x04, script generated"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_locked" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x04_Pin_1_1"
				(polyline
					(pts
						(xy 1.27 -5.08) (xy 0.8636 -5.08)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 -2.54) (xy 0.8636 -2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 0) (xy 0.8636 0)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 2.54) (xy 0.8636 2.54)
					)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start 0.8636 -4.953)
					(end 0 -5.207)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 -2.413)
					(end 0 -2.667)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(rectangle
					(start 0.8636 2.667)
					(end 0 2.413)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(pin passive line
					(at 5.08 2.54 180)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 0 180)
					(length 3.81)
					(name "Pin_2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -2.54 180)
					(length 3.81)
					(name "Pin_3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 5.08 -5.08 180)
					(length 3.81)
					(name "Pin_4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Device:RotaryEncoder_Switch"
			(pin_names
				(offset 0.254) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "SW"
				(at 0 6.604 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "RotaryEncoder_Switch"
				(at 0 -6.604 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at -3.81 4.064 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 6.604 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Rotary encoder, dual channel, incremental quadrate outputs, with switch"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "rotary switch encoder switch push button"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "RotaryEncoder*Switch*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "RotaryEncoder_Switch_0_1"
				(rectangle
					(start -5.08 5.08)
					(end 5.08 -5.08)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(circle
					(center -3.81 0)
					(radius 0.254)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type outline)
					)
				)
				(circle
					(center -0.381 0)
					(radius 1.905)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(arc
					(start -0.381 2.667)
					(mid -3.0988 -0.0635)
					(end -0.381 -2.794)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.635 -1.778) (xy -0.635 1.778)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.381 -1.778) (xy -0.381 1.778)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.127 1.778) (xy -0.127 -1.778)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.81 0) (xy 3.429 0)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 3.81 1.016) (xy 3.81 -1.016)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -5.08 -2.54) (xy -3.81 -2.54) (xy -3.81 -2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -5.08 2.54) (xy -3.81 2.54) (xy -3.81 2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 -3.048) (xy -0.508 -2.794) (xy 0.127 -2.413)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0.254 2.921) (xy -0.508 2.667) (xy 0.127 2.286)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 5.08 -2.54) (xy 4.318 -2.54) (xy 4.318 -1.016)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 5.08 2.54) (xy 4.318 2.54) (xy 4.318 1.016)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -5.08 0) (xy -3.81 0) (xy -3.81 -1.016) (xy -3.302 -2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -4.318 0) (xy -3.81 0) (xy -3.81 1.016) (xy -3.302 2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 4.318 -1.016)
					(radius 0.127)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 4.318 1.016)
					(radius 0.127)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "RotaryEncoder_Switch_1_1"
				(pin passive line
					(at -7.62 2.54 0)
					(length 2.54)
					(name "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -7.62 -2.54 0)
					(length 2.54)
					(name "B"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "B"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -7.62 0 0)
					(length 2.54)
					(name "C"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "C"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 7.62 2.54 180)
					(length 2.54)
					(name "S1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "S1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 7.62 -2.54 180)
					(length 2.54)
					(name "S2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "S2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "ScottoKeebs:Placeholder_Diode"
			(pin_numbers hide)
			(pin_names hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "D"
				(at 0 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "Diode"
				(at 0 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Device" "D"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Sim.Pins" "1=K 2=A"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "diode"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "D*DO?35*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Placeholder_Diode_0_1"
				(polyline
					(pts
						(xy -1.27 1.27) (xy -1.27 -1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 0) (xy -1.27 0)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.27 1.27) (xy 1.27 -1.27) (xy -1.27 0) (xy 1.27 1.27)
					)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "Placeholder_Diode_1_1"
				(pin passive line
					(at -3.81 0 0)
					(length 2.54)
					(name "K"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 3.81 0 180)
					(length 2.54)
					(name "A"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "ScottoKeebs:Placeholder_Keyswitch"
			(pin_numbers hide)
			(pin_names
				(offset 1.016) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "S"
				(at 3.048 1.016 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "Keyswitch"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Push button switch, normally open, two pins, 45° tilted"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "switch normally-open pushbutton push-button"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Placeholder_Keyswitch_0_1"
				(circle
					(center -1.1684 1.1684)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -0.508 2.54) (xy 2.54 -0.508)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.016 1.016) (xy 2.032 2.032)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.54 2.54) (xy -1.524 1.524) (xy -1.524 1.524)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 1.524 -1.524) (xy 2.54 -2.54) (xy 2.54 -2.54) (xy 2.54 -2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(circle
					(center 1.143 -1.1938)
					(radius 0.508)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at -2.54 2.54 0)
					(length 0)
					(name "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 2.54 -2.54 180)
					(length 0)
					(name "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "Seeed_Studio_XIAO_Series:XIAO-RP2040-DIP"
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "U"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "XIAO-RP2040-DIP"
				(at 5.334 -1.778 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" "Module:MOUDLE14P-XIAO-DIP-SMD"
				(at 14.478 -32.258 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "XIAO-RP2040-DIP_1_0"
				(polyline
					(pts
						(xy -1.27 -30.48) (xy -1.27 -16.51)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -27.94) (xy -2.54 -27.94)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -24.13) (xy -2.54 -24.13)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -20.32) (xy -2.54 -20.32)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -16.51) (xy -2.54 -16.51)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -16.51) (xy -1.27 -12.7)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -12.7) (xy -2.54 -12.7)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -12.7) (xy -1.27 -8.89)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -8.89) (xy -2.54 -8.89)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -8.89) (xy -1.27 -5.08)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -5.08) (xy -2.54 -5.08)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -5.08) (xy -1.27 -2.54)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -1.27 -2.54) (xy 29.21 -2.54)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 29.21 -30.48) (xy -1.27 -30.48)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 29.21 -12.7) (xy 29.21 -30.48)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 29.21 -8.89) (xy 29.21 -12.7)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 29.21 -5.08) (xy 29.21 -8.89)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 29.21 -2.54) (xy 29.21 -5.08)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 30.48 -27.94) (xy 29.21 -27.94)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 30.48 -24.13) (xy 29.21 -24.13)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 30.48 -20.32) (xy 29.21 -20.32)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 30.48 -16.51) (xy 29.21 -16.51)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 30.48 -12.7) (xy 29.21 -12.7)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 30.48 -8.89) (xy 29.21 -8.89)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 30.48 -5.08) (xy 29.21 -5.08)
					)
					(stroke
						(width 0.1524)
						(type solid)
					)
					(fill
						(type none)
					)
				)
				(pin passive line
					(at -3.81 -5.08 0)
					(length 2.54)
					(name "GPIO26/ADC0/A0"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 31.75 -20.32 180)
					(length 2.54)
					(name "GPIO4/MISO"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "10"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 31.75 -16.51 180)
					(length 2.54)
					(name "GPIO3/MOSI"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "11"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 31.75 -12.7 180)
					(length 2.54)
					(name "3V3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "12"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 31.75 -8.89 180)
					(length 2.54)
					(name "GND"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "13"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 31.75 -5.08 180)
					(length 2.54)
					(name "VBUS"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "14"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -3.81 -8.89 0)
					(length 2.54)
					(name "GPIO27/ADC1/A1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -3.81 -12.7 0)
					(length 2.54)
					(name "GPIO28/ADC2/A2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -3.81 -16.51 0)
					(length 2.54)
					(name "GPIO29/ADC3/A3"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "4"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -3.81 -20.32 0)
					(length 2.54)
					(name "GPIO6/SDA"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "5"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -3.81 -24.13 0)
					(length 2.54)
					(name "GPIO7/SCL"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "6"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at -3.81 -27.94 0)
					(length 2.54)
					(name "GPIO0/TX"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "7"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 31.75 -27.94 180)
					(length 2.54)
					(name "GPIO1/RX"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "8"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 31.75 -24.13 180)
					(length 2.54)
					(name "GPIO2/SCK"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "9"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:+3V3"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "+3V3"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"+3V3\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "+3V3_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "+3V3_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:+5V"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "+5V"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"+5V\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "+5V_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "+5V_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:GND"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
	)
	(junction
		(at 92.71 106.68)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "0e6611e2-243b-4402-b25a-b68523318940")
	)
	(junction
		(at 110.49 119.38)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "4a1c408e-cd5c-4d83-8039-9192217ee127")
	)
	(junction
		(at 92.71 86.36)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "7b6e813d-6a85-48d1-8bc5-b49ad5a35d61")
	)
	(junction
		(at 110.49 99.06)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "7bddf0f3-c1a0-40c9-a984-d2104cbec752")
	)
	(junction
		(at 110.49 139.7)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "8ffb6421-df17-4d5b-ba33-01278f7878b2")
	)
	(junction
		(at 124.46 99.06)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "95bae191-1450-47cc-a3f0-ea0fa8143261")
	)
	(junction
		(at 124.46 119.38)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "9ddff8bf-f2a6-46e8-b9ad-6b1de16356b3")
	)
	(junction
		(at 105.41 106.68)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "a6c8b9e1-7c70-460a-b31b-fe8f13a3fca4")
	)
	(junction
		(at 124.46 139.7)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "b840fea5-a43e-43d5-97d7-b6935bcb6df2")
	)
	(junction
		(at 119.38 86.36)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "c06dea01-8f65-4315-a97b-efd2b64fbe9a")
	)
	(junction
		(at 119.38 106.68)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "e91f7a20-5016-4cba-b5c5-a8c428abae55")
	)
	(junction
		(at 105.41 86.36)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "fa9f2b97-e0ac-45ee-9b67-93b1c0122be2")
	)
	(wire
		(pts
			(xy 110.49 139.7) (xy 124.46 139.7)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "01fecdb1-1dbc-4eed-b038-82be38a0e08d")
	)
	(wire
		(pts
			(xy 105.41 86.36) (xy 105.41 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "08ee9c95-8f07-46de-843f-26756a35264d")
	)
	(wire
		(pts
			(xy 105.41 78.74) (xy 105.41 86.36)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "13db87db-0795-494d-bce4-14a81eb52df4")
	)
	(wire
		(pts
			(xy 119.38 86.36) (xy 119.38 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "15f88c88-82d5-4138-ba48-909174da7f19")
	)
	(wire
		(pts
			(xy 105.41 106.68) (xy 105.41 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1da05788-b980-442c-a61b-faeb49209624")
	)
	(wire
		(pts
			(xy 133.35 139.7) (xy 124.46 139.7)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "1e5dfc58-9afd-4c19-8798-18ee3040c05b")
	)
	(wire
		(pts
			(xy 133.35 99.06) (xy 124.46 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "473ce1f6-3f47-4be3-9954-72e668b05fab")
	)
	(wire
		(pts
			(xy 97.79 139.7) (xy 110.49 139.7)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "5060e01e-2f06-471e-8aec-2fff2368bc0c")
	)
	(wire
		(pts
			(xy 110.49 99.06) (xy 124.46 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "560bab17-7c50-4778-bed3-50373b904afb")
	)
	(wire
		(pts
			(xy 97.79 99.06) (xy 110.49 99.06)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "58ae9542-fa9c-4fd7-ad29-1c9324ec3585")
	)
	(wire
		(pts
			(xy 92.71 86.36) (xy 92.71 106.68)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "668c72f2-d9e3-4e1a-b382-4f853b525b0e")
	)
	(wire
		(pts
			(xy 110.49 119.38) (xy 124.46 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "97ee39e6-5f56-4c34-8fc5-8bf2d1d51df6")
	)
	(wire
		(pts
			(xy 97.79 119.38) (xy 110.49 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d28638bd-ef1a-4336-b62f-31b83d00a85c")
	)
	(wire
		(pts
			(xy 119.38 106.68) (xy 119.38 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d73caa5c-6127-46ea-82fc-cc67bbee0344")
	)
	(wire
		(pts
			(xy 92.71 78.74) (xy 92.71 86.36)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "d8cba00e-50ed-4258-8398-c947823e1e21")
	)
	(wire
		(pts
			(xy 133.35 119.38) (xy 124.46 119.38)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e7e6842f-83bf-4a2c-8031-64577e19e3f6")
	)
	(wire
		(pts
			(xy 119.38 78.74) (xy 119.38 86.36)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f2be7cb1-0429-43d7-91ba-e5f0d13e26bc")
	)
	(wire
		(pts
			(xy 92.71 106.68) (xy 92.71 127)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "f821d55a-d8df-4f82-b6fc-c6ce73f5c7ce")
	)
	(global_label "Row 1"
		(shape input)
		(at 162.56 123.19 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "1878b448-f8c1-487e-8259-764591e94c90")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 153.6482 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Pot Analog 2"
		(shape input)
		(at 170.18 82.55 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "1a8ac329-14a2-4299-9c41-58e479f32e35")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 154.5556 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Column 0"
		(shape input)
		(at 198.12 111.76 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "45bab6ca-dbb3-4815-9605-4fcfed13bd37")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 210.3578 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Column 2"
		(shape input)
		(at 198.12 119.38 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "45e6b82d-9c2c-4130-ab8a-b555be53d500")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 210.3578 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Column 1"
		(shape input)
		(at 198.12 115.57 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "4c92291a-9e71-4f6a-837f-e37c50bacc41")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 210.3578 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Pot Button 1"
		(shape input)
		(at 185.42 82.55 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "5b069772-01d5-43aa-ba3a-1e73e18c4532")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 200.863 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Row 0"
		(shape input)
		(at 133.35 99.06 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "5e877364-5fb2-4514-bc84-df04c5b7891c")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 142.2618 99.06 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Column 0"
		(shape input)
		(at 92.71 78.74 90)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "764c4c68-b34f-46c8-9d2f-cabe26627263")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 92.71 66.5022 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "SCL Pin"
		(shape input)
		(at 220.98 82.55 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "78923b1e-2dac-4a90-ad42-5852b571381c")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 231.4642 82.55 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "SDA Pin"
		(shape input)
		(at 220.98 85.09 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "886be5eb-c480-4a2c-8bbc-d2077b7a64a4")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 231.5247 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "SCL Pin"
		(shape input)
		(at 162.56 119.38 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "a147f6aa-072a-43c7-943e-98fde8843424")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 152.0758 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Row 1"
		(shape input)
		(at 133.35 119.38 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "a24d45ae-6215-4551-9a7d-309d93cf512d")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 142.2618 119.38 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Row 2"
		(shape input)
		(at 198.12 123.19 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "a65b6489-9fee-4335-ae7d-d61e1403a916")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 207.0318 123.19 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Pot Analog 1"
		(shape input)
		(at 170.18 77.47 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "a7bd7674-0942-4a86-8213-d8fa2d774bef")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 154.5556 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Column 1"
		(shape input)
		(at 105.41 78.74 90)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "af9c4e69-5edc-4c62-87a4-3f0addbd8334")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 105.41 66.5022 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "SDA Pin"
		(shape input)
		(at 162.56 115.57 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "b6834abc-8e80-4895-a44e-d9d19ac85342")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 152.0153 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Column 2"
		(shape input)
		(at 119.38 78.74 90)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "c0df27f9-688e-4e22-a9f1-148ae98472ff")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 119.38 66.5022 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Row 2"
		(shape input)
		(at 133.35 139.7 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left)
		)
		(uuid "c19cb88e-bd45-41ac-af60-e09915721af6")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 142.2618 139.7 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
				(hide yes)
			)
		)
	)
	(global_label "Row 0"
		(shape input)
		(at 162.56 111.76 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "d0091ea5-b18a-459a-a3a2-b7e46832c3c0")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 153.6482 111.76 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Pot Analog 2"
		(shape input)
		(at 162.56 104.14 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "d03b5206-7c63-44e5-b408-776b14cd6f2d")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 146.9356 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Pot Analog 1"
		(shape input)
		(at 162.56 100.33 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "e82667d1-073a-4332-826c-a842b2d01022")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 146.9356 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(global_label "Pot Button 1"
		(shape input)
		(at 162.56 107.95 180)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify right)
		)
		(uuid "ffbfe8b3-2720-4f5e-9281-46e4c60e7582")
		(property "Intersheetrefs" "${INTERSHEET_REFS}"
			(at 147.117 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
				(hide yes)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 107.95 109.22 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "0bee618f-479e-49df-9d80-210443f4042d")
		(property "Reference" "S5"
			(at 107.95 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 107.95 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 107.95 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 107.95 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 107.95 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "7f1e038d-9aa2-4c14-8e3c-4288aa0c745d")
		)
		(pin "2"
			(uuid "6db04cc6-b904-476a-a352-658ac9372d7c")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 198.12 104.14 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "16a8892f-0d96-465f-8519-0ce2424e2818")
		(property "Reference" "#PWR03"
			(at 204.47 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 201.93 104.1399 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" ""
			(at 198.12 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 198.12 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 198.12 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3a2f65ce-c400-43ed-9cf6-da5a6d02fc96")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "#PWR03")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 121.92 129.54 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "29494c0a-547b-43ca-846c-c2048134e85a")
		(property "Reference" "S9"
			(at 121.92 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 121.92 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 121.92 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 121.92 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 121.92 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "316f7820-663c-465a-921f-8b66fb2db9cb")
		)
		(pin "2"
			(uuid "8a239e71-ac2e-4aea-8bc2-4ac37099d899")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 124.46 115.57 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "29cfae28-17af-4a62-8b76-03f12d9d9071")
		(property "Reference" "D6"
			(at 127 114.2999 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 127 116.8399 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 124.46 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 124.46 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 124.46 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 124.46 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 124.46 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "ea0b7f60-8298-475d-9aba-d3f11d36a22e")
		)
		(pin "1"
			(uuid "a5ac2602-6d52-4974-8d74-74350e293632")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 110.49 135.89 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "31fd3121-**************-4dcc14f365fc")
		(property "Reference" "D8"
			(at 113.03 134.6199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 113.03 137.1599 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 110.49 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 110.49 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 110.49 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 110.49 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 110.49 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "5ddce742-9b26-4946-8e76-6e978d2c9eac")
		)
		(pin "1"
			(uuid "748c168c-3984-4006-90ea-5ed0db0c5e51")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 124.46 95.25 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4ee5e023-a552-4c1e-8ba5-861e1e904ee9")
		(property "Reference" "D3"
			(at 127 93.9799 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 127 96.5199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 124.46 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 124.46 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 124.46 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 124.46 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 124.46 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "087dfee9-8683-4691-9ba9-a38cef79a61e")
		)
		(pin "1"
			(uuid "b3fd834d-681e-4c25-89d2-f54ed050c9e0")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 95.25 109.22 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4fe02e45-aa2c-45bc-a2e4-5ba3787591d1")
		(property "Reference" "S4"
			(at 95.25 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 95.25 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 95.25 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 95.25 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 95.25 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b73a522c-c42b-46cd-b03b-59d740864084")
		)
		(pin "2"
			(uuid "e1482134-e17d-48ce-b611-a8c24dacde15")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 121.92 109.22 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "56bbb86b-5260-40c7-a17b-d213c86206ac")
		(property "Reference" "S6"
			(at 121.92 101.6 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 121.92 104.14 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 121.92 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 121.92 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 121.92 109.22 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "0bf7c9a8-3335-48b4-b160-86eee658e832")
		)
		(pin "2"
			(uuid "6400b828-8e48-4a68-818d-d4abb7a030ae")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S6")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:+3V3")
		(at 198.12 107.95 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "57aa045b-d273-43de-89be-a20d6535e438")
		(property "Reference" "#PWR05"
			(at 194.31 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+3V3"
			(at 201.93 107.9499 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 198.12 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 198.12 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+3V3\""
			(at 198.12 107.95 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "552c4259-a995-4e79-bc1c-cea677b0a317")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "#PWR05")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 110.49 95.25 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "5881d372-512a-4f1f-a01f-21b864c70aa8")
		(property "Reference" "D2"
			(at 113.03 93.9799 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 113.03 96.5199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 110.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 110.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 110.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 110.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 110.49 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "e5a3e9e2-26a1-4b69-b636-86aba9d850ff")
		)
		(pin "1"
			(uuid "fd67fc93-1068-4b0a-a040-227fa6d767e5")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 124.46 135.89 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "58aae583-40d8-4a05-979b-ff8ca38258d7")
		(property "Reference" "D9"
			(at 127 134.6199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 127 137.1599 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 124.46 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 124.46 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 124.46 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 124.46 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 124.46 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "366d8b52-439b-4e13-b0f1-1ca604080a9d")
		)
		(pin "1"
			(uuid "5d437a81-6e81-461d-a283-dd0df77ed338")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 170.18 80.01 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "5d7eee81-b8f2-417d-a53e-6979d00a6174")
		(property "Reference" "#PWR02"
			(at 163.83 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 166.37 80.0099 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" ""
			(at 170.18 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 170.18 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 170.18 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "3292dff8-f203-4183-a56e-18b1fdc915c6")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:+5V")
		(at 198.12 100.33 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "7177a421-1c61-47b0-86d0-df3a591bb670")
		(property "Reference" "#PWR08"
			(at 194.31 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+5V"
			(at 201.93 100.3299 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 198.12 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 198.12 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+5V\""
			(at 198.12 100.33 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e2035dab-77fb-4d0d-871f-843e12e9cbee")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "#PWR08")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 121.92 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "8167d41a-af1f-48a7-9721-57ce4d01e168")
		(property "Reference" "S3"
			(at 121.92 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 121.92 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 121.92 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 121.92 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 121.92 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "63e20d56-dfbb-4cf4-9583-fc71ac68a3c8")
		)
		(pin "2"
			(uuid "4f9c923a-b8b6-4ef7-881a-3db239f33dca")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S3")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 110.49 115.57 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "85d4b6a1-7e0d-47f2-b1a6-db6c39073eb2")
		(property "Reference" "D5"
			(at 113.03 114.2999 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 113.03 116.8399 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 110.49 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 110.49 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 110.49 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 110.49 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 110.49 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "a044bc43-6b01-4f84-a8cb-393e260d7869")
		)
		(pin "1"
			(uuid "81a57949-7dcb-4b55-ae47-208211a16559")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D5")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 95.25 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "867ee365-f7b8-4eb6-8470-6dd058ea4d93")
		(property "Reference" "S1"
			(at 95.25 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 95.25 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 95.25 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 95.25 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 95.25 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d3495a8b-4f43-44ef-ad93-0a6271bd9e1d")
		)
		(pin "2"
			(uuid "a33e1806-03b1-4ded-9260-20dfcdc35dfb")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector:Conn_01x04_Pin")
		(at 215.9 80.01 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "98477ad6-c806-41e7-9237-062999929361")
		(property "Reference" "J1"
			(at 216.535 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Conn_01x04_Pin"
			(at 216.535 74.93 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "KiCad-SSD1306-0.91-OLED-4pin-128x32.pretty-master:SSD1306-0.91-OLED-4pin-128x32"
			(at 215.9 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 215.9 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x04, script generated"
			(at 215.9 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "3eced02a-dc8f-429a-9559-6d86e70aac59")
		)
		(pin "3"
			(uuid "de8f6ae2-a106-4082-942a-ef06943b07a1")
		)
		(pin "4"
			(uuid "846ef68d-7aa3-4cad-93d9-cbac2859f99b")
		)
		(pin "1"
			(uuid "52409ddb-3979-458c-b614-92246ff4cd0b")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "J1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:+3V3")
		(at 220.98 80.01 270)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "9df80999-d048-4d3f-a35c-75e211071e77")
		(property "Reference" "#PWR06"
			(at 217.17 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "+3V3"
			(at 224.79 80.0099 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 220.98 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 220.98 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"+3V3\""
			(at 220.98 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b386354f-7f58-4e0d-bde8-a338f47ddbce")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "#PWR06")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 97.79 135.89 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a3ad39dd-bbb1-44ff-bb1e-0534e398733d")
		(property "Reference" "D7"
			(at 100.33 134.6199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 100.33 137.1599 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 97.79 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 97.79 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 97.79 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 97.79 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 97.79 135.89 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "80947a79-b8f7-489c-be0e-9ceb647bd360")
		)
		(pin "1"
			(uuid "f64668ba-1196-45eb-bb7a-5d1d84365b6c")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Seeed_Studio_XIAO_Series:XIAO-RP2040-DIP")
		(at 166.37 95.25 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "b2f6dd05-5250-47f9-8e4d-4cf69106b262")
		(property "Reference" "U1"
			(at 180.34 92.71 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "XIAO-RP2040-DIP"
			(at 180.34 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Seeed Studio XIAO Series Library:XIAO-RP2040-DIP"
			(at 180.848 127.508 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 166.37 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" ""
			(at 166.37 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "0c67e0f8-0f58-457e-9b99-28c9a85c8c67")
		)
		(pin "7"
			(uuid "ee24bce4-9418-4e58-91a0-5545b6e21118")
		)
		(pin "14"
			(uuid "ff7e3109-6330-4a9a-a5f3-69c216f03e18")
		)
		(pin "6"
			(uuid "9b8a318c-a8bc-480e-b3d9-005834fce7cd")
		)
		(pin "10"
			(uuid "46123a72-ecfc-4a22-940a-b38aeb3a1ee3")
		)
		(pin "3"
			(uuid "7b9ab2f9-e2a7-4d53-b916-443f01930e1c")
		)
		(pin "8"
			(uuid "39faf83d-6970-4871-a42f-65eb0c48986e")
		)
		(pin "13"
			(uuid "e3480322-2e84-49b6-b6e0-76275b9d5881")
		)
		(pin "11"
			(uuid "8f11c1fd-7c79-4872-bcc5-e47773389a99")
		)
		(pin "12"
			(uuid "2c2a262f-47c6-4b5b-8112-10ad19197c17")
		)
		(pin "1"
			(uuid "d073cd88-f587-4ecb-a269-4491d17c13d2")
		)
		(pin "9"
			(uuid "1ddce50d-c4c7-403e-9ef3-19e7aefe5b82")
		)
		(pin "5"
			(uuid "387ddd8a-1e3c-450c-bd57-0a9c792284f1")
		)
		(pin "4"
			(uuid "7100574b-d5b7-406a-aba5-2530dc173a68")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "U1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 220.98 77.47 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "b61b8a5c-d159-409e-8059-03a6aab3246f")
		(property "Reference" "#PWR04"
			(at 227.33 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 224.79 77.4699 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" ""
			(at 220.98 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 220.98 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 220.98 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "354f7b72-4d42-4ddc-87b7-28ea99a05a22")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "#PWR04")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 95.25 129.54 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "bce886ea-5c38-44b5-ade2-0052da900907")
		(property "Reference" "S7"
			(at 95.25 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 95.25 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 95.25 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 95.25 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 95.25 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "19c9e4fb-2db2-43e4-97f1-d00f3aef8295")
		)
		(pin "2"
			(uuid "6fa4f26c-24db-417c-8b6e-25a2af214721")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 107.95 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c412cfbd-0b3f-4f30-b4a4-88ceda197004")
		(property "Reference" "S2"
			(at 107.95 81.28 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 107.95 83.82 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 107.95 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 107.95 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 107.95 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a0cdc068-db45-4c2c-b080-908e7f4aca71")
		)
		(pin "2"
			(uuid "f5f40693-4fe5-4d32-a1db-4afd04e08dc9")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 97.79 115.57 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "c9ed797a-3a94-4045-9056-b88ddbea3a11")
		(property "Reference" "D4"
			(at 100.33 114.2999 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 100.33 116.8399 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 97.79 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 97.79 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 97.79 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 97.79 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 97.79 115.57 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "6124d903-1eac-4b8d-a463-eaa9cc51f39e")
		)
		(pin "1"
			(uuid "1802f406-887a-408f-8e2b-c8bd73559538")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D4")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:RotaryEncoder_Switch")
		(at 177.8 80.01 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "cdac3806-1d75-4471-8e16-6b294a24d0bf")
		(property "Reference" "SW1"
			(at 177.8 69.85 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "RotaryEncoder_Switch"
			(at 177.8 72.39 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "Rotary_Encoder:RotaryEncoder_Alps_EC11E-Switch_Vertical_H20mm"
			(at 173.99 75.946 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 177.8 73.406 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Rotary encoder, dual channel, incremental quadrate outputs, with switch"
			(at 177.8 80.01 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "S2"
			(uuid "931f9401-1818-4f63-973b-0709ea805a58")
		)
		(pin "A"
			(uuid "e366f239-7f2f-4c61-ac61-984de793205e")
		)
		(pin "B"
			(uuid "0b65de5b-4c88-4fbd-a041-1162bec5c367")
		)
		(pin "C"
			(uuid "9e824a3c-e61a-4951-83e1-3faa88d05b7c")
		)
		(pin "S1"
			(uuid "449d377c-94f0-46c8-8ac3-f9801701e101")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "SW1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Keyswitch")
		(at 107.95 129.54 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d5c8e422-f987-4fa0-a8b3-42ca9788814a")
		(property "Reference" "S8"
			(at 107.95 121.92 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Value" "Keyswitch"
			(at 107.95 124.46 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" "ScottoKeebs_MX:MX_PCB_1.00u"
			(at 107.95 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 107.95 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Push button switch, normally open, two pins, 45° tilted"
			(at 107.95 129.54 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "4f557948-5488-4190-9c78-1a7def8c9b9e")
		)
		(pin "2"
			(uuid "bb3dcbbd-a0e5-4cac-ada9-219dee2f59cb")
		)
		(instances
			(project "ZapPad"
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "S8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "ScottoKeebs:Placeholder_Diode")
		(at 97.79 95.25 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "dc025a6b-4f34-45ff-97b8-7996adac8edd")
		(property "Reference" "D1"
			(at 100.33 93.9799 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Value" "Diode"
			(at 100.33 96.5199 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" "ScottoKeebs_Components:Diode_DO-35"
			(at 97.79 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 97.79 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "1N4148 (DO-35) or 1N4148W (SOD-123)"
			(at 97.79 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Device" "D"
			(at 97.79 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Sim.Pins" "1=K 2=A"
			(at 97.79 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "2"
			(uuid "f9ce4f93-9e8e-4955-8c25-8aa3c73015dc")
		)
		(pin "1"
			(uuid "4db2ffb2-01c6-4327-a9ef-63738eadeb32")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "D1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 185.42 77.47 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "f6a7bc26-25e5-49d8-a483-2e834842b66f")
		(property "Reference" "#PWR01"
			(at 191.77 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 189.23 77.4699 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify right)
			)
		)
		(property "Footprint" ""
			(at 185.42 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 185.42 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 185.42 77.47 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "f5c5d9ed-85e0-42be-8280-4205497c906d")
		)
		(instances
			(project ""
				(path "/713cf004-a426-41d5-ab9c-805d2b9459f5"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
)
